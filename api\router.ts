import { router, publicProcedure } from './trpc';
import { getAccessCatalog } from './permissions';
// import { partsRouter } from './routers/parts'; // Временно закомментировано из-за отсутствующих типов
import { schemaEditorRouter } from './routers/schema-editor';
import { adminRouter } from './routers/admin';
import { uploadRouter } from './routers/upload';
import { partAttributesRouter } from './routers/part-attributes';
import { attributeTemplatesRouter } from './routers/attribute-templates';
import { attributeSynonymsRouter } from './routers/attribute-synonyms';
import { matchingRouter } from './routers/matching';
import { searchRouter } from './routers/search';
import { createRouter as createCRUDRouter } from './generated/trpc/routers';

// Создаем автоматически сгенерированные CRUD роутеры
const crudRouter = createCRUDRouter();

export const appRouter = router({
  // Кастомные роутеры
  // parts: partsRouter, // Временно закомментировано
  schemaEditor: schemaEditorRouter,
  admin: adminRouter,
  upload: uploadRouter,
  partAttributes: partAttributesRouter,
  attributeTemplates: attributeTemplatesRouter,
  attributeSynonyms: attributeSynonymsRouter,
  matching: matchingRouter,
  search: searchRouter,

  // Автоматически сгенерированные CRUD роутеры для всех моделей
  crud: crudRouter,

  // вспомогательное: каталог ресурсов/действий для UI ACL
  access: router({
    list: publicProcedure.query(() => getAccessCatalog()),
  }),
});

export type AppRouter = typeof appRouter;
