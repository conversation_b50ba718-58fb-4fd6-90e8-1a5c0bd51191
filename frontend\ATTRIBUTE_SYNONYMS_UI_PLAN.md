## План реализации фронтенда: AttributeSynonymGroup / AttributeSynonym

Цель: дать администраторам удобные инструменты для создания и поддержки групп синонимов строковых атрибутов, чтобы алгоритм сопоставления `CatalogItem → Part` мог корректно учитывать эквивалентности и помечать совпадения с учетом `compatibilityLevel` и `notes`.

### 1) Размещение в UI
- В админке (раздел атрибутов):
  - В `AttributeTemplateManager.vue` — у каждой строки шаблона добавить действие «Синонимы».
  - В `TemplateForm.vue` — кнопка «Управление синонимами» рядом с настройками для STRING-атрибутов.
- Навигация открывает менеджер синонимов, привязанный к выбранному `AttributeTemplate`.

### 2) Сущности (фронтенд-модели)
- AttributeSynonymGroup: `{ id, templateId, name, description?, compatibilityLevel: 'EXACT'|'NEAR'|'LEGACY', notes? }`
- AttributeSynonym: `{ id, groupId, value }`

Примечание: редактор синонимов показывается только для шаблонов с `dataType = STRING`.

### 3) Компоненты
1. admin/attributes/AttributeSynonymManager.vue
   - Вход: `template` (целевой `AttributeTemplate`)
   - Отображает список групп для `templateId`, поиск, пагинацию, CRUD групп.
   - При выборе группы — правая панель для управления ее синонимами (CRUD значений).

2. admin/attributes/SynonymGroupList.vue
   - Таблица групп: `name`, `compatibilityLevel` (как Tag), `description`, `notes`, счетчик синонимов.
   - Действия: Edit, Delete, Select.

3. admin/attributes/EditSynonymGroupDialog.vue
   - Форма: `name` (обязательно), `description`, `compatibilityLevel` (EXACT/NEAR/LEGACY), `notes`.
   - Режимы: создание/редактирование.

4. admin/attributes/SynonymValueEditor.vue
   - Chips-редактор значений или таблица со строками: value + Delete.
   - Ввод с защитой от дублей и лишних пробелов.

5. Интеграция в TemplateForm.vue
   - Для `STRING` показать кнопку «Синонимы» → открывает `AttributeSynonymManager` по `templateId`.

6. Подсказки при вводе значений
   - В `AttributeValueInput.vue` (STRING): подсказка «значение входит в группу … (EXACT/NEAR/LEGACY)» при наличии матча (опционально в итерации 2).

### 4) tRPC-клиент (useTrpc)
Добавить секцию `attributeSynonyms`:
- `groups.findMany({ templateId, search?, limit?, offset? })`
- `groups.create({ templateId, name, description?, compatibilityLevel, notes? })`
- `groups.update({ id, name?, description?, compatibilityLevel?, notes? })`
- `groups.delete({ id })`
- `synonyms.findMany({ groupId })`
- `synonyms.create({ groupId, value })`
- `synonyms.delete({ id })`
- `utils.findGroupByValue({ templateId, value })` — быстрый поиск группы по значению для подсказок в формах (итерация 2).

Примечание: названия методов должны соответствовать серверным роутам, которые будут реализованы отдельно.

### 5) UX-потоки
- Создание группы:
  1) Открыть «Синонимы» у шаблона → «Создать группу».
  2) Заполнить `name`, выбрать `compatibilityLevel`, при необходимости `description` и `notes` → Сохранить.
- Управление синонимами:
  1) Выбрать группу → Добавлять значения (по одному, через Enter или кнопкой), удалять ненужные.
  2) Дубликаты и пустые значения не допускаются.
- Информирование о совместимости:
  - В списке групп и в подсказках показывать бейдж `compatibilityLevel`.

### 6) Валидация
- Group: `name` обязателен; длина до 100; уникальность в рамках одного `templateId` (ошибку бэкенда переводить в читаемое сообщение).
- Synonym: `value` обязателен; trim; запрет на дубликаты в группе.
- Только для STRING-шаблонов отображать UI.

### 7) Права
- Все операции доступны только ролям с правом ADMIN (UI кнопки скрывать/блокировать у остальных).

### 8) Визуальные компоненты
- Использовать уже принятые Volt-компоненты: `VCard`, `VButton`, `VDialog`, `VInputText`, `InputChips`, `VTag`, `VDataTable`, `VAutoComplete`.
- Иконки PrimeIcons: `pi-tags`, `pi-list`, `pi-plus`, `pi-pencil`, `pi-trash`.

### 9) Ошибки и уведомления
- Локальные тосты (`useToast`) для успеха/ошибок (создание/обновление/удаление).
- Пустые состояния (нет групп/синонимов) — дружелюбные плейсхолдеры.

### 10) Тест-кейсы (минимум)
- CRUD групп: создание с валидными и невалидными данными; редактирование; удаление (в т.ч. с сообщением if 409/конфликт по бекенду).
- CRUD синонимов: добавление, удаление, недопуск дублей, недопуск пустых значений.
- Встроенная кнопка из `TemplateForm.vue` корректно открывает менеджер и возвращается назад без потерь состояния.

### 11) Пошаговая реализация (итерации)
- Итерация 1 (MVP):
  - tRPC-клиент `attributeSynonyms` (findMany/create/update/delete для групп; findMany/create/delete для синонимов).
  - Компоненты: `AttributeSynonymManager`, `SynonymGroupList`, `EditSynonymGroupDialog`, `SynonymValueEditor`.
  - Интеграция в `AttributeTemplateManager` и `TemplateForm` (кнопки «Синонимы»).

- Итерация 2:
  - Подсказки в `AttributeValueInput.vue` (поиск группы по текущему вводу через `utils.findGroupByValue`).
  - Плашка уровня совместимости в местах отображения значений.

- Итерация 3:
  - Страница сравнения/подбора: подсветка совпадений с учетом `compatibilityLevel` и вывод `notes` в деталях.

### 12) Критерии готовности (DoD)
- Админ может создать/редактировать/удалить группу синонимов для любого STRING-шаблона, задавая `compatibilityLevel` и `notes`.
- Админ может управлять списком синонимов группы (добавлять/удалять значения, защита от дублей/пустых).
- Из `TemplateForm.vue` доступно управление синонимами выбранного шаблона.
- UI защищен по ролям, ошибки отображаются, пустые состояния обработаны.


