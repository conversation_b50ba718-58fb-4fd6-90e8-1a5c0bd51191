<template>
  <div class="p-6 max-w-md mx-auto">
    <Card class="mb-4">
      <template #title>
        <h2 class="text-2xl font-bold text-primary">Добро пожаловать в PartTec!</h2>
      </template>
      <template #content>
        <p class="text-surface-700 dark:text-surface-300 mb-4">
          Система управления каталогом взаимозаменяемых запчастей
        </p>
        
        <!-- Демонстрация темы -->
        <div class="mb-4 p-3 bg-surface-100 dark:bg-surface-200 rounded-md">
          <p class="text-sm text-surface-600 dark:text-surface-400 mb-2">
            Текущая тема: <span class="font-semibold">{{ themeName }}</span>
          </p>
          <p class="text-xs text-surface-500 dark:text-surface-500">
            Этот блок демонстрирует адаптивность к темам
          </p>
        </div>

        <div class="mb-4">
          <label for="name" class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Ваше имя:
          </label>
          <InputText 
            id="name"
            v-model="name" 
            placeholder="Введите ваше имя"
            class="w-full"
          />
        </div>
        <p v-if="name" class="text-surface-600 dark:text-surface-400 mb-4">
          Привет, {{ name }}! 👋
        </p>
        <Button 
          :label="buttonLabel" 
          @click="handleClick"
          class="w-full"
        />
        <p v-if="clicked" class="text-primary-600 dark:text-primary-400 mt-2 text-center">
          Кнопка была нажата {{ clickCount }} раз!
        </p>
      </template>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Button from '@/volt/Button.vue';
import Card from '@/volt/Card.vue';
import InputText from '@/volt/InputText.vue';
import { useTheme } from '@/composables/useTheme';

const name = ref('');
const clicked = ref(false);
const clickCount = ref(0);

// Используем composable темы
const { themeName } = useTheme();

const buttonLabel = computed(() => 
  clicked.value ? 'Нажать еще раз' : 'Нажми меня!'
);

const handleClick = () => {
  clicked.value = true;
  clickCount.value++;
};
</script>
