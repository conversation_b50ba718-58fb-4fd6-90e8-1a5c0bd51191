<template>
  <aside class="w-64 bg-surface-section shadow-sm border-r border-surface-border min-h-full">
    <div class="p-6">
      <!-- Основное -->
      <div class="mb-6">
        <h3 class="text-xs font-semibold text-surface-500 uppercase tracking-wider mb-3">Основное</h3>
        <div class="space-y-1">
          <SecondaryButton icon="pi pi-home" label="Дашборд" text class="w-full justify-start"
            :aria-current="isActiveExact('/admin') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActiveExact('/admin') }"
            @click="navigateTo('/admin')" />
        </div>
      </div>

      <!-- Каталог -->
      <div class="mb-6">
        <h3 class="text-xs font-semibold text-surface-500 uppercase tracking-wider mb-3">Каталог</h3>
        <div class="space-y-1">
          <SecondaryButton icon="pi pi-wrench" label="Запчасти" text class="w-full justify-start"
            :aria-current="isActive('/admin/parts') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/parts') }"
            @click="navigateTo('/admin/parts')" />
          <SecondaryButton icon="pi pi-box" label="Каталожные позиции" text class="w-full justify-start"
            :aria-current="isActive('/admin/catalogitems') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/catalogitems') }"
            @click="navigateTo('/admin/catalogitems')" />
          <SecondaryButton icon="pi pi-link" label="Предложения эквивалентов" text class="w-full justify-start"
            :aria-current="isActive('/admin/proposals') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/proposals') }"
            @click="navigateTo('/admin/proposals')" />
        </div>
      </div>

      <!-- Справочники -->
      <div class="mb-6">
        <h3 class="text-xs font-semibold text-surface-500 uppercase tracking-wider mb-3">Справочники</h3>
        <div class="space-y-1">
          <SecondaryButton icon="pi pi-sliders-h" label="Атрибуты" text class="w-full justify-start"
            :aria-current="isActive('/admin/attributes') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/attributes') }"
            @click="navigateTo('/admin/attributes')" />
          <SecondaryButton icon="pi pi-tags" label="Бренды" text class="w-full justify-start"
            :aria-current="isActive('/admin/brands') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/brands') }"
            @click="navigateTo('/admin/brands')" />
          <SecondaryButton icon="pi pi-sitemap" label="Категории" text class="w-full justify-start"
            :aria-current="isActive('/admin/categories') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/categories') }"
            @click="navigateTo('/admin/categories')" />
          <SecondaryButton icon="pi pi-cog" label="Модели техники" text class="w-full justify-start"
            :aria-current="isActive('/admin/equipment') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/equipment') }"
            @click="navigateTo('/admin/equipment')" />
        </div>
      </div>

      <!-- Система -->
      <div>
        <h3 class="text-xs font-semibold text-surface-500 uppercase tracking-wider mb-3">Система</h3>
        <div class="space-y-1">
          <SecondaryButton icon="pi pi-users" label="Пользователи" text class="w-full justify-start"
            :aria-current="isActive('/admin/users') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/users') }"
            @click="navigateTo('/admin/users')" />
          <SecondaryButton icon="pi pi-shield" label="Контроль доступа" text class="w-full justify-start"
            :aria-current="isActive('/admin/access-control') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/access-control') }"
            @click="navigateTo('/admin/access-control')" />
          <SecondaryButton icon="pi pi-code" label="Тест UI" text class="w-full justify-start"
            :aria-current="isActive('/admin/ui-demo') ? 'page' : undefined"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/ui-demo') }"
            @click="navigateTo('/admin/ui-demo')" />
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import { navigate } from 'astro:transitions/client'

// Получаем текущий путь
const currentPath = computed(() => {
  if (typeof window !== 'undefined') {
    return window.location.pathname
  }
  return ''
})

// Методы
const navigateTo = (path: string) => {
  // window.location.href = path
  navigate(path)
}

const isActive = (path: string): boolean => {
  return currentPath.value.startsWith(path)
}

const isActiveExact = (path: string): boolean => currentPath.value === path
</script>
