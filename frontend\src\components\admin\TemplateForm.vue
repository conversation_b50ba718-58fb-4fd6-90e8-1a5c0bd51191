<template>
  <div class="template-form">
    <div class="space-y-4">
      <!-- Основная информация -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Системное имя -->
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Системное имя *
          </label>
          <VInputText v-model="form.name" placeholder="inner_diameter" class="w-full"
            :class="{ 'p-invalid': errors.name }" />
          <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
          <small class="text-surface-500 dark:text-surface-400">
            Только строчные буквы, цифры и подчеркивания
          </small>
        </div>

        <!-- Отображаемое название -->
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Отображаемое название *
          </label>
          <VInputText v-model="form.title" placeholder="Внутренний диаметр" class="w-full"
            :class="{ 'p-invalid': errors.title }" />
          <small v-if="errors.title" class="p-error">{{ errors.title }}</small>
        </div>
      </div>

      <!-- Описание -->
      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Описание
        </label>
        <VTextarea v-model="form.description" placeholder="Подробное описание атрибута..." rows="3" class="w-full" />
      </div>

      <!-- Тип данных и единица измерения -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Тип данных -->
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Тип данных *
          </label>
          <VAutoComplete 
            v-model="selectedDataType" 
            :suggestions="filteredDataTypeOptions" 
            @complete="filterDataTypes"
            @dropdown-click="() => filterDataTypes({ query: '' })"
            option-label="label" 
            option-value="value"
            placeholder="Выберите тип" 
            :class="dataTypeClass" 
            dropdown
          />
          <small v-if="errors.dataType" class="p-error">{{ errors.dataType }}</small>
        </div>

        <!-- Единица измерения -->
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Единица измерения
          </label>
          <VAutoComplete 
            v-model="selectedUnit" 
            :suggestions="filteredUnitOptions" 
            @complete="filterUnits"
            @dropdown-click="() => filterUnits({ query: '' })"
            option-label="label" 
            option-value="value"
            placeholder="Выберите единицу" 
            class="w-full" 
            dropdown
            show-clear 
          />
        </div>
      </div>

      <!-- Группа -->
      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Группа
        </label>
        <div class="flex gap-2">
          <VAutoComplete
            v-model="selectedGroup"
            :suggestions="filteredGroups"
            @complete="searchGroups"
            @dropdown-click="() => searchGroups({ query: '' })"
            option-label="name"
            placeholder="Поиск группы..."
            class="flex-1"
            :class="{ 'p-invalid': errors.groupId }"
            dropdown
            dropdown-mode="current"
            show-clear
            @item-select="onGroupSelect"
            @clear="onGroupClear"
          />
          <VButton @click="showCreateGroupDialog = true" severity="secondary" outlined size="small" label="Создать новую группу">
            <template #icon>
              <Icon name="pi pi-plus" class="w-5 h-5" />
            </template>
          </VButton>
        </div>
        <small v-if="errors.groupId" class="p-error">{{ errors.groupId }}</small>
      </div>

      <!-- Валидация (для числовых типов) -->
      <div v-if="form.dataType === 'NUMBER'" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Минимальное значение -->
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Минимальное значение
          </label>
          <VInputNumber v-model="form.minValue" placeholder="0" class="w-full" :use-grouping="false" :min-fraction-digits="2" :max-fraction-digits="2" />
        </div>

        <!-- Максимальное значение -->
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Максимальное значение
          </label>
          <VInputNumber v-model="form.maxValue" placeholder="100" class="w-full" :use-grouping="false" :min-fraction-digits="2" :max-fraction-digits="2" />
        </div>
      </div>

      <!-- Допуск для числовых атрибутов (tolerance) -->
      <div v-if="form.dataType === 'NUMBER'">
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Допустимое отклонение (tolerance)
        </label>
        <VInputNumber 
          v-model="form.tolerance" 
          placeholder="0.1" 
          class="w-full" 
          :use-grouping="false" 
          :min-fraction-digits="1" 
          :max-fraction-digits="4"
          :min="0" 
        />
        <small class="text-surface-500 dark:text-surface-400">
          Допустимое отклонение при сопоставлении числовых значений. 
          Например: если эталон = 30.0 и допуск = 0.1, то значения от 29.9 до 30.1 будут считаться эквивалентными.
        </small>
      </div>

      <!-- Допустимые значения (для строковых типов) -->
      <div v-if="form.dataType === 'STRING'">
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Допустимые значения
        </label>
        <InputChips v-model="form.allowedValues" placeholder="Введите значение и нажмите Enter" class="w-full" />
        <small class="text-surface-500 dark:text-surface-400">
          Оставьте пустым для любых значений. Например: steel, aluminum, plastic
        </small>
        <div class="mt-3">
          <VButton severity="secondary" outlined label="Синонимы" @click="openSynonyms">
            <template #icon>
              <Icon name="pi pi-tags" class="w-5 h-5" />
            </template>
          </VButton>
          <small class="ml-2 text-surface-500">Доступно после сохранения шаблона</small>
        </div>
      </div>

      <!-- Дополнительные опции -->
      <div class="flex items-center gap-4">
        <VCheckbox v-model="form.isRequired" input-id="required" binary />
        <label for="required" class="text-sm text-surface-700 dark:text-surface-300">
          Обязательный атрибут
        </label>
      </div>
    </div>

    <!-- Кнопки -->
    <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-surface-200 dark:border-surface-700">
      <VButton label="Отмена" severity="secondary" @click="$emit('cancel')" />
      <VButton :label="isEditing ? 'Обновить' : 'Создать'" @click="save" :loading="loading" :disabled="!isValid" />
    </div>

    <!-- Диалог создания группы -->
    <VDialog v-model:visible="showCreateGroupDialog" modal header="Создать группу атрибутов"
      :style="{ width: '30rem' }">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Название группы *
          </label>
          <VInputText v-model="newGroupForm.name" placeholder="Размеры" class="w-full" />
        </div>

        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Описание
          </label>
          <VTextarea v-model="newGroupForm.description" placeholder="Описание группы..." rows="2" class="w-full" />
        </div>
      </div>

      <template #footer>
        <VButton label="Отмена" severity="secondary" @click="showCreateGroupDialog = false" />
        <VButton label="Создать" @click="createGroup" :loading="creatingGroup" :disabled="!newGroupForm.name" />
      </template>
    </VDialog>

    <!-- Диалог управления синонимами -->
    <VDialog v-model:visible="showSynonymsDialog" modal header="Синонимы значения" :style="{ width: '80rem' }" :breakpoints="{ '1199px': '90vw', '575px': '98vw' }">
      <AttributeSynonymManager v-if="form.id && form.dataType === 'STRING'" :template="{ id: form.id, dataType: form.dataType, title: form.title, name: form.name } as any" />
      <div v-else class="p-4 text-surface-500">Сохраните шаблон, чтобы управлять синонимами.</div>
    </VDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import { useToast } from '@/composables/useToast';
import { useAuth } from '@/composables/useAuth';
import Icon from '@/components/ui/Icon.vue';

// Импорт компонентов
import VInputText from '@/volt/InputText.vue';
import VTextarea from '@/volt/Textarea.vue';
import VInputNumber from '@/volt/InputNumber.vue';
import InputChips from '@/volt/InputChips.vue';
import VCheckbox from '@/volt/Checkbox.vue';
import VButton from '@/volt/Button.vue';
import VDialog from '@/volt/Dialog.vue';
import VAutoComplete from '@/volt/AutoComplete.vue';
import AttributeSynonymManager from './attributes/AttributeSynonymManager.vue';

// Props
interface Props {
  modelValue: any;
  groups: any[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any];
  'save': [value: any];
  'cancel': [];
  'group-created': [group: any];
}>();

// Composables
const { attributeTemplates } = useTrpc();
const toast = useToast();
const { userRole } = useAuth();

// Локальное состояние
const form = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const errors = ref<Record<string, string>>({});
const showCreateGroupDialog = ref(false);
const creatingGroup = ref(false);
const newGroupForm = ref({
  name: '',
  description: ''
});

// Диалог синонимов
const showSynonymsDialog = ref(false);

const openSynonyms = () => {
  if (!form.value.id) {
    toast.info('Сначала сохраните шаблон');
    return;
  }
  showSynonymsDialog.value = true;
};

// Автокомплит для групп
const selectedGroup = ref<any>(null);
const filteredGroups = ref<any[]>([]);
const searchTimeout = ref<NodeJS.Timeout | null>(null);

// Опции
const dataTypeOptions = [
  { label: 'Строка', value: 'STRING' },
  { label: 'Число', value: 'NUMBER' },
  { label: 'Логическое', value: 'BOOLEAN' },
  { label: 'Дата', value: 'DATE' },
  { label: 'JSON', value: 'JSON' }
];

const unitOptions = [
  // Длина
  { label: 'мм', value: 'MM' },
  { label: 'дюймы', value: 'INCH' },
  { label: 'футы', value: 'FT' },
  // Вес
  { label: 'г', value: 'G' },
  { label: 'кг', value: 'KG' },
  { label: 'т', value: 'T' },
  { label: 'фунты', value: 'LB' },
  // Объем
  { label: 'мл', value: 'ML' },
  { label: 'л', value: 'L' },
  { label: 'галлоны', value: 'GAL' },
  // Количество
  { label: 'шт', value: 'PCS' },
  { label: 'комплект', value: 'SET' },
  { label: 'пара', value: 'PAIR' },
  // Давление
  { label: 'бар', value: 'BAR' },
  { label: 'PSI', value: 'PSI' },
  // Мощность
  { label: 'кВт', value: 'KW' },
  { label: 'л.с.', value: 'HP' },
  // Крутящий момент
  { label: 'Н⋅м', value: 'NM' },
  { label: 'об/мин', value: 'RPM' },
  // Температура
  { label: '°C', value: 'C' },
  { label: '°F', value: 'F' },
  // Относительные
  { label: '%', value: 'PERCENT' }
];

// Автокомплит для типов данных и единиц (инициализируются после объявления опций)
const filteredDataTypeOptions = ref(dataTypeOptions);
const filteredUnitOptions = ref(unitOptions);

// Computed property для VAutoComplete типа данных
const selectedDataType = computed({
  get: () => {
    // Находим объект в dataTypeOptions по значению в form.dataType
    return dataTypeOptions.find(opt => opt.value === form.value.dataType) || null;
  },
  set: (newValue) => {
    // Обновляем form.dataType значением из выбранного объекта
    form.value.dataType = newValue ? newValue.value : null;
  }
});

// Computed property для VAutoComplete единиц измерения
const selectedUnit = computed({
  get: () => {
    // Находим объект в unitOptions по значению в form.unit
    return unitOptions.find(opt => opt.value === form.value.unit) || null;
  },
  set: (newValue) => {
    // Обновляем form.unit значением из выбранного объекта
    form.value.unit = newValue ? newValue.value : null;
  }
});

// Фильтрация типов данных
const filterDataTypes = (event: any) => {
  console.log('filterDataTypes вызван в TemplateForm', { event });
  const query = event.query?.toLowerCase() || '';
  if (!query.trim()) {
    filteredDataTypeOptions.value = [...dataTypeOptions];
    console.log('Показываем все типы данных:', filteredDataTypeOptions.value.length);
  } else {
    filteredDataTypeOptions.value = dataTypeOptions.filter(option =>
      option.label.toLowerCase().includes(query)
    );
    console.log('Фильтруем типы данных:', filteredDataTypeOptions.value.length);
  }
};

// Фильтрация единиц измерения
const filterUnits = (event: any) => {
  console.log('filterUnits вызван в TemplateForm', { event });
  const query = event.query?.toLowerCase() || '';
  if (!query.trim()) {
    filteredUnitOptions.value = [...unitOptions];
    console.log('Показываем все единицы:', filteredUnitOptions.value.length);
  } else {
    filteredUnitOptions.value = unitOptions.filter(option =>
      option.label.toLowerCase().includes(query)
    );
    console.log('Фильтруем единицы:', filteredUnitOptions.value.length);
  }
};

// Находим выбранную группу по ID
const findSelectedGroup = () => {
  if (form.value.groupId) {
    selectedGroup.value = props.groups.find(g => g.id === form.value.groupId) || null;
  }
};

// Вычисляемые свойства
const isEditing = computed(() => !!form.value.id);

const isValid = computed(() => {
  return form.value.name && 
         form.value.title && 
         form.value.dataType &&
         !Object.keys(errors.value).length;
});

const dataTypeClass = computed(() => {
  return `w-full ${errors.value.dataType ? 'p-invalid' : ''}`;
});

// Валидация
const validateForm = () => {
  errors.value = {};
  
  if (!form.value.name) {
    errors.value.name = 'Системное имя обязательно';
  } else if (!/^[a-z0-9_]+$/.test(form.value.name)) {
    errors.value.name = 'Только строчные буквы, цифры и подчеркивания';
  }
  
  if (!form.value.title) {
    errors.value.title = 'Отображаемое название обязательно';
  }
  
  if (!form.value.dataType) {
    errors.value.dataType = 'Тип данных обязателен';
  }
};

// Функции
const save = () => {
  validateForm();
  
  if (isValid.value) {
    emit('save', form.value);
  } else {
    toast.error('Пожалуйста, исправьте ошибки в форме');
  }
};

// Функции автокомплита
const searchGroups = async (event: any) => {
  console.log('searchGroups вызван в TemplateForm', { event, groupsLength: props.groups.length });
  const query = event.query || '';
  
  // Очищаем предыдущий таймаут
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
  
  // Если запрос пустой, показываем все группы
  if (!query.trim()) {
    filteredGroups.value = [...props.groups];
    console.log('Показываем все группы:', filteredGroups.value.length);
    return;
  }
  
  // Debounce поиск через 300ms
  searchTimeout.value = setTimeout(async () => {
    try {
      // Пока используем локальный поиск, пока tRPC клиент не обновится
      filteredGroups.value = props.groups.filter(group => 
        group.name.toLowerCase().includes(query.toLowerCase()) ||
        (group.description && group.description.toLowerCase().includes(query.toLowerCase()))
      );
      
      // TODO: Включить API поиск после обновления tRPC клиента
      // const result = await attributeTemplates.searchGroups({
      //   query: query.trim(),
      //   limit: 20
      // });
      // 
      // if (result && Array.isArray(result)) {
      //   filteredGroups.value = result;
      // } else {
      //   filteredGroups.value = [];
      // }
    } catch (error: any) {
      console.error('Ошибка поиска групп:', error);
      toast.error('Не удалось выполнить поиск групп');
      // В случае ошибки показываем локальный поиск
      filteredGroups.value = props.groups.filter(group => 
        group.name.toLowerCase().includes(query.toLowerCase()) ||
        (group.description && group.description.toLowerCase().includes(query.toLowerCase()))
      );
    }
  }, 300);
};

const onGroupSelect = (event: any) => {
  form.value.groupId = event.value.id;
  selectedGroup.value = event.value;
  toast.info(`Выбрана группа: ${event.value.name}`);
};

const onGroupClear = () => {
  form.value.groupId = null;
  selectedGroup.value = null;
  toast.info('Группа сброшена');
};

const createGroup = async () => {
  if (!newGroupForm.value.name) {
    toast.error('Введите название группы');
    return;
  }
  
  try {
    creatingGroup.value = true;
    
    // Показываем тост о начале создания
    toast.info('Создание группы...');
    
    const result = await attributeTemplates.createGroup(newGroupForm.value);
    
    if (result && typeof result === 'object' && 'id' in result) {
      // Глобальный успех уже обработан в useTrpc
      
      // Устанавливаем новую группу как выбранную
      form.value.groupId = result.id;
      selectedGroup.value = result;
      
      // Закрываем диалог и очищаем форму
      showCreateGroupDialog.value = false;
      newGroupForm.value = { name: '', description: '' };
      
      // Эмитим событие для обновления списка групп в родительском компоненте
      emit('group-created', result);
    }
  } catch (error: any) {
    console.error('Ошибка создания группы:', error);
    
    // Показываем более подробную ошибку
    if (error.message?.includes('уже существует')) {
      toast.error('Группа с таким названием уже существует');
    } else {
      toast.error(error.message || 'Не удалось создать группу');
    }
  } finally {
    creatingGroup.value = false;
  }
};

// Инициализация формы
watch(() => props.modelValue, (newValue) => {
  // Убеждаемся, что у нас есть объект для работы
  if (!newValue) {
    form.value = {
      dataType: 'STRING',
      isRequired: false,
      allowedValues: []
    };
    return;
  }
  
  if (!newValue.dataType) {
    form.value.dataType = 'STRING';
  }
  if (!newValue.isRequired) {
    form.value.isRequired = false;
  }
  if (!newValue.allowedValues) {
    form.value.allowedValues = [];
  }
  
  // Инициализируем выбранную группу
  findSelectedGroup();
}, { immediate: true });

// Инициализируем автокомплит при изменении групп
watch(() => props.groups, () => {
  filteredGroups.value = props.groups;
  findSelectedGroup();
}, { immediate: true });

// Предзагружаем все группы при монтировании компонента
onMounted(() => {
  console.log('TemplateForm монтируется');
  
  // Инициализируем все автокомплиты
  filteredDataTypeOptions.value = [...dataTypeOptions];
  filteredUnitOptions.value = [...unitOptions]; 
  filteredGroups.value = [...props.groups];
  
  console.log('Инициализированы автокомплиты в TemplateForm:', {
    dataTypes: filteredDataTypeOptions.value.length,
    units: filteredUnitOptions.value.length,
    groups: filteredGroups.value.length
  });
  
  findSelectedGroup();
});

// Очищаем таймауты при размонтировании
onUnmounted(() => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
});

// Очистка валидации при изменении полей
watch(() => form.value.name, () => {
  if (errors.value.name) {
    delete errors.value.name;
  }
});

watch(() => form.value.title, () => {
  if (errors.value.title) {
    delete errors.value.title;
  }
});

watch(() => form.value.dataType, () => {
  if (errors.value.dataType) {
    delete errors.value.dataType;
  }
});

</script>
