<template>
  <div class="attribute-template-manager">
    <!-- Индикатор загрузки -->
    <div v-if="loading" class="text-center py-12">
      <Icon name="pi pi-spinner pi-spin" class="inline-block text-4xl text-primary mb-4" />
      <p class="text-surface-600 dark:text-surface-400">Загрузка шаблонов атрибутов...</p>
    </div>

    <!-- Основной контент -->
    <div v-else>
      <!-- Заголовок и действия -->
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
            Шаблоны атрибутов
          </h2>
          <p class="text-surface-600 dark:text-surface-400 text-sm mt-1">
            Управление шаблонами атрибутов для запчастей, каталожных позиций и техники
          </p>
        </div>

        <div class="flex gap-3">
          <VButton @click="refreshData" :disabled="loading" severity="secondary" outlined label="Обновить">
            <template #icon>
              <Icon name="pi pi-refresh" class="w-5 h-5" />
            </template>
          </VButton>

          <VButton @click="createNewTemplate" label="Создать шаблон">
            <template #icon>
              <Icon name="pi pi-plus" class="w-5 h-5" />
            </template>
          </VButton>
        </div>
      </div>

      <!-- Фильтры и поиск -->
      <VCard class="mb-6">
        <template #content>
          <div class="p-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <!-- Поиск -->
              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Поиск
                </label>
                <VInputText v-model="searchQuery" placeholder="Поиск по названию, имени или описанию..." class="w-full"
                  @input="debouncedSearch" />
              </div>

              <!-- Группа -->
              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Группа
                </label>
                <VAutoComplete v-model="selectedGroup" :suggestions="groupSuggestions" @complete="filterGroups"
                  option-label="name" option-value="id" placeholder="Все группы" class="w-full" 
                  dropdown show-clear @change="loadTemplates" />
              </div>

              <!-- Тип данных -->
              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Тип данных
                </label>
                <VAutoComplete v-model="selectedDataType" :suggestions="dataTypeSuggestions" @complete="filterDataTypes"
                  option-label="label" option-value="value" placeholder="Все типы" class="w-full" 
                  dropdown show-clear @change="loadTemplates" />
              </div>

              <!-- Режим отображения -->
            </div>
          </div>
        </template>
      </VCard>

      <!-- Статистика -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="text-2xl font-bold text-primary mb-2">{{ totalCount }}</div>
              <div class="text-sm text-surface-600 dark:text-surface-400">Всего шаблонов</div>
            </div>
          </template>
        </VCard>

        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="text-2xl font-bold text-green-600 mb-2">{{ groups.length }}</div>
              <div class="text-sm text-surface-600 dark:text-surface-400">Групп</div>
            </div>
          </template>
        </VCard>

        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="text-2xl font-bold text-blue-600 mb-2">{{ usedTemplatesCount }}</div>
              <div class="text-sm text-surface-600 dark:text-surface-400">Используется</div>
            </div>
          </template>
        </VCard>

        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="text-2xl font-bold text-orange-600 mb-2">{{ unusedTemplatesCount }}</div>
              <div class="text-sm text-surface-600 dark:text-surface-400">Не используется</div>
            </div>
          </template>
        </VCard>
      </div>

      <!-- Табличный режим -->
      <VCard v-if="viewMode === 'table'">
        <template #content>
          <VDataTable :value="templates" :loading="tableLoading && templates.length === 0" paginator :rows="pageSize" :total-records="totalCount"
            :rows-per-page-options="[10, 25, 50]" lazy @page="onPageChange" table-style="min-width: 50rem"
            class="p-datatable-sm" striped-rows>
            <!-- ID -->
            <Column field="id" header="ID" sortable style="width: 80px">
              <template #body="{ data }">
                <span class="font-mono text-sm text-surface-700 dark:text-surface-300">#{{ data.id }}</span>
              </template>
            </Column>

            <!-- Название -->
            <Column field="title" header="Название" sortable>
              <template #body="{ data }">
                <div>
                  <div class="font-medium text-surface-900 dark:text-surface-0">
                    {{ data.title }}
                  </div>
                  <div class="text-sm text-surface-600 dark:text-surface-400 font-mono">
                    {{ data.name }}
                  </div>
                </div>
              </template>
            </Column>

            <!-- Группа -->
            <Column field="group.name" header="Группа" sortable>
              <template #body="{ data }">
                <VTag v-if="data.group" :value="data.group.name" severity="secondary" />
                <span v-else class="text-surface-400 dark:text-surface-600">—</span>
              </template>
            </Column>

            <!-- Тип данных -->
            <Column field="dataType" header="Тип" sortable>
              <template #body="{ data }">
                <VTag :value="getDataTypeLabel(data.dataType)" severity="info" />
              </template>
            </Column>

            <!-- Единица измерения -->
            <Column field="unit" header="Единица">
              <template #body="{ data }">
                <VTag v-if="data.unit" :value="getUnitLabel(data.unit)" severity="success" />
                <span v-else class="text-surface-400 dark:text-surface-600">—</span>
              </template>
            </Column>

            <!-- Использование -->
            <Column header="Использование" style="width: 120px">
              <template #body="{ data }">
                <div class="text-sm">
                  <div v-if="data._count">
                    <div class="text-surface-700 dark:text-surface-300">
                      {{ getTotalUsage(data._count) }} исп.
                    </div>
                    <div class="text-xs text-surface-500 dark:text-surface-400">
                      {{ getUsageDetails(data._count) }}
                    </div>
                  </div>
                  <span v-else class="text-surface-400 dark:text-surface-600">—</span>
                </div>
              </template>
            </Column>

            <!-- Действия -->
            <Column header="Действия" style="width: 120px">
              <template #body="{ data }">
                <div class="flex gap-2">
                  <VButton v-if="data.dataType === 'STRING'" @click="openSynonyms(data)" severity="secondary" outlined size="small" label="Синонимы">
                    <template #icon>
                      <Icon name="pi pi-tags" class="w-5 h-5" />
                    </template>
                  </VButton>
                  <VButton @click="editTemplate(data)" severity="secondary" outlined size="small" label="Редактировать">
                    <template #icon>
                      <Icon name="pi pi-pencil" class="w-5 h-5" />
                    </template>
                  </VButton>
                  <VButton @click="deleteTemplate(data)" severity="danger" outlined size="small" label="Удалить" :disabled="getTotalUsage(data._count) > 0">
                    <template #icon>
                      <Icon name="pi pi-trash" class="w-5 h-5" />
                    </template>
                  </VButton>
                </div>
              </template>
            </Column>
          </VDataTable>
        </template>
      </VCard>

      <!-- Карточный режим -->
      <div v-else-if="viewMode === 'cards'" class="grid gap-4">
        <VCard v-for="template in templates" :key="template.id"
          class="border border-surface-200 dark:border-surface-700 hover:border-primary transition-colors">
          <template #content>
            <div class="p-6">
              <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <Icon name="pi pi-tag" class="text-blue-600 w-5 h-5" />
                    <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                      {{ template.title }}
                    </h3>
                    <VTag v-if="template.isRequired" value="Обязательный" severity="danger" size="small" />
                  </div>
                  <div class="text-sm text-surface-600 dark:text-surface-400 font-mono mb-2">
                    {{ template.name }}
                  </div>
                  <p v-if="template.description" class="text-surface-600 dark:text-surface-400 mb-3">
                    {{ template.description }}
                  </p>
                </div>

                <div class="flex gap-2 ml-4">
                  <VButton @click="editTemplate(template)" severity="secondary" outlined size="small" label="Редактировать">
                    <template #icon>
                      <Icon name="pi pi-pencil" class="w-5 h-5" />
                    </template>
                  </VButton>
                  <VButton @click="deleteTemplate(template)" severity="danger" outlined size="small" label="Удалить" :disabled="getTotalUsage(template._count) > 0">
                    <template #icon>
                      <Icon name="pi pi-trash" class="w-5 h-5" />
                    </template>
                  </VButton>
                </div>
              </div>

              <div class="flex items-center gap-4 mb-4">
                <VTag :value="getDataTypeLabel(template.dataType)" severity="info" />
                <VTag v-if="template.unit" :value="getUnitLabel(template.unit)" severity="success" />
                <VTag v-if="template.group" :value="template.group.name" severity="secondary" />
              </div>

              <!-- Статистика использования -->
              <div v-if="template._count" class="border-t border-surface-200 dark:border-surface-700 pt-4">
                <div class="text-sm text-surface-600 dark:text-surface-400 mb-2">
                  Использование:
                </div>
                <div class="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                      {{ template._count.partAttributes || 0 }}
                    </div>
                    <div class="text-xs text-surface-500">Запчасти</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                      {{ template._count.catalogItemAttributes || 0 }}
                    </div>
                    <div class="text-xs text-surface-500">Каталог</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                      {{ template._count.equipmentAttributes || 0 }}
                    </div>
                    <div class="text-xs text-surface-500">Техника</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </VCard>

        <!-- Пагинация для карточного режима -->
        <VCard v-if="totalCount > pageSize">
          <template #content>
            <div class="p-4">
              <Paginator :rows="pageSize" :total-records="totalCount" :rows-per-page-options="[10, 25, 50]"
                @page="onPageChange" />
            </div>
          </template>
        </VCard>
      </div>

      <!-- Диалог создания/редактирования шаблона -->
      <VDialog v-model:visible="showCreateDialog" modal
        :header="editingTemplate ? 'Редактировать шаблон' : 'Создать шаблон'" :style="{ width: '50rem' }"
        :breakpoints="{ '1199px': '75vw', '575px': '90vw' }">
        <TemplateForm v-model="templateForm" :groups="groups" :loading="saving" @save="saveTemplate"
          @cancel="showCreateDialog = false" />
      </VDialog>

      <VDialog v-model:visible="showSynonymsDialog" modal header="Управление синонимами" :style="{ width: '80rem' }"
        :breakpoints="{ '1199px': '90vw', '575px': '98vw' }">
        <AttributeSynonymManager v-if="selectedTemplateForSynonyms" :template="selectedTemplateForSynonyms" />
      </VDialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import { useUrlParams } from '@/composables/useUrlParams';

// Импорт компонентов
import VCard from '@/volt/Card.vue';
import VButton from '@/volt/Button.vue';
import VInputText from '@/volt/InputText.vue';
// VDropdown removed - replaced with VAutoComplete
import VDataTable from '@/volt/DataTable.vue';
import VTag from '@/volt/Tag.vue';
import VDialog from '@/volt/Dialog.vue';
import Column from 'primevue/column';
import Paginator from 'primevue/paginator';
import TemplateForm from '../TemplateForm.vue';
import VAutoComplete from '@/volt/AutoComplete.vue';
import AttributeSynonymManager from './AttributeSynonymManager.vue';
import Icon from '@/components/ui/Icon.vue';

// Composables
const { attributeTemplates, loading } = useTrpc();

// Состояние компонента
const templates = ref<any[]>([]);
const groups = ref<any[]>([]);
const totalCount = ref(0);
const pageSize = ref(25);
const currentPage = ref(0);

// Локальный индикатор загрузки только для таблицы,
// чтобы не задействовать глобальный `loading` tRPC и избежать "моргания".
const tableLoading = ref(false);

// Счётчик для защиты от гонки запросов
let lastRequestId = 0;

// Фильтры
const searchQuery = ref('');
const selectedGroup = ref<number | null>(null);
const selectedDataType = ref<string | null>(null);
const viewMode = ref('table');

// Синхронизация фильтров с URL (постепенная миграция на единый механизм)
const urlSync = useUrlParams({
  search: '',
  groupId: undefined as number | undefined,
  dataType: undefined as string | undefined,
}, {
  prefix: 'attr_',
  numberParams: ['groupId'],
  debounceMs: 300,
});

// Поддерживаем URL в актуальном состоянии при изменении локальных фильтров
watch(searchQuery, (val) => {
  urlSync.updateFilter('search', val || undefined)
});
watch(selectedGroup, (val) => {
  const gid = val && typeof val === 'object' ? (val as any).id : val;
  urlSync.updateFilter('groupId', gid ?? undefined)
});
watch(selectedDataType, (val) => {
  const dt = val && typeof val === 'object' ? (val as any).value : val;
  urlSync.updateFilter('dataType', dt ?? undefined)
});

// Реагируем на изменения URL (назад/вперёд) и применяем к локальному стейту
watch(urlSync.filters, (f) => {
  const nextSearch = (f as any).search || ''
  const nextGroupId = (f as any).groupId ?? null
  const nextDataType = (f as any).dataType ?? null

  if (searchQuery.value !== nextSearch) searchQuery.value = nextSearch
  if (selectedGroup.value !== nextGroupId) selectedGroup.value = nextGroupId
  if (selectedDataType.value !== nextDataType) selectedDataType.value = nextDataType

  currentPage.value = 0
  loadTemplates()
});

// Диалоги
const showCreateDialog = ref(false);
const editingTemplate = ref<any>(null);
const templateForm = ref<any>({});
const saving = ref(false);

// Синонимы
const showSynonymsDialog = ref(false);
const selectedTemplateForSynonyms = ref<any | null>(null);

// Опции для фильтров
const dataTypeOptions = [
  { label: 'Строка', value: 'STRING' },
  { label: 'Число', value: 'NUMBER' },
  { label: 'Логическое', value: 'BOOLEAN' },
  { label: 'Дата', value: 'DATE' },
  { label: 'JSON', value: 'JSON' }
];

const viewModeOptions = [
  { label: 'Таблица', value: 'table' },
  { label: 'Карточки', value: 'cards' }
];

// Данные для автокомплита
const groupSuggestions = ref<any[]>([]);
const dataTypeSuggestions = ref<any[]>([]);
const viewModeSuggestions = ref<any[]>([]);

// Фильтрация для автокомплита
const filterGroups = (event: any) => {
  const query = event.query?.toLowerCase() || '';
  if (!query.trim()) {
    groupSuggestions.value = [...groups.value];
  } else {
    groupSuggestions.value = groups.value.filter(group =>
      group.name.toLowerCase().includes(query)
    );
  }
};

const filterDataTypes = (event: any) => {
  const query = event.query?.toLowerCase() || '';
  if (!query.trim()) {
    dataTypeSuggestions.value = [...dataTypeOptions];
  } else {
    dataTypeSuggestions.value = dataTypeOptions.filter(option =>
      option.label.toLowerCase().includes(query)
    );
  }
};

const filterViewModes = (event: any) => {
  const query = event.query?.toLowerCase() || '';
  if (!query.trim()) {
    viewModeSuggestions.value = [...viewModeOptions];
  } else {
    viewModeSuggestions.value = viewModeOptions.filter(option =>
      option.label.toLowerCase().includes(query)
    );
  }
};

// Инициализация автокомплитов встроена в loadGroups и onMounted

// Вычисляемые свойства
const usedTemplatesCount = computed(() => {
  return templates.value.filter(template => getTotalUsage(template._count) > 0).length;
});

const unusedTemplatesCount = computed(() => {
  return templates.value.filter(template => getTotalUsage(template._count) === 0).length;
});

// Вспомогательные функции
const getDataTypeLabel = (dataType: string) => {
  const option = dataTypeOptions.find(opt => opt.value === dataType);
  return option?.label || dataType;
};

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    'MM': 'мм', 'INCH': 'дюймы', 'FT': 'футы',
    'G': 'г', 'KG': 'кг', 'T': 'т', 'LB': 'фунты',
    'ML': 'мл', 'L': 'л', 'GAL': 'галлоны',
    'PCS': 'шт', 'SET': 'комплект', 'PAIR': 'пара',
    'BAR': 'бар', 'PSI': 'PSI',
    'KW': 'кВт', 'HP': 'л.с.',
    'NM': 'Н⋅м', 'RPM': 'об/мин',
    'C': '°C', 'F': '°F',
    'PERCENT': '%'
  };
  return labels[unit] || unit;
};

const getTotalUsage = (count: any) => {
  if (!count) return 0;
  return (count.partAttributes || 0) + (count.catalogItemAttributes || 0) + (count.equipmentAttributes || 0);
};

const getUsageDetails = (count: any) => {
  if (!count) return '';
  const parts = count.partAttributes || 0;
  const items = count.catalogItemAttributes || 0;
  const equipment = count.equipmentAttributes || 0;

  const details = [];
  if (parts > 0) details.push(`${parts} зап.`);
  if (items > 0) details.push(`${items} кат.`);
  if (equipment > 0) details.push(`${equipment} тех.`);

  return details.join(', ');
};

// Основные функции
const loadTemplates = async () => {
  const current = ++lastRequestId;
  tableLoading.value = true;
  try {
    // Извлекаем ID из выбранной группы
    const groupId = selectedGroup.value 
      ? (typeof selectedGroup.value === 'object' ? (selectedGroup.value as any).id : selectedGroup.value)
      : undefined;
      
    // Извлекаем значение из выбранного типа данных
    const dataType = selectedDataType.value && typeof selectedDataType.value === 'object'
      ? (selectedDataType.value as any).value
      : undefined;
      
    const result = await attributeTemplates.findMany({
      groupId: groupId,
      search: searchQuery.value || undefined,
      dataType: dataType,
      limit: pageSize.value,
      offset: currentPage.value * pageSize.value
    });

    if (current === lastRequestId && result && typeof result === 'object') {
      templates.value = (result as any).templates || [];
      totalCount.value = (result as any).total || 0;
    }
  } catch (error) {
    console.error('Ошибка загрузки шаблонов:', error);
    console.error('Не удалось загрузить шаблоны');
  } finally {
    if (current === lastRequestId) {
      tableLoading.value = false;
    }
  }
};

const loadGroups = async () => {
  try {
    const result = await attributeTemplates.findAllGroups();
    if (result && Array.isArray(result)) {
      groups.value = result;
      // Обновляем данные для автокомплита групп после загрузки
      groupSuggestions.value = [...result];
    }
  } catch (error) {
    console.error('Ошибка загрузки групп:', error);
  }
};

// Debounced search
let searchTimeout: NodeJS.Timeout;
const debouncedSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    currentPage.value = 0;
    // Обновляем URL и перезагружаем данные
    urlSync.updateFilter('search', searchQuery.value || undefined)
    loadTemplates();
  }, 500);
};

const refreshData = async () => {
  await loadGroups(); // Обновляем группы (это также обновит groupSuggestions)
  await loadTemplates();
};

// Обработчики событий
const onPageChange = (event: any) => {
  currentPage.value = event.page;
  pageSize.value = event.rows;
  loadTemplates();
};

const editTemplate = (template: any) => {
  editingTemplate.value = template;
  templateForm.value = { ...template };
  showCreateDialog.value = true;
};

const openSynonyms = (template: any) => {
  selectedTemplateForSynonyms.value = template;
  showSynonymsDialog.value = true;
};

const createNewTemplate = () => {
  editingTemplate.value = null;
  templateForm.value = {
    dataType: 'STRING',
    isRequired: false,
    allowedValues: []
  };
  showCreateDialog.value = true;
};

const deleteTemplate = async (template: any) => {
  const confirmed = window.confirm(`Вы уверены, что хотите удалить шаблон "${template.title}"?`);

  if (confirmed) {
    try {
      await attributeTemplates.delete({ id: template.id });
      console.log('Шаблон успешно удален');
      loadTemplates();
    } catch (error: any) {
      console.error('Ошибка удаления шаблона:', error);
      alert(error.message || 'Не удалось удалить шаблон');
    }
  }
};

const saveTemplate = async (formData: any) => {
  try {
    saving.value = true;

    if (editingTemplate.value) {
      await attributeTemplates.update({ id: editingTemplate.value.id, ...formData });
      console.log('Шаблон успешно обновлен');
    } else {
      await attributeTemplates.create(formData);
      console.log('Шаблон успешно создан');
    }

    showCreateDialog.value = false;
    editingTemplate.value = null;
    templateForm.value = {};
    await loadGroups(); // Обновляем группы на случай создания новой (это также обновит groupSuggestions)
    await loadTemplates();
  } catch (error: any) {
    console.error('Ошибка сохранения шаблона:', error);
    alert(error.message || 'Не удалось сохранить шаблон');
  } finally {
    saving.value = false;
  }
};

// Инициализация
onMounted(async () => {
  // Сначала инициализируем статичные данные автокомплита
  dataTypeSuggestions.value = [...dataTypeOptions];
  viewModeSuggestions.value = [...viewModeOptions];

  // Применяем состояние из URL к локальным фильтрам (до загрузки)
  const f = urlSync.filters.value as any
  searchQuery.value = f.search || ''
  selectedGroup.value = f.groupId ?? null
  selectedDataType.value = f.dataType ?? null

  // Загружаем динамические данные
  await loadGroups(); // Загружаем группы (это также обновит groupSuggestions)
  await loadTemplates();
});
</script>
