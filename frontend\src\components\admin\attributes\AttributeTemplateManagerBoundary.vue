<template>
  <ErrorBoundary variant="detailed" @retry="onRetry" title="Проблема при загрузке шаблонов" message="Попробуйте обновить список или повторить попытку.">
    <AttributeTemplateManager :key="key" />
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ErrorBoundary from '@/components/ui/ErrorBoundary.vue'
import AttributeTemplateManager from './AttributeTemplateManager.vue'

const key = ref(0)
const onRetry = () => {
  key.value++
}
</script>

