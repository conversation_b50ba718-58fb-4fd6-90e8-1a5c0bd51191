# Система управления атрибутами PartTec

Мощная и универсальная система управления атрибутами для каталога запчастей PartTec.

## Обзор

Система состоит из набора универсальных компонентов для работы с атрибутами, шаблонами атрибутов и их группами. Все компоненты следуют принципам DRY и обеспечивают единообразный пользовательский интерфейс.

## Архитектура

### Основные компоненты

1. **AttributeValueInput** - Универсальный компонент для ввода значений атрибутов
2. **AttributeDisplay** - Компонент для отображения атрибутов в различных режимах
3. **SimpleAttributeManager** - Простой и эффективный редактор атрибутов (заменяет AttributeEditor)
4. **AttributeGroupManager** - Управление группами атрибутов
5. **AttributeTemplateManager** - Управление шаблонами атрибутов
6. **UnifiedAttributeManager** - Главное приложение управления

### Структура данных

```typescript
interface AttributeTemplate {
  id: number;
  name: string;          // Системное имя (snake_case)
  title: string;         // Отображаемое название
  description?: string;  // Описание
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
  unit?: string;         // Единица измерения
  isRequired: boolean;   // Обязательность
  minValue?: number;     // Минимальное значение (для NUMBER)
  maxValue?: number;     // Максимальное значение (для NUMBER)
  allowedValues?: string[]; // Допустимые значения (для STRING)
  group?: AttributeGroup;
}

interface AttributeGroup {
  id: number;
  name: string;
  description?: string;
}

interface AttributeForm {
  id?: number;           // ID существующего атрибута
  templateId: number;    // ID шаблона
  value: string;         // Значение атрибута
  template?: AttributeTemplate;
}
```

## Использование компонентов

### AttributeValueInput

Универсальный компонент для ввода значений с автоматической валидацией:

```vue
<AttributeValueInput
  v-model="attributeValue"
  :template="attributeTemplate"
  placeholder="Введите значение..."
  @error="handleError"
/>
```

**Особенности:**
- Автоматический выбор типа поля на основе `dataType`
- Встроенная валидация по правилам шаблона
- Поддержка всех типов данных и единиц измерения
- Локализованные сообщения об ошибках

### AttributeDisplay

Компонент для отображения атрибутов в различных режимах:

```vue
<AttributeDisplay
  :attribute="attribute"
  mode="detailed"
  :show-group="true"
  size="normal"
/>
```

**Режимы отображения:**
- `compact` - Компактный (одна строка)
- `card` - Карточка
- `table` - Табличный
- `detailed` - Детальный

### SimpleAttributeManager

Простой и эффективный редактор для работы с наборами атрибутов:

```vue
<SimpleAttributeManager
  v-model="attributes"
  title="Атрибуты запчасти"
  :show-group-selector="true"
  :group-by-template="true"
  card-mode="detailed"
  :entity-id="partId"
/>
```

**Возможности:**
- Поиск и добавление шаблонов атрибутов
- Быстрое добавление по группам шаблонов
- Группировка атрибутов по шаблонам (опционально)
- Inline редактирование значений
- Валидация значений
- Редактирование и удаление атрибутов
- Диалоги для детального редактирования

### AttributeGroupManager

Управление группами атрибутов:

```vue
<AttributeGroupManager />
```

**Функции:**
- Создание, редактирование, удаление групп
- Просмотр шаблонов в группах
- Поиск и фильтрация
- Статистика использования

### AttributeTemplateManager

Управление шаблонами атрибутов:

```vue
<AttributeTemplateManager />
```

**Функции:**
- Создание, редактирование, удаление шаблонов
- Фильтрация по группам и типам данных
- Табличный и карточный режимы просмотра
- Статистика использования
- Пагинация

## Типы данных и валидация

### Поддерживаемые типы данных

1. **STRING** - Строковые значения
   - Поддержка допустимых значений (dropdown)
   - Валидация по списку

2. **NUMBER** - Числовые значения
   - Поддержка диапазонов (min/max)
   - Автоматическое форматирование
   - Единицы измерения

3. **BOOLEAN** - Логические значения
   - Checkbox интерфейс
   - Локализованное отображение (Да/Нет)

4. **DATE** - Даты
   - Календарный виджет
   - Локализованный формат

5. **JSON** - JSON данные
   - Textarea с валидацией JSON
   - Форматированное отображение

### Единицы измерения

Система поддерживает широкий набор единиц измерения:

- **Размеры**: MM (мм), INCH (дюймы), FT (футы)
- **Вес**: G (г), KG (кг), T (т), LB (фунты)
- **Объем**: ML (мл), L (л), GAL (галлоны)
- **Количество**: PCS (шт), SET (комплект), PAIR (пара)
- **Давление**: BAR (бар), PSI (PSI)
- **Мощность**: KW (кВт), HP (л.с.)
- **Момент**: NM (Н⋅м), RPM (об/мин)
- **Температура**: C (°C), F (°F)
- **Процент**: PERCENT (%)

## Интеграция с существующими компонентами

### Обновление PartWizard

Старый компонент `AttributeManager` был заменен на использование нового `SimpleAttributeManager`:

```vue
<!-- Старый подход -->
<AttributeManager v-model="attributes" />

<!-- Новый подход -->
<SimpleAttributeManager
  v-model="attributes"
  title="Атрибуты запчасти"
  :show-group-selector="true"
  :group-by-template="false"
  card-mode="detailed"
/>
```

### Миграция данных

Компоненты поддерживают как новый формат данных, так и совместимость со старым:

```typescript
// Новый формат (рекомендуемый)
{
  id: 1,
  templateId: 5,
  value: "NBR",
  template: { /* полная информация о шаблоне */ }
}

// Совместимость со старым форматом
{
  id: 1,
  templateId: 5,
  value: "NBR",
  templateTitle: "Материал",
  templateDataType: "STRING",
  templateUnit: null,
  templateGroup: "Материалы"
}
```

## Страница управления

Доступна централизованная страница управления по адресу `/admin/attributes`:

- **Обзор** - Статистика и аналитика
- **Шаблоны** - Управление шаблонами атрибутов
- **Группы** - Управление группами
- **Редактор** - Демонстрация функциональности

## Лучшие практики

1. **Именование шаблонов**:
   - `name` - snake_case для системного использования
   - `title` - Человекочитаемое название

2. **Группировка**:
   - Логически группируйте связанные атрибуты
   - Используйте описательные названия групп

3. **Валидация**:
   - Всегда указывайте `isRequired` для обязательных полей
   - Используйте `allowedValues` для ограниченных наборов
   - Задавайте `minValue`/`maxValue` для числовых диапазонов

4. **Единицы измерения**:
   - Выбирайте подходящие единицы для типа данных
   - Используйте стандартные обозначения

5. **Производительность**:
   - Используйте пагинацию для больших списков
   - Применяйте debounce для поиска
   - Кэшируйте часто используемые данные

## API интеграция

Система полностью интегрирована с реальными tRPC методами:

### Шаблоны атрибутов
- `attributeTemplates.findMany()` - поиск и фильтрация шаблонов
- `attributeTemplates.create()` - создание нового шаблона
- `attributeTemplates.update()` - обновление шаблона
- `attributeTemplates.delete()` - удаление шаблона
- `attributeTemplates.findById()` - получение шаблона по ID

### Группы атрибутов
- `attributeTemplates.findAllGroups()` - получение всех групп
- `attributeTemplates.createGroup()` - создание группы
- `attributeTemplates.updateGroup()` - обновление группы
- `attributeTemplates.deleteGroup()` - удаление группы

### Атрибуты запчастей
- `partAttributes.findByPartId()` - получение атрибутов запчасти
- `partAttributes.create()` - создание атрибута
- `partAttributes.update()` - обновление атрибута
- `partAttributes.delete()` - удаление атрибута
- `partAttributes.bulkCreate()` - массовое создание атрибутов

## Расширение системы

Система спроектирована для легкого расширения:

1. **Новые типы данных** - добавьте в `AttributeValueInput`
2. **Новые единицы измерения** - обновите справочники
3. **Дополнительная валидация** - расширьте функции валидации
4. **Новые режимы отображения** - добавьте в `AttributeDisplay`

## Поддержка и разработка

При разработке новых функций следуйте принципам:

- **Универсальность** - компоненты должны работать в разных контекстах
- **Переиспользование** - избегайте дублирования кода
- **Типизация** - используйте TypeScript для всех интерфейсов
- **Тестирование** - покрывайте новую функциональность тестами
- **Документация** - обновляйте документацию при изменениях
