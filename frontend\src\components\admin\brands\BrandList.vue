<script setup lang="ts">
import { trpc } from '@/lib/trpc'
import Button from '@/volt/Button.vue'
import DataTable from '@/volt/DataTable.vue'
import { PencilIcon, TrashIcon, PlusIcon } from 'lucide-vue-next'
import Column from 'primevue/column'
import EditBrandDialog from './EditBrandDialog.vue'
import { ref, watch } from 'vue'
import { navigate } from 'astro:transitions/client'
import type { BrandWithCount, BrandFormData } from '@/types/brand'
import InputText from '@/volt/InputText.vue'
import Toast from '@/volt/Toast.vue'

const searchValue = ref('')
const dialogVisible = ref(false)
const editingBrand = ref<BrandFormData | null>(null)

const props = defineProps<{
  initialData: BrandWithCount[]
}>()

const items = ref(props.initialData)

// Тип для ключей BrandWithCount, которые мы хотим отображать в таблице
type DisplayableBrandKey = keyof Omit<BrandWithCount, '_count'>

const keyMapping: Record<DisplayableBrandKey, string> = {
  id: 'ID',
  name: 'Наименование',
  slug: 'url слаг',
  country: 'Страна',
  isOem: 'OEM',
}

// Явный список ключей для колонок, чтобы избежать зависимости от runtime данных
const columnKeys: DisplayableBrandKey[] = [
  'id',
  'name',
  'slug',
  'country',
  'isOem',
]


function createBrand() {
  editingBrand.value = {} // Пустой объект для создания
  dialogVisible.value = true
}

function editBrand(data: BrandWithCount) {
  editingBrand.value = { ...data }
  dialogVisible.value = true
}


async function handleSave(brandData: BrandFormData) {
  if (!brandData) return;

  try {
    if (brandData.id) {
      // Обновление существующего бренда
      const { id, ...dataToUpdate } = brandData
      await trpc.crud.brand.update.mutate({
        where: { id },
        data: dataToUpdate,
      })
    } else {
      // Создание нового бренда
      // Убедимся, что все обязательные поля присутствуют
      if (brandData.name && brandData.slug) {
         await trpc.crud.brand.create.mutate({
          data: {
            name: brandData.name,
            slug: brandData.slug,
            country: brandData.country,
            isOem: brandData.isOem || false
          },
        })
      } else {
        console.error("Name and slug are required to create a brand.")
        // Можно показать уведомление пользователю
        return
      }
    }
    // Перезагрузка страницы для отображения изменений
    navigate(window.location.href)
  } catch (error) {
    console.error('Failed to save brand:', error)
    // Здесь можно добавить обработку ошибок, например, показать тост
  } finally {
    dialogVisible.value = false
  }
}

/**
 * Обработчик события отмены редактирования
 */
function handleCancel() {
  dialogVisible.value = false
  editingBrand.value = null
}
async function deleteBrand(data: BrandWithCount) {
  dialogVisible.value = false
  // Здесь можно добавить диалог подтверждения удаления
  await trpc.crud.brand.delete.mutate({
    where: {
      id: data.id,
    },
  })

  navigate(window.location.href)
}

watch(searchValue, (newValue) => {
  debouncedSearch(newValue)
})

async function debouncedSearch(value = '') {
  console.log('value', value)

    items.value = await trpc.crud.brand.findMany.query({
      where: {
        OR: [
          {
            name: {
              contains: value,
            }
          },
          {
            slug: {
              contains: value,
            }
          },
          {
            country: {
              contains: value,
            }
          },

        ],
      },
    })
}
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-2xl font-bold">Бренды</h1>
      <Button @click="createBrand">
        <PlusIcon class="w-5 h-5 mr-2" />
        Создать бренд
      </Button>
    </div>

    <DataTable show-headers :value="items">
      <template #header>
        <div class="flex justify-end">
          <InputText v-model="searchValue" placeholder="Поиск" />
        </div>
      </template>
      <Column
        v-for="key in columnKeys"
        :key="key"
        :field="key"
        :header="keyMapping[key] || key"
      />
      <!-- Отдельная колонка для вложенного свойства -->
      <Column field="_count.catalogItems" header="Кол-во кат.поз." />

      <Column header="Действия">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button @click="editBrand(data)" outlined size="small">
              <PencilIcon class="w-5 h-5" />
            </Button>
            <Button
              @click="deleteBrand(data)"
              outlined
              severity="danger"
              size="small"
            >
              <TrashIcon class="w-5 h-5" />
            </Button>
          </div>
        </template>
      </Column>
    </DataTable>

    <EditBrandDialog
      v-model:isVisible="dialogVisible"
      :brand="editingBrand"
      @save="handleSave"
      @cancel="handleCancel"
    />

    <!-- Toast контейнер для изолята брендов -->
    <Toast />
  </div>
</template>
