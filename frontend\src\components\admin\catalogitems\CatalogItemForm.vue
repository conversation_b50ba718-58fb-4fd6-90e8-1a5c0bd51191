<template>
  <div class="catalog-item-form">
    <form @submit.prevent="onSubmit" class="space-y-6">
      <!-- Основная информация -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Артикул -->
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Артикул (SKU) *
          </label>
          <VInputText
            v-model="formData.sku"
            placeholder="Например: 12345-ABC"
            class="w-full"
            :class="{ 'p-invalid': errors.sku }"
            @blur="validateSku"
          />
          <small v-if="errors.sku" class="p-error">{{ errors.sku }}</small>
        </div>

        <!-- Бренд -->
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Бренд *
          </label>
          <div class="flex gap-2">
            <VAutoComplete
              v-model="formData.selectedBrand"
              :suggestions="brandSuggestions"
              @complete="searchBrands"
              option-label="name"
              placeholder="Поиск бренда..."
              class="flex-1"
              :class="{ 'p-invalid': errors.brandId }"
              dropdown
            />
            <VButton
              @click="showCreateBrand = true"
              severity="secondary"
              outlined
              size="small"
              v-tooltip="'Создать новый бренд'"
            >
              <Icon name="pi pi-plus" class="w-5 h-5" />
            </VButton>
          </div>
          <small v-if="errors.brandId" class="p-error">{{ errors.brandId }}</small>
        </div>
      </div>

      <!-- Описание -->
      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Описание
        </label>
        <VTextarea
          v-model="formData.description"
          placeholder="Описание каталожной позиции..."
          rows="3"
          class="w-full"
        />
      </div>

      <!-- Источник -->
      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Источник информации
        </label>
        <VInputText
          v-model="formData.source"
          placeholder="Например: Официальный каталог, Данные клиента"
          class="w-full"
        />
      </div>

      <!-- Настройки видимости -->
      <div class="flex items-center gap-3">
        <VCheckbox
          v-model="formData.isPublic"
          input-id="isPublic"
          binary
        />
        <label for="isPublic" class="text-sm font-medium text-surface-700 dark:text-surface-300">
          Публичная позиция (видна всем пользователям)
        </label>
      </div>

      <!-- Атрибуты -->
      <div>
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-lg font-medium text-surface-900 dark:text-surface-0">
            Атрибуты
          </h4>
          <VButton
            @click="showAttributeManager = true"
            severity="secondary"
            outlined
            size="small"
            label="Добавить атрибут"
          >
            <template #icon>
              <Icon name="pi pi-plus" class="w-5 h-5" />
            </template>
          </VButton>
        </div>

        <!-- Список атрибутов -->
        <div v-if="formData.attributes.length > 0" class="space-y-3">
          <div
            v-for="(attribute, index) in formData.attributes"
            :key="index"
            class="flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"
          >
            <div class="flex-1">
              <div class="font-medium text-surface-900 dark:text-surface-0">
                {{ attribute.template?.title || attribute.templateTitle }}
                <span v-if="attribute.template?.isRequired" class="text-red-500 ml-1">*</span>
              </div>
              <div class="text-sm text-surface-600 dark:text-surface-400">
                {{ attribute.value }}
                {{ attribute.template?.unit ? getUnitLabel(attribute.template.unit) : '' }}
                <span
                  v-if="attribute.template?.group?.name"
                  class="ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"
                >
                  {{ attribute.template.group.name }}
                </span>
              </div>
            </div>
            <VButton
              @click="removeAttribute(index)"
              severity="danger"
              size="small"
              text
            >
              <Icon name="pi pi-trash" class="w-5 h-5" />
            </VButton>
          </div>
        </div>

        <div v-else class="text-center py-6 text-surface-500">
          Атрибуты не добавлены
        </div>
      </div>

      <!-- Кнопки действий -->
      <div class="flex justify-end gap-3 pt-4 border-t border-surface-200 dark:border-surface-700">
        <VButton
          @click="$emit('cancel')"
          severity="secondary"
          outlined
          label="Отмена"
        />
        <VButton
          type="submit"
          :loading="loading"
          :disabled="!isFormValid"
          :label="item ? 'Сохранить изменения' : 'Создать позицию'"
        />
      </div>
    </form>

    <!-- Диалог управления атрибутами -->
    <VDialog
      v-model:visible="showAttributeManager"
      modal
      header="Управление атрибутами"
      class="w-full max-w-2xl"
    >
      <AttributeManager
        v-model="formData.attributes"
        @close="showAttributeManager = false"
      />
    </VDialog>

    <!-- Диалог создания бренда -->
    <QuickCreateBrand
      v-model:visible="showCreateBrand"
      @created="onBrandCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useTrpc } from '@/composables/useTrpc'
import VInputText from '@/volt/InputText.vue'
import VTextarea from '@/volt/Textarea.vue'
import VAutoComplete from '@/volt/AutoComplete.vue'
import VCheckbox from '@/volt/Checkbox.vue'
import VButton from '@/volt/Button.vue'
import VDialog from '@/volt/Dialog.vue'
import AttributeManager from '@/components/admin/attributes/AttributeManager.vue'
import QuickCreateBrand from '../parts/QuickCreateBrand.vue'
import Icon from '@/components/ui/Icon.vue'

// Props
interface Props {
  item?: any // Элемент для редактирования
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'save', data: any): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// Composables
const { brands, loading } = useTrpc()

// Состояние формы
const formData = ref({
  sku: '',
  selectedBrand: null as any,
  description: '',
  source: '',
  isPublic: true,
  attributes: [] as any[]
})

// Валидация
const errors = ref({
  sku: '',
  brandId: ''
})

// Диалоги
const showAttributeManager = ref(false)
const showCreateBrand = ref(false)

// Поиск брендов
const brandSuggestions = ref<any[]>([])

// Методы валидации
const validateSku = () => {
  errors.value.sku = ''
  
  if (!formData.value.sku.trim()) {
    errors.value.sku = 'Артикул обязателен'
    return false
  }
  
  if (formData.value.sku.length < 2) {
    errors.value.sku = 'Артикул должен содержать минимум 2 символа'
    return false
  }
  
  if (formData.value.sku.length > 64) {
    errors.value.sku = 'Артикул не может быть длиннее 64 символов'
    return false
  }
  
  return true
}

const validateBrand = () => {
  errors.value.brandId = ''
  
  if (!formData.value.selectedBrand) {
    errors.value.brandId = 'Бренд обязателен'
    return false
  }
  
  return true
}

const isFormValid = computed(() => {
  return formData.value.sku.trim() && 
         formData.value.selectedBrand &&
         !errors.value.sku &&
         !errors.value.brandId
})

// Методы
const searchBrands = async (event: any) => {
  try {
    const query = event.query.toLowerCase()
    const result = await brands.findMany({
      where: {
        name: { contains: query, mode: 'insensitive' }
      },
      take: 10
    })
    if (result) {
      brandSuggestions.value = result
    }
  } catch (err) {
    console.error('Ошибка поиска брендов:', err)
  }
}

const onBrandCreated = (brand: any) => {
  formData.value.selectedBrand = brand
  brandSuggestions.value = [brand, ...brandSuggestions.value]
}

const removeAttribute = (index: number) => {
  formData.value.attributes.splice(index, 1)
}

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    MM: 'мм',
    INCH: 'дюймы',
    FT: 'футы',
    G: 'г',
    KG: 'кг',
    T: 'т',
    LB: 'фунты',
    ML: 'мл',
    L: 'л',
    GAL: 'галлоны',
    PCS: 'шт',
    SET: 'комплект',
    PAIR: 'пара',
    BAR: 'бар',
    PSI: 'PSI',
    KW: 'кВт',
    HP: 'л.с.',
    NM: 'Н⋅м',
    RPM: 'об/мин',
    C: '°C',
    F: '°F',
    PERCENT: '%'
  }
  return labels[unit] || unit
}

const onSubmit = () => {
  // Валидация
  const isSkuValid = validateSku()
  const isBrandValid = validateBrand()
  
  if (!isSkuValid || !isBrandValid) {
    return
  }

  // Подготовка данных для сохранения
  const saveData: any = {
    sku: formData.value.sku.toUpperCase().trim(),
    brandId: formData.value.selectedBrand.id,
    description: formData.value.description.trim() || undefined,
    source: formData.value.source.trim() || undefined,
    isPublic: formData.value.isPublic
  }

  // Добавляем атрибуты, если они есть
  // Фильтруем атрибуты: только с заполненными значениями и валидными templateId
  const validAttributes = formData.value.attributes ? formData.value.attributes.filter(attr => {
    const hasValue = attr.value && String(attr.value).trim() !== ''
    const hasTemplateId = attr.templateId || attr.template?.id
    return hasValue && hasTemplateId
  }) : []

  if (validAttributes.length > 0) {
    if (props.item) {
      // При редактировании: заменяем все атрибуты
      saveData.attributes = {
        deleteMany: {}, // Удаляем все существующие атрибуты
        create: validAttributes.map(attr => ({
          templateId: attr.templateId || attr.template?.id,
          value: attr.value
        }))
      }
    } else {
      // При создании: просто создаем новые атрибуты
      saveData.attributes = {
        create: validAttributes.map(attr => ({
          templateId: attr.templateId || attr.template?.id,
          value: attr.value
        }))
      }
    }
  } else if (props.item) {
    // При редактировании: если атрибутов нет, удаляем все существующие
    saveData.attributes = {
      deleteMany: {}
    }
  }

  emit('save', saveData)
}

// Загрузка данных для редактирования
const loadItemData = () => {
  if (props.item) {
    formData.value = {
      sku: props.item.sku || '',
      selectedBrand: props.item.brand || null,
      description: props.item.description || '',
      source: props.item.source || '',
      isPublic: props.item.isPublic ?? true,
      attributes: props.item.attributes || []
    }
  }
}

// Watchers
watch(() => props.item, loadItemData, { immediate: true })

// Инициализация
onMounted(() => {
  loadItemData()
})
</script>
