<template>
  <ErrorBoundary variant="detailed" title="Ошибка каталожных позиций" message="Не удалось загрузить или отрисовать таблицу. Попробуйте повторить."
    @retry="onRetry">
    <CatalogItemsManager :key="key" />
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ErrorBoundary from '@/components/ui/ErrorBoundary.vue'
import CatalogItemsManager from './CatalogItemsManager.vue'

const key = ref(0)
const onRetry = () => {
  key.value++
}
</script>

