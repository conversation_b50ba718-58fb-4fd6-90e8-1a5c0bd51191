<template>
  <div class="p-4 space-y-4">
    <div class="flex items-center justify-between">
      <div>
        <div class="text-sm text-surface-500">Каталожная позиция</div>
        <div class="text-lg font-mono font-semibold">{{ item.sku }} <span class="text-surface-500">—</span> {{ item.brand?.name }}</div>
      </div>
      <div class="flex gap-2">
        <VButton severity="secondary" outlined size="small" :loading="loading" @click="$emit('refresh')">
          <RefreshCcwIcon />
        </VButton>
      </div>
    </div>

      <MatchingLoadingState v-if="loading" />

    <MatchingEmptyState v-else-if="!results || results.length === 0" />

      <div v-else class="space-y-3">
      <VCard v-for="c in results" :key="c.part.id" class="border">
        <template #content>
          <div class="p-4 grid grid-cols-1 md:grid-cols-3 gap-3 items-start">
            <div class="md:col-span-1">
              <div class="font-semibold text-surface-900 dark:text-surface-0">{{ c.part.name || ('Группа #' + c.part.id) }}</div>
              <div class="mt-2">
                <VTag :value="getAccuracyLabel(c.accuracySuggestion)" :severity="getAccuracySeverity(c.accuracySuggestion)" />
              </div>
              <div class="mt-3">
                <VButton size="small" severity="secondary" outlined @click="openConfirmDialog(c)" v-tooltip="'Создать связь'">
                  <LinkIcon />
                </VButton>
                <VButton size="small" severity="secondary" outlined @click="queueProposal(c)" class="ml-2" v-tooltip="'В очередь предложений'">
                  <SendIcon />
                </VButton>
              </div>
            </div>
            <div class="md:col-span-2">
              <div class="text-sm text-surface-500 mb-2">Детали сопоставления</div>
              <MatchingDetailsGrid :details="c.details" :controls="false" />
            </div>
          </div>
        </template>
      </VCard>
    </div>

    <!-- Диалог подтверждения связи -->
    <VDialog v-model:visible="showConfirmDialog" modal header="Подтверждение связи" class="w-full max-w-3xl">
      <div v-if="selectedCandidate" class="space-y-4">
        <!-- Информация о связываемых элементах -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-surface-50 dark:bg-surface-900 rounded">
          <div>
            <div class="text-sm text-surface-500">Каталожная позиция</div>
            <div class="font-semibold">{{ item.sku }}</div>
            <div class="text-sm">{{ item.brand?.name }}</div>
          </div>
          <div>
            <div class="text-sm text-surface-500">Группа взаимозаменяемости</div>
            <div class="font-semibold">{{ selectedCandidate.part.name || `Группа #${selectedCandidate.part.id}` }}</div>
          </div>
        </div>

        <!-- Детали сопоставления -->
        <div>
          <h3 class="text-lg font-semibold mb-3">Детали сопоставления</h3>
          <MatchingDetailsGrid :details="selectedCandidate.details" :controls="false" />
        </div>

        <!-- Форма подтверждения -->
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
              Точность совпадения
            </label>
            <VSelect 
              v-model="confirmForm.accuracy" 
              :options="accuracyOptions" 
              option-label="label" 
              option-value="value"
              placeholder="Выберите точность" 
              class="w-full"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
              Примечания
            </label>
            <VTextarea 
              v-model="confirmForm.notes" 
              rows="3" 
              placeholder="Дополнительная информация о совместимости..."
              class="w-full"
            />
            <small class="text-surface-500">
              Укажите особенности применения, ограничения или условия замены
            </small>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between">
          <VButton label="Отмена" severity="secondary" @click="closeConfirmDialog" />
          <VButton label="Создать связь" severity="success" @click="confirmLink" :loading="linking" />
        </div>
      </template>
    </VDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import VButton from '@/volt/Button.vue'
import VCard from '@/volt/Card.vue'
import VTag from '@/volt/Tag.vue'
import VDialog from '@/volt/Dialog.vue'
import VSelect from '@/volt/Select.vue'
import VTextarea from '@/volt/Textarea.vue'
import Icon from '@/components/ui/Icon.vue'
import MatchingDetailsGrid from './MatchingDetailsGrid.vue'
import MatchingEmptyState from './MatchingEmptyState.vue'
import { useMatchingLabels } from '@/composables/useMatchingLabels'

const props = defineProps<{ item: any; results: any[] | null; loading: boolean }>()
const emit = defineEmits<{ (e: 'refresh'): void; (e: 'link', payload: { partId: number; accuracy: 'EXACT_MATCH'|'MATCH_WITH_NOTES'|'REQUIRES_MODIFICATION'|'PARTIAL_MATCH'; notes?: string }): void }>()

const { getAccuracyLabel, getAccuracySeverity } = useMatchingLabels()

// Состояние диалога подтверждения
const showConfirmDialog = ref(false)
const selectedCandidate = ref<any>(null)
const linking = ref(false)
const confirmForm = reactive({
  accuracy: 'EXACT_MATCH' as string,
  notes: ''
})

// Опции для выбора точности
const accuracyOptions = [
  { label: 'Точное совпадение', value: 'EXACT_MATCH' },
  { label: 'Совпадение с примечаниями', value: 'MATCH_WITH_NOTES' },
  { label: 'Требуется модификация', value: 'REQUIRES_MODIFICATION' },
  { label: 'Частичное совпадение', value: 'PARTIAL_MATCH' }
]

// Открытие диалога подтверждения
const openConfirmDialog = (candidate: any) => {
  selectedCandidate.value = candidate
  
  // Устанавливаем начальные значения формы
  confirmForm.accuracy = candidate.accuracySuggestion
  confirmForm.notes = ''
  
  // Если есть примечания из сопоставления - предзаполняем
  const withNotes = (candidate.details || []).find((d: any) => 
    String(d.kind).includes('NEAR') || String(d.kind).includes('LEGACY')
  )
  if (withNotes?.notes) {
    confirmForm.notes = withNotes.notes
  }
  
  // При совпадении по допуску тоже добавляем заметку
  const tol = (candidate.details || []).find((d: any) => d.kind === 'NUMBER_WITHIN_TOLERANCE')
  if (tol && !confirmForm.notes) {
    confirmForm.notes = 'Совпадение по допуску'
  }
  
  showConfirmDialog.value = true
}

// Закрытие диалога
const closeConfirmDialog = () => {
  showConfirmDialog.value = false
  selectedCandidate.value = null
  confirmForm.accuracy = 'EXACT_MATCH'
  confirmForm.notes = ''
}

// Подтверждение связи
const confirmLink = () => {
  if (!selectedCandidate.value) return
  
  linking.value = true
  
  // Эмитим событие с выбранными параметрами
  emit('link', {
    partId: selectedCandidate.value.part.id,
    accuracy: confirmForm.accuracy as any,
    notes: confirmForm.notes || undefined
  })
  
  linking.value = false
  closeConfirmDialog()
}

// Очередь предложений — сохранение PENDING (admin → общая очередь на странице предложений)
import { useTrpc } from '@/composables/useTrpc'
import { useToast } from '@/composables/useToast'
import { LoaderIcon } from 'lucide-vue-next';
import { RefreshCcwIcon } from 'lucide-vue-next';
import { LinkIcon } from 'lucide-vue-next';
import { SendIcon } from 'lucide-vue-next';
import MatchingLoadingState from './MatchingLoadingState.vue'
const { matching } = useTrpc()
const toast = useToast()
const queueProposal = async (c: any) => {
  try {
    const payload = { partId: c.part.id, accuracy: c.accuracySuggestion as 'EXACT_MATCH' | 'MATCH_WITH_NOTES', notes: undefined as string | undefined }
    const withNotes = (c.details || []).find((d: any) => String(d.kind).includes('NEAR') || String(d.kind).includes('LEGACY'))
    if (withNotes?.notes) payload.notes = withNotes.notes
    const tol = (c.details || []).find((d: any) => d.kind === 'NUMBER_WITHIN_TOLERANCE')
    if (tol && !payload.notes) payload.notes = 'Совпадение по допуску'
    await matching.proposeLink({ catalogItemId: (props.item as any).id, partId: payload.partId, accuracySuggestion: payload.accuracy, notesSuggestion: payload.notes, details: c.details })
    // глобальный успех в useTrpc
  } catch (err) {
    toast.error('Ошибка', 'Не удалось добавить предложение')
  }
}
</script>


