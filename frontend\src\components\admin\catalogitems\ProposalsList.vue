<template>
  <div class="space-y-4">
    <div class="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
      <div class="flex items-center gap-2">
        <VSelect v-model="status" :options="statusOptions" optionLabel="label" optionValue="value" class="w-56" />
        <VButton :loading="loading" label="Обновить" @click="load">
          <template #icon>
            <Icon name="pi pi-refresh" class="w-5 h-5" />
          </template>
        </VButton>
        <div class="hidden md:block text-sm text-surface-500">
          Показано {{ pageStart }}–{{ pageEnd }} из {{ total }}
        </div>
      </div>
      <div class="flex items-center gap-3">
        <div class="flex items-center gap-2">
          <span class="text-xs text-surface-500">На странице</span>
          <VSelect v-model="take" :options="takeOptions" optionLabel="label" optionValue="value" class="w-28" />
        </div>
        <div class="flex items-center gap-2">
          <span class="text-xs text-surface-500">Раскрыть детали</span>
          <VToggleSwitch v-model="expandAll" />
        </div>
        <VButton label="Сгенерировать" severity="secondary" @click="generate">
          <template #icon>
            <Icon name="pi pi-cog" class="w-5 h-5" />
          </template>
        </VButton>
      </div>
    </div>

    <VCard>
      <template #content>
        <div v-if="loading" class="py-10 text-center text-surface-500">Загрузка...</div>
        <div v-else-if="items.length === 0" class="py-10 text-center text-surface-500">Нет предложений</div>
        <div v-else class="divide-y divide-surface-border">
          <div v-for="p in items" :key="p.id" class="py-3 grid grid-cols-1 md:grid-cols-3 gap-3 items-start">
            <div class="md:col-span-1">
              <div class="text-sm text-surface-500">Каталожная позиция</div>
              <div class="font-mono font-semibold">{{ p.catalogItem.sku }} — {{ p.catalogItem.brand?.name }}</div>
              <a :href="`/admin/parts/${p.part.id}`" class="text-xs text-surface-500">Группа: {{ p.part.name || ('#' + p.part.id) }}</a>
            </div>
            <div class="md:col-span-1">
              <div class="text-sm text-surface-500 mb-1">Предложение</div>
              <VTag :value="getAccuracyLabel(p.accuracySuggestion)" :severity="getAccuracySeverity(p.accuracySuggestion)" />
              <div v-if="p.notesSuggestion" class="text-xs text-surface-500 mt-1">{{ p.notesSuggestion }}</div>
            </div>
            <div class="md:col-span-1 flex items-center justify-end gap-2">
              <VButton size="small" label="Отклонить" severity="danger" outlined @click="reject(p.id)" v-tooltip="'Отклонить предложение'">
                <template #icon>
                  <Icon name="pi pi-times" class="w-5 h-5" />
                </template>
              </VButton>
              <VButton size="small" label="Подтвердить" @click="approve(p.id, p.accuracySuggestion, p.notesSuggestion)" v-tooltip="'Подтвердить и применить'">
                <template #icon>
                  <Icon name="pi pi-check" class="w-5 h-5" />
                </template>
              </VButton>
            </div>
            <div class="md:col-span-3">
              <details :open="expandAll">
                <summary class="text-xs text-surface-500 cursor-pointer flex items-center gap-2 select-none">
                  Детали сопоставления
                  <span class="text-surface-400">({{ summarizeDetails(p.details).total }})</span>
                  <span class="hidden md:inline-flex items-center gap-1">
                    <VTag size="small" :value="`EXACT ${summarizeDetails(p.details).exact}`" severity="success" />
                    <VTag size="small" :value="`NEAR ${summarizeDetails(p.details).near}`" severity="info" />
                    <VTag size="small" :value="`TOL ${summarizeDetails(p.details).tol}`" severity="info" />
                    <VTag size="small" :value="`LEGACY ${summarizeDetails(p.details).legacy}`" severity="warning" />
                  </span>
                </summary>
                <div class="mt-2">
                  <MatchingDetailsGrid :details="p.details || []" />
                </div>
              </details>
            </div>
          </div>
        </div>
      </template>
    </VCard>

    <div class="flex items-center justify-between gap-2">
      <div class="text-sm text-surface-500">Показано {{ pageStart }}–{{ pageEnd }} из {{ total }}</div>
      <div class="flex justify-end gap-2">
        <VButton :disabled="skip===0" label="Назад" severity="secondary" outlined @click="prev" />
        <VButton :disabled="skip+take>=total" label="Вперёд" severity="secondary" outlined @click="next" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import VButton from '@/volt/Button.vue'
import VCard from '@/volt/Card.vue'
import VSelect from '@/volt/Select.vue'
import VTag from '@/volt/Tag.vue'
import VToggleSwitch from '@/volt/ToggleSwitch.vue'
import MatchingDetailsGrid from './MatchingDetailsGrid.vue'
import { ref, onMounted, computed, watch } from 'vue'
import { useTrpc } from '@/composables/useTrpc'
import { useMatchingLabels } from '@/composables/useMatchingLabels'
import Icon from '@/components/ui/Icon.vue'

const { matching, loading } = useTrpc()
const { getAccuracyLabel, getAccuracySeverity } = useMatchingLabels()

const status = ref<'PENDING'|'APPROVED'|'REJECTED'|'INVALIDATED'>('PENDING')
const statusOptions = [
  { label: 'Ожидают', value: 'PENDING' },
  { label: 'Подтверждено', value: 'APPROVED' },
  { label: 'Отклонено', value: 'REJECTED' },
  { label: 'Инвалидировано', value: 'INVALIDATED' }
]
const items = ref<any[]>([])
const total = ref(0)
const take = ref(20)
const takeOptions = [
  { label: '10', value: 10 },
  { label: '20', value: 20 },
  { label: '50', value: 50 }
]
const skip = ref(0)
const expandAll = ref(false)

const pageStart = computed(() => total.value === 0 ? 0 : skip.value + 1)
const pageEnd = computed(() => Math.min(skip.value + take.value, total.value))

const load = async () => {
  const res = await matching.listProposals({ status: status.value, skip: skip.value, take: take.value })
  if (res) { items.value = (res as any).items; total.value = (res as any).total }
}

watch(status, async () => { skip.value = 0; await load() })
watch(take, async () => { skip.value = 0; await load() })

const next = async () => { if (skip.value + take.value < total.value) { skip.value += take.value; await load() } }
const prev = async () => { if (skip.value > 0) { skip.value = Math.max(0, skip.value - take.value); await load() } }

const approve = async (id: number, accuracy: any, notes?: string) => {
  const res = await matching.approveProposal({ id, override: { accuracy, notes } })
  if (res) await load()
}
const reject = async (id: number) => {
  const res = await matching.rejectProposal({ id })
  if (res) await load()
}

const generate = async () => {
  const res = await matching.generateProposals({ take: 50 })
  if (res) await load()
}

onMounted(load)

const summarizeDetails = (details: any[] = []) => {
  let exact = 0, near = 0, legacy = 0, tol = 0
  for (const d of details) {
    const kind = String(d?.kind || '')
    if (kind.includes('EXACT')) exact++
    if (kind.includes('NEAR')) near++
    if (kind.includes('LEGACY')) legacy++
    if (kind.includes('WITHIN_TOLERANCE')) tol++
  }
  return { total: details.length, exact, near, legacy, tol }
}
</script>


