<template>
  <ErrorBoundary variant="minimal" title="Ошибка категорий" message="Список категорий не отрисовался. Повторите попытку."
    @retry="onRetry">
    <CategoryList :initialData="initialData" :key="key" />
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ErrorBoundary from '@/components/ui/ErrorBoundary.vue'
import CategoryList from './CategoryList.vue'

const props = defineProps<{ initialData: any[] }>()

const key = ref(0)
const onRetry = () => {
  key.value++
}
</script>

