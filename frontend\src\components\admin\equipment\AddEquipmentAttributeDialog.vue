<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { SearchIcon, XIcon, InfoIcon } from 'lucide-vue-next'
import Dialog from '@/volt/Dialog.vue'
import Button from '@/volt/Button.vue'
import InputText from '@/volt/InputText.vue'
import InputNumber from '@/volt/InputNumber.vue'
import Select from '@/volt/Select.vue'
import Checkbox from '@/volt/Checkbox.vue'
import Textarea from '@/volt/Textarea.vue'
import Tag from '@/volt/Tag.vue'
import Message from '@/volt/Message.vue'
import { useTrpc } from '@/composables/useTrpc'
import { useToast } from '@/composables/useToast'
import type { 
  AttributeTemplate, 
  AttributeFormData, 
  AttributeDataType,
  AttributeTemplateFilter,
  EquipmentModelAttributeWithTemplate
} from '@/types/attributes'
import { 
  createAttributeValueSchema
} from '@/types/attributes'
import { 
  getAvailableTemplates, 
  getDataTypeDisplayName, 
  getUnitDisplayName,
  convertValueToType, 
  convertValueToString 
} from '@/utils/attributes'
import AttributeValueInput from '@/components/admin/attributes/AttributeValueInput.vue'

interface Props {
  visible: boolean
  equipmentId: string
  existingAttributes?: EquipmentModelAttributeWithTemplate[]
}

const props = withDefaults(defineProps<Props>(), {
  existingAttributes: () => []
})

const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'save': [data: AttributeFormData]
  'cancel': []
}>()

const { $trpc } = useTrpc()
const toast = useToast()

// Form state
const formData = ref<AttributeFormData>({
  templateId: 0,
  value: '',
  template: undefined
})

// UI state
const loading = ref(false)
const saving = ref(false)
const searchQuery = ref('')
const selectedGroupId = ref<number | null>(null)
const selectedDataType = ref<AttributeDataType | null>(null)
const validationErrors = ref<string[]>([])

// Data
const allTemplates = ref<AttributeTemplate[]>([])
const attributeGroups = ref<any[]>([])

// Computed properties
const isVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const availableTemplates = computed(() => {
  const filter: AttributeTemplateFilter = {
    searchQuery: searchQuery.value || undefined,
    groupId: selectedGroupId.value || undefined,
    dataType: selectedDataType.value || undefined,
    excludeTemplateIds: props.existingAttributes.map(attr => attr.templateId)
  }
  
  return getAvailableTemplates(allTemplates.value, props.existingAttributes, filter)
})

const selectedTemplate = computed(() => {
  return allTemplates.value.find(t => t.id === formData.value.templateId) || null
})

const isFormValid = computed(() => {
  return formData.value.templateId > 0 && 
         formData.value.value !== null && 
         formData.value.value !== '' &&
         validationErrors.value.length === 0
})

const dataTypeOptions = computed(() => {
  const types: AttributeDataType[] = ['STRING', 'NUMBER', 'BOOLEAN', 'DATE', 'JSON']
  return types.map(type => ({
    label: getDataTypeDisplayName(type),
    value: type
  }))
})

const groupOptions = computed(() => {
  return [
    { label: 'Все группы', value: null },
    ...attributeGroups.value.map(group => ({
      label: group.name,
      value: group.id
    }))
  ]
})

// Methods
async function loadTemplates() {
  loading.value = true
  try {
    const templates = await $trpc.crud.attributeTemplate.findMany.query({
      include: {
        group: true
      },
      orderBy: [
        { group: { name: 'asc' } },
        { title: 'asc' }
      ]
    })
    
    allTemplates.value = templates as AttributeTemplate[]
  } catch (error: any) {
    toast.error('Не удалось загрузить шаблоны атрибутов')
    console.error('Failed to load templates:', error)
  } finally {
    loading.value = false
  }
}

async function loadAttributeGroups() {
  try {
    const groups = await $trpc.crud.attributeGroup.findMany.query({
      orderBy: { name: 'asc' }
    })
    attributeGroups.value = groups
  } catch (error: any) {
    console.error('Failed to load attribute groups:', error)
  }
}

function selectTemplate(template: AttributeTemplate) {
  formData.value.templateId = template.id
  formData.value.template = template
  
  // Reset value when template changes
  resetValue()
  
  // Clear search to show selected template
  searchQuery.value = ''
}

function resetValue() {
  const template = selectedTemplate.value
  if (!template) {
    formData.value.value = ''
    return
  }
  
  // Set default value based on data type
  switch (template.dataType) {
    case 'STRING':
      formData.value.value = ''
      break
    case 'NUMBER':
      formData.value.value = template.minValue || 0
      break
    case 'BOOLEAN':
      formData.value.value = false
      break
    case 'DATE':
      formData.value.value = new Date()
      break
    case 'JSON':
      formData.value.value = ''
      break
    default:
      formData.value.value = ''
  }
  
  validateValue()
}

function validateValue() {
  validationErrors.value = []
  
  const template = selectedTemplate.value
  if (!template || formData.value.value === null || formData.value.value === '') {
    return
  }
  
  try {
    const schema = createAttributeValueSchema(template)
    const convertedValue = convertValueToType(String(formData.value.value), template.dataType)
    schema.parse(convertedValue)
  } catch (error: any) {
    if (error.errors) {
      validationErrors.value = error.errors.map((err: any) => err.message)
    } else {
      validationErrors.value = ['Неверное значение атрибута']
    }
  }
}

async function handleSave() {
  if (!isFormValid.value) return
  
  saving.value = true
  try {
    const template = selectedTemplate.value!
    const convertedValue = convertValueToType(String(formData.value.value), template.dataType)
    
    const saveData: AttributeFormData = {
      templateId: formData.value.templateId,
      value: convertedValue,
      template: template
    }
    
    emit('save', saveData)
    handleCancel() // Reset form
  } catch (error: any) {
    toast.error('Ошибка при сохранении атрибута')
    console.error('Save error:', error)
  } finally {
    saving.value = false
  }
}

function handleCancel() {
  // Reset form
  formData.value = {
    templateId: 0,
    value: '',
    template: undefined
  }
  searchQuery.value = ''
  selectedGroupId.value = null
  selectedDataType.value = null
  validationErrors.value = []
  
  emit('cancel')
}

function clearFilters() {
  searchQuery.value = ''
  selectedGroupId.value = null
  selectedDataType.value = null
}

function getTemplateDisplayInfo(template: AttributeTemplate) {
  const parts = []
  
  if (template.group) {
    parts.push(template.group.name)
  }
  
  parts.push(getDataTypeDisplayName(template.dataType))
  
  if (template.unit) {
    parts.push(getUnitDisplayName(template.unit))
  }
  
  if (template.isRequired) {
    parts.push('Обязательный')
  }
  
  return parts.join(' • ')
}

// Watchers
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    loadTemplates()
    loadAttributeGroups()
  }
})

watch(() => formData.value.value, () => {
  validateValue()
}, { deep: true })

watch(() => formData.value.templateId, () => {
  validateValue()
})

// Initialize
onMounted(() => {
  if (props.visible) {
    loadTemplates()
    loadAttributeGroups()
  }
})
</script>

<template>
  <Dialog
    v-model:visible="isVisible"
    modal
    :closable="true"
    :draggable="false"
    class="w-full max-w-4xl"
    header="Добавить атрибут"
  >
    <div class="space-y-6">
      <!-- Template Selection Section -->
      <div>
        <h6 class="text-sm font-medium text-surface-900 dark:text-surface-0 mb-3">
          Выберите шаблон атрибута
        </h6>
        
        <!-- Filters -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-surface-50 dark:bg-surface-800 rounded-lg">
          <!-- Search -->
          <div class="relative">
            <InputText
              v-model="searchQuery"
              placeholder="Поиск по названию..."
              class="w-full pl-10"
            />
            <SearchIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-surface-400" />
          </div>
          
          <!-- Group Filter -->
          <Select
            v-model="selectedGroupId"
            :options="groupOptions"
            option-label="label"
            option-value="value"
            placeholder="Выберите группу"
            class="w-full"
            :show-clear="true"
          />
          
          <!-- Data Type Filter -->
          <Select
            v-model="selectedDataType"
            :options="dataTypeOptions"
            option-label="label"
            option-value="value"
            placeholder="Тип данных"
            class="w-full"
            :show-clear="true"
          />
        </div>
        
        <!-- Clear Filters Button -->
        <div class="flex justify-between items-center mb-4">
          <span class="text-sm text-surface-600 dark:text-surface-400">
            Найдено шаблонов: {{ availableTemplates.length }}
          </span>
          <Button
            @click="clearFilters"
            text
            size="small"
            class="text-sm"
          >
            <XIcon class="w-4 h-4 mr-1" />
            Очистить фильтры
          </Button>
        </div>
        
        <!-- Templates List -->
        <div class="max-h-64 overflow-y-auto border border-surface-200 dark:border-surface-700 rounded-lg">
          <div v-if="loading" class="p-4 text-center text-surface-500">
            <Icon name="pi pi-spinner pi-spin" class="mr-2 inline-block" />
            Загрузка шаблонов...
          </div>
          
          <div v-else-if="availableTemplates.length === 0" class="p-4 text-center text-surface-500">
            <InfoIcon class="w-5 h-5 mx-auto mb-2" />
            <p class="text-sm">Нет доступных шаблонов</p>
            <p class="text-xs mt-1">Попробуйте изменить фильтры или все шаблоны уже используются</p>
          </div>
          
          <div v-else class="divide-y divide-surface-200 dark:divide-surface-700">
            <div
              v-for="template in availableTemplates"
              :key="template.id"
              class="p-4 hover:bg-surface-50 dark:hover:bg-surface-800 cursor-pointer transition-colors"
              :class="{
                'bg-primary-50 dark:bg-primary-900/20 border-l-4 border-primary': formData.templateId === template.id
              }"
              @click="selectTemplate(template)"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-1">
                    <h6 class="font-medium text-surface-900 dark:text-surface-0">
                      {{ template.title }}
                    </h6>
                    <Tag 
                      v-if="template.isRequired" 
                      severity="danger" 
                      class="text-xs"
                    >
                      Обязательный
                    </Tag>
                  </div>
                  
                  <p class="text-sm text-surface-600 dark:text-surface-400 mb-2">
                    {{ getTemplateDisplayInfo(template) }}
                  </p>
                  
                  <p 
                    v-if="template.description" 
                    class="text-xs text-surface-500 dark:text-surface-400 line-clamp-2"
                  >
                    {{ template.description }}
                  </p>
                </div>
                
                <div v-if="formData.templateId === template.id" class="ml-4">
                  <i class="pi pi-check text-primary text-lg"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Value Input Section -->
      <div v-if="selectedTemplate">
        <h6 class="text-sm font-medium text-surface-900 dark:text-surface-0 mb-3">
          Введите значение для "{{ selectedTemplate.title }}"
        </h6>
        
        <!-- Template Info -->
        <div class="p-3 bg-surface-50 dark:bg-surface-800 rounded-lg mb-4">
          <div class="flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400">
            <InfoIcon class="w-4 h-4" />
            <span>{{ getTemplateDisplayInfo(selectedTemplate) }}</span>
          </div>
          <p v-if="selectedTemplate.description" class="text-xs text-surface-500 dark:text-surface-400 mt-1">
            {{ selectedTemplate.description }}
          </p>
        </div>
        
        <!-- Value Input using universal component -->
        <div class="space-y-4">
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Значение
          </label>
          <AttributeValueInput
            v-model="formData.value"
            :template="selectedTemplate"
            placeholder="Введите значение атрибута"
            class="w-full"
          />
        </div>
        
        <!-- Validation Errors -->
        <div v-if="validationErrors.length > 0" class="mt-4">
          <Message
            v-for="error in validationErrors"
            :key="error"
            severity="error"
            :closable="false"
            class="mb-2"
          >
            {{ error }}
          </Message>
        </div>
      </div>
    </div>
    
    <!-- Dialog Footer -->
    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          @click="handleCancel"
          outlined
          :disabled="saving"
        >
          Отмена
        </Button>
        <Button
          @click="handleSave"
          :disabled="!isFormValid || saving"
          :loading="saving"
        >
          {{ saving ? 'Сохранение...' : 'Добавить атрибут' }}
        </Button>
      </div>
    </template>
  </Dialog>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>