<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { PlusIcon, ChevronDownIcon, ChevronUpIcon, TableIcon, GridIcon, TagsIcon } from 'lucide-vue-next'
import Button from '@/volt/Button.vue'
import Tag from '@/volt/Tag.vue'
import Card from '@/volt/Card.vue'
import AutoComplete from '@/volt/AutoComplete.vue'
import Dialog from '@/volt/Dialog.vue'
import EquipmentAttributesList from './EquipmentAttributesList.vue'
import AddEquipmentAttributeDialog from './AddEquipmentAttributeDialog.vue'
import type { EquipmentModelAttributeWithTemplate, AttributeFormData } from '@/types/attributes'
import { groupAttributes, formatAttributeValue, getDataTypeDisplayName } from '@/utils/attributes'
import { useTrpc } from '@/composables/useTrpc'
import Icon from '@/components/ui/Icon.vue'

interface Props {
  equipmentId: string
  attributes?: EquipmentModelAttributeWithTemplate[]
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits<{
  'add-attribute': [data: AttributeFormData]
  'edit-attribute': [attributeId: number]
  'delete-attribute': [attributeId: number]
}>()

// tRPC client
const { client } = useTrpc()

// State for compact/expanded view
const showAllAttributes = ref(false)
const COMPACT_LIMIT = 6 // Show only first 6 attributes in compact mode

// State for view mode (grid or table)
const viewMode = ref<'grid' | 'table'>('grid')

// Dialog state
const showAddDialog = ref(false)
const showGroupDialog = ref(false)

// Quick add state
const selectedTemplateGroup = ref<any>(null)
const selectedTemplate = ref<any>(null)
const groupSuggestions = ref<any[]>([])
const templateSuggestions = ref<any[]>([])
const templateGroups = ref<any[]>([])
const loadingTemplates = ref(false)

// Computed properties
const groupedAttributes = computed(() => {
  if (!props.attributes) return {}
  return groupAttributes(props.attributes)
})

const hasAttributes = computed(() => {
  return props.attributes && props.attributes.length > 0
})

const totalAttributesCount = computed(() => {
  return props.attributes?.length || 0
})

const shouldShowExpandButton = computed(() => {
  return totalAttributesCount.value > COMPACT_LIMIT
})

const visibleAttributes = computed(() => {
  if (!props.attributes) return []
  
  if (showAllAttributes.value || totalAttributesCount.value <= COMPACT_LIMIT) {
    return props.attributes
  }
  
  return props.attributes.slice(0, COMPACT_LIMIT)
})

const visibleGroupedAttributes = computed(() => {
  if (!visibleAttributes.value.length) return {}
  return groupAttributes(visibleAttributes.value)
})

const hiddenAttributesCount = computed(() => {
  return Math.max(0, totalAttributesCount.value - COMPACT_LIMIT)
})

const filledAttributesCount = computed(() => {
  return props.attributes?.filter(attr => attr.value && String(attr.value).trim()).length || 0
})

// Methods
function handleAddAttribute() {
  showAddDialog.value = true
}

function handleSaveAttribute(data: AttributeFormData) {
  emit('add-attribute', data)
  showAddDialog.value = false
}

function handleCancelAddAttribute() {
  showAddDialog.value = false
}

function handleEditAttribute(attributeId: number) {
  emit('edit-attribute', attributeId)
}

function handleDeleteAttribute(attributeId: number) {
  emit('delete-attribute', attributeId)
}

function toggleShowAll() {
  showAllAttributes.value = !showAllAttributes.value
}

function toggleViewMode() {
  viewMode.value = viewMode.value === 'grid' ? 'table' : 'grid'
}

function formatAttribute(attribute: EquipmentModelAttributeWithTemplate) {
  return formatAttributeValue(attribute)
}

// Check if user has admin rights (simplified - in real app would check actual permissions)
const canManageAttributes = computed(() => {
  return !props.readonly
})

// Quick add methods
async function filterGroups(event: any) {
  const query = event.query.toLowerCase()
  try {
    const groups = await client.crud.attributeGroup.findMany.query({
      where: {
        name: {
          contains: query,
          mode: 'insensitive'
        }
      },
      take: 10
    })
    groupSuggestions.value = groups || []
  } catch (error) {
    console.error('Error filtering groups:', error)
    groupSuggestions.value = []
  }
}

async function filterTemplates(event: any) {
  const query = event.query.toLowerCase()
  try {
    const templates = await client.crud.attributeTemplate.findMany.query({
      where: {
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { name: { contains: query, mode: 'insensitive' } }
        ]
      },
      include: {
        group: true
      },
      take: 10
    })
    templateSuggestions.value = templates || []
  } catch (error) {
    console.error('Error filtering templates:', error)
    templateSuggestions.value = []
  }
}

async function loadSelectedGroupTemplates() {
  if (!selectedTemplateGroup.value) return
  
  loadingTemplates.value = true
  try {
    const groupId = selectedTemplateGroup.value.id || selectedTemplateGroup.value
    const templates = await client.crud.attributeTemplate.findMany.query({
      where: { groupId },
      include: { group: true }
    })
    
    if (templates) {
      // Add templates as new attributes
      for (const template of templates) {
        const attributeData: AttributeFormData = {
          templateId: template.id,
          value: getDefaultValueForType(template.dataType),
          template: template
        }
        emit('add-attribute', attributeData)
      }
    }
    
    selectedTemplateGroup.value = null
  } catch (error) {
    console.error('Error loading group templates:', error)
  } finally {
    loadingTemplates.value = false
  }
}

function addSingleTemplate() {
  if (!selectedTemplate.value) return
  
  const template = selectedTemplate.value
  const attributeData: AttributeFormData = {
    templateId: template.id,
    value: getDefaultValueForType(template.dataType),
    template: template
  }
  
  emit('add-attribute', attributeData)
  selectedTemplate.value = null
}

async function loadTemplatesByGroupId(groupId: number) {
  loadingTemplates.value = true
  try {
    const templates = await client.crud.attributeTemplate.findMany.query({
      where: { groupId },
      include: { group: true }
    })
    
    if (templates) {
      for (const template of templates) {
        const attributeData: AttributeFormData = {
          templateId: template.id,
          value: getDefaultValueForType(template.dataType),
          template: template
        }
        emit('add-attribute', attributeData)
      }
    }
    
    showGroupDialog.value = false
  } catch (error) {
    console.error('Error loading templates by group:', error)
  } finally {
    loadingTemplates.value = false
  }
}

function getDefaultValueForType(dataType: string): any {
  switch (dataType) {
    case 'STRING': return ''
    case 'NUMBER': return 0
    case 'BOOLEAN': return false
    case 'DATE': return new Date()
    case 'JSON': return ''
    default: return ''
  }
}

function getDataTypeIcon(dataType: string) {
  const icons: Record<string, string> = {
    STRING: 'pi pi-font',
    NUMBER: 'pi pi-hashtag',
    BOOLEAN: 'pi pi-check-square',
    DATE: 'pi pi-calendar',
    JSON: 'pi pi-code'
  }
  return icons[dataType] || 'pi pi-question'
}

// Load template groups on mount
onMounted(async () => {
  try {
    const groups = await client.crud.attributeGroup.findMany.query({
      include: {
        _count: {
          select: { templates: true }
        }
      },
      orderBy: { name: 'asc' }
    })
    templateGroups.value = groups || []
  } catch (error) {
    console.error('Error loading template groups:', error)
  }
})
</script>

<template>
  <div class="equipment-attributes-section">
    <!-- Section Header -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-3">
        <h5 class="font-semibold flex items-center gap-2 text-surface-900 dark:text-surface-0">
          <Icon name="pi pi-list" class="text-green-600 w-4 h-4" />
          Атрибуты модели
        </h5>
        <Tag
          v-if="totalAttributesCount > 0"
          :value="`${filledAttributesCount}/${totalAttributesCount} заполнено`"
          :severity="filledAttributesCount === totalAttributesCount ? 'success' : 'warn'"
          size="small"
        />
      </div>
      
      <div class="flex items-center gap-2">
        <!-- View Mode Toggle -->
        <Button
          v-if="hasAttributes"
          @click="toggleViewMode"
          text
          size="small"
          :title="viewMode === 'grid' ? 'Переключить на табличный вид' : 'Переключить на сеточный вид'"
        >
          <TableIcon v-if="viewMode === 'grid'" class="w-4 h-4" />
          <GridIcon v-else class="w-4 h-4" />
        </Button>
        
        <!-- Add Group Button -->
        <Button
          v-if="canManageAttributes"
          @click="showGroupDialog = true"
          outlined
          severity="secondary"
          size="small"
          class="text-sm"
        >
          <TagsIcon class="w-4 h-4 mr-1" />
          Добавить группу
        </Button>
        
        <!-- Add Attribute Button -->
        <Button
          v-if="canManageAttributes"
          @click="handleAddAttribute"
          size="small"
          class="text-sm"
        >
          <PlusIcon class="w-4 h-4 mr-1" />
          Добавить атрибут
        </Button>
      </div>
    </div>

    <!-- Quick Add Section -->
    <Card v-if="canManageAttributes" class="mb-4">
      <template #content>
        <div class="p-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                Группа шаблонов
              </label>
              <AutoComplete
                v-model="selectedTemplateGroup"
                :suggestions="groupSuggestions"
                @complete="filterGroups"
                option-label="name"
                placeholder="Поиск группы..."
                class="w-full"
                dropdown
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                Или отдельный шаблон
              </label>
              <AutoComplete
                v-model="selectedTemplate"
                :suggestions="templateSuggestions"
                @complete="filterTemplates"
                option-label="title"
                placeholder="Поиск шаблона..."
                class="w-full"
                dropdown
              >
                <template #option="{ option }">
                  <div class="flex items-center gap-2">
                     <Icon :name="getDataTypeIcon(option.dataType)" class="text-primary w-4 h-4" />
                    <div class="flex-1">
                      <div class="font-medium">{{ option.title }}</div>
                      <div class="text-sm text-surface-600">
                        {{ option.group?.name }} • {{ getDataTypeDisplayName(option.dataType) }}
                      </div>
                    </div>
                  </div>
                </template>
              </AutoComplete>
            </div>
            <div class="flex items-end gap-2">
              <Button
                @click="loadSelectedGroupTemplates"
                size="small"
                outlined
                :disabled="!selectedTemplateGroup || loadingTemplates"
                :loading="loadingTemplates"
                label="Добавить группу"
                class="flex-1"
              />
              <Button
                @click="addSingleTemplate"
                size="small"
                outlined
                :disabled="!selectedTemplate"
                label="Добавить"
                class="flex-1"
              />
            </div>
          </div>
        </div>
      </template>
    </Card>

    <!-- Attributes Content -->
    <div v-if="hasAttributes">
      <!-- Table View -->
      <div v-if="viewMode === 'table'">
        <EquipmentAttributesList
          :attributes="visibleAttributes"
          :readonly="readonly"
          :compact="!showAllAttributes && shouldShowExpandButton"
          @edit-attribute="handleEditAttribute"
          @delete-attribute="handleDeleteAttribute"
        />
        
        <!-- Show More/Less Button for Table View -->
        <div v-if="shouldShowExpandButton" class="text-center pt-4">
          <Button
            @click="toggleShowAll"
            text
            size="small"
            class="text-sm text-surface-600 dark:text-surface-400 hover:text-surface-900 dark:hover:text-surface-0"
          >
            <template v-if="!showAllAttributes">
              <ChevronDownIcon class="w-4 h-4 mr-1" />
              Показать все (еще {{ hiddenAttributesCount }})
            </template>
            <template v-else>
              <ChevronUpIcon class="w-4 h-4 mr-1" />
              Свернуть
            </template>
          </Button>
        </div>
      </div>

      <!-- Grid View (Original) -->
      <div v-else>
        <!-- Grouped Attributes Display -->
        <div 
          v-for="(groupAttributes, groupName) in visibleGroupedAttributes"
          :key="groupName"
          class="attribute-group mb-4"
        >
          <!-- Group Header -->
          <div class="mb-3 pb-2 border-b border-surface-200 dark:border-surface-700">
            <h6 class="text-sm font-medium text-surface-700 dark:text-surface-300 flex items-center gap-2">
              <Icon name="pi pi-folder" class="w-3 h-3" />
              {{ groupName }}
              <Tag 
                severity="info" 
                :value="groupAttributes.length.toString()"
                class="text-xs"
              />
            </h6>
          </div>
          
          <!-- Attributes Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <div
              v-for="attribute in groupAttributes"
              :key="attribute.id"
              class="attribute-card p-3 bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 hover:shadow-sm transition-shadow"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <!-- Attribute Title -->
                  <div class="flex items-center gap-2 mb-1">
                    <span class="text-sm font-medium text-surface-900 dark:text-surface-0 truncate">
                      {{ attribute.template.title }}
                    </span>
                    <Tag 
                      v-if="attribute.template.isRequired" 
                      severity="danger" 
                      class="text-xs flex-shrink-0"
                    >
                      Обязательный
                    </Tag>
                  </div>
                  
                  <!-- Attribute Value -->
                  <div class="text-sm text-surface-600 dark:text-surface-400 mb-2 break-words">
                    {{ formatAttribute(attribute).displayValue }}
                  </div>
                  
                  <!-- Attribute Description (if exists) -->
                  <div 
                    v-if="attribute.template.description" 
                    class="text-xs text-surface-500 dark:text-surface-400 line-clamp-2"
                    :title="attribute.template.description"
                  >
                    {{ attribute.template.description }}
                  </div>
                </div>
                
                <!-- Action Buttons -->
                <div v-if="canManageAttributes" class="ml-2 flex flex-col gap-1 flex-shrink-0">
                  <Button
                    @click="handleEditAttribute(attribute.id)"
                    text
                    size="small"
                    class="p-1 text-xs"
                    :title="`Редактировать ${attribute.template.title}`"
                  >
                    <Icon name="pi pi-pencil" class="w-3 h-3" />
                  </Button>
                  <Button
                    @click="handleDeleteAttribute(attribute.id)"
                    text
                    severity="danger"
                    size="small"
                    class="p-1 text-xs"
                    :title="`Удалить ${attribute.template.title}`"
                    :disabled="attribute.template.isRequired"
                  >
                    <Icon name="pi pi-trash" class="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Show More/Less Button for Grid View -->
        <div v-if="shouldShowExpandButton" class="text-center pt-2">
          <Button
            @click="toggleShowAll"
            text
            size="small"
            class="text-sm text-surface-600 dark:text-surface-400 hover:text-surface-900 dark:hover:text-surface-0"
          >
            <template v-if="!showAllAttributes">
              <ChevronDownIcon class="w-4 h-4 mr-1" />
              Показать все (еще {{ hiddenAttributesCount }})
            </template>
            <template v-else>
              <ChevronUpIcon class="w-4 h-4 mr-1" />
              Свернуть
            </template>
          </Button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-6 text-surface-500 dark:text-surface-400">
      <Icon name="pi pi-info-circle" class="text-2xl mb-2 inline-block" />
      <p class="text-sm mb-3">У данной модели техники нет атрибутов</p>
      <Button
        v-if="canManageAttributes"
        @click="handleAddAttribute"
        outlined
        size="small"
      >
        <PlusIcon class="w-4 h-4 mr-1" />
        Добавить первый атрибут
      </Button>
    </div>

    <!-- Add Attribute Dialog -->
    <AddEquipmentAttributeDialog
      v-model:visible="showAddDialog"
      :equipment-id="equipmentId"
      :existing-attributes="attributes || []"
      @save="handleSaveAttribute"
      @cancel="handleCancelAddAttribute"
    />

    <!-- Group Selection Dialog -->
    <Dialog
      v-model:visible="showGroupDialog"
      modal
      header="Выбор группы шаблонов"
      :style="{ width: '40rem' }"
    >
      <div class="space-y-4">
        <div v-for="group in templateGroups" :key="group.id" class="border border-surface-200 dark:border-surface-700 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="font-medium text-surface-900 dark:text-surface-0">{{ group.name }}</h4>
              <p class="text-sm text-surface-600 dark:text-surface-400">{{ group.description }}</p>
              <small class="text-surface-500">{{ group._count?.templates || 0 }} шаблонов</small>
            </div>
            <Button
              @click="loadTemplatesByGroupId(group.id)"
              size="small"
              :loading="loadingTemplates"
            >
              Добавить все
            </Button>
          </div>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<style scoped>
.equipment-attributes-section {
  /* space-y-4 equivalent */
}

.equipment-attributes-section > * + * {
  margin-top: 1rem;
}

.attribute-group {
  /* space-y-3 equivalent */
}

.attribute-group > * + * {
  margin-top: 0.75rem;
}

.attribute-card {
  transition: all 0.2s;
}

.attribute-card:hover {
  border-color: rgb(209 213 219);
}

.dark .attribute-card:hover {
  border-color: rgb(75 85 99);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>