<script setup lang="ts">
import { trpc } from '@/lib/trpc'
import Button from '@/volt/Button.vue'
import DataTable from '@/volt/DataTable.vue'
import { PencilIcon, TrashIcon, PlusIcon, ExternalLinkIcon } from 'lucide-vue-next'
import Column from 'primevue/column'
import EditEquipmentDialog from './EditEquipmentDialog.vue'
import EquipmentAttributesSection from './EquipmentAttributesSection.vue'
import { ref, watch } from 'vue'
import { navigate } from 'astro:transitions/client'
import type { EquipmentModelWithCount, EquipmentModelFormData } from '@/types/equipment'
import type { EquipmentModelAttributeWithTemplate } from '@/types/attributes'
import InputText from '@/volt/InputText.vue'
import Tag from '@/volt/Tag.vue'
import Icon from '@/components/ui/Icon.vue'

const searchValue = ref('')
const dialogVisible = ref(false)
const editingEquipment = ref<EquipmentModelFormData | null>(null)
const expandedRows = ref<EquipmentModelWithCount[]>([])
const equipmentPartsCache = ref<Record<string, any[]>>({})
const equipmentAttributesCache = ref<Record<string, any[]>>({})

const props = defineProps<{
  initialData: EquipmentModelWithCount[]
}>()

const items = ref(props.initialData)

// Тип для ключей EquipmentModelWithCount, которые мы хотим отображать в таблице
type DisplayableEquipmentKey = keyof Omit<EquipmentModelWithCount, '_count' | 'brand' | 'partApplicabilities' | 'attributes'>

const keyMapping: Record<DisplayableEquipmentKey, string> = {
  id: 'ID',
  name: 'Наименование',
  brandId: 'Бренд ID',
  createdAt: 'Создано',
  updatedAt: 'Обновлено',
}

// Явный список ключей для колонок, чтобы избежать зависимости от runtime данных
const columnKeys: DisplayableEquipmentKey[] = [
  'id',
  'name',
  'createdAt',
]

function createEquipment() {
  editingEquipment.value = {} // Пустой объект для создания
  dialogVisible.value = true
}

function editEquipment(data: EquipmentModelWithCount) {
  editingEquipment.value = { ...data }
  dialogVisible.value = true
}

async function handleSave(equipmentData: EquipmentModelFormData) {
  if (!equipmentData) return;

  try {
    if (equipmentData.id) {
      // Обновление существующей модели техники
      const { id, ...dataToUpdate } = equipmentData
      await trpc.crud.equipmentModel.update.mutate({
        where: { id },
        data: dataToUpdate,
      })
    } else {
      // Создание новой модели техники
      // Убедимся, что все обязательные поля присутствуют
      if (equipmentData.name) {
        await trpc.crud.equipmentModel.create.mutate({
          data: {
            name: equipmentData.name,
            brandId: equipmentData.brandId || null
          },
        })
      } else {
        console.error("Name is required to create an equipment model.")
        return
      }
    }
    // Перезагрузка страницы для отображения изменений
    navigate(window.location.href)
  } catch (error) {
    console.error('Failed to save equipment model:', error)
  } finally {
    dialogVisible.value = false
  }
}

/**
 * Обработчик события отмены редактирования
 */
function handleCancel() {
  dialogVisible.value = false
  editingEquipment.value = null
}

async function deleteEquipment(data: EquipmentModelWithCount) {
  dialogVisible.value = false
  // Здесь можно добавить диалог подтверждения удаления
  await trpc.crud.equipmentModel.delete.mutate({
    where: {
      id: data.id,
    },
  })

  navigate(window.location.href)
}

watch(searchValue, (newValue) => {
  debouncedSearch(newValue)
})

async function debouncedSearch(value = '') {
  console.log('value', value)

  items.value = await trpc.crud.equipmentModel.findMany.query({
    where: {
      OR: [
        {
          name: {
            contains: value,
          }
        },
        {
          brand: {
            name: {
              contains: value,
            }
          }
        },
      ],
    },
    include: {
      brand: {
        select: {
          name: true
        }
      },
      _count: {
        select: {
          partApplicabilities: true,
          attributes: true,
        }
      }
    }
  })
}

// Функция для форматирования даты
function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('ru-RU', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Функция для загрузки запчастей для конкретной модели техники
async function loadEquipmentParts(equipmentId: string) {
  if (equipmentPartsCache.value[equipmentId]) {
    return equipmentPartsCache.value[equipmentId]
  }

  try {
    const parts = await trpc.crud.equipmentApplicability.findMany.query({
      where: {
        equipmentModelId: equipmentId
      },
      include: {
        part: {
          include: {
            partCategory: true,
            applicabilities: {
              include: {
                catalogItem: {
                  include: {
                    brand: true
                  }
                }
              }
            },
            _count: {
              select: {
                attributes: true,
                applicabilities: true
              }
            }
          }
        }
      },
      orderBy: {
        part: {
          name: 'asc'
        }
      }
    })

    equipmentPartsCache.value[equipmentId] = parts || []
    return parts || []
  } catch (error) {
    console.error('Failed to load equipment parts:', error)
    return []
  }
}

// Функция для загрузки атрибутов для конкретной модели техники
async function loadEquipmentAttributes(equipmentId: string): Promise<EquipmentModelAttributeWithTemplate[]> {
  if (equipmentAttributesCache.value[equipmentId]) {
    return equipmentAttributesCache.value[equipmentId]
  }

  try {
    const attributes = await trpc.crud.equipmentModelAttribute.findMany.query({
      where: {
        equipmentModelId: equipmentId
      },
      include: {
        template: {
          include: {
            group: true
          }
        }
      },
      orderBy: [
        {
          template: {
            group: {
              name: 'asc'
            }
          }
        },
        {
          template: {
            title: 'asc'
          }
        }
      ]
    })

    const attributesWithTemplate = attributes.map(attr => ({
      ...attr,
      template: {
        ...attr.template,
        group: attr.template.group
      }
    })) as EquipmentModelAttributeWithTemplate[]

    equipmentAttributesCache.value[equipmentId] = attributesWithTemplate
    return attributesWithTemplate
  } catch (error) {
    console.error('Failed to load equipment attributes:', error)
    return []
  }
}

// Обработчик события разворачивания строки
async function onRowExpand(event: { data: EquipmentModelWithCount }) {
  // Загружаем запчасти если они есть
  if (event.data._count.partApplicabilities > 0) {
    await loadEquipmentParts(event.data.id)
  }
  
  // Загружаем атрибуты если они есть
  if (event.data._count.attributes > 0) {
    await loadEquipmentAttributes(event.data.id)
  }
}

// Функция для получения метки точности применимости
function getAccuracyLabel(accuracy: string) {
  const labels: Record<string, string> = {
    'EXACT_MATCH': 'Точное совпадение',
    'MATCH_WITH_NOTES': 'С примечаниями', 
    'REQUIRES_MODIFICATION': 'Требует доработки',
    'PARTIAL_MATCH': 'Частичное совпадение'
  }
  return labels[accuracy] || accuracy
}

// Функция для получения цвета тега точности
function getAccuracySeverity(accuracy: string) {
  const severities: Record<string, string> = {
    'EXACT_MATCH': 'success',
    'MATCH_WITH_NOTES': 'info',
    'REQUIRES_MODIFICATION': 'warning', 
    'PARTIAL_MATCH': 'secondary'
  }
  return severities[accuracy] || 'secondary'
}

// Функция для перехода к редактированию запчасти
function editPart(partId: number) {
  // Пока что просто логируем, позже можно будет добавить навигацию
  console.log('Edit part:', partId)
  // navigate(`/admin/parts/${partId}/edit`)
}

// Функция для группировки атрибутов по группам
function groupAttributesByGroup(attributes: EquipmentModelAttributeWithTemplate[]) {
  const grouped: Record<string, EquipmentModelAttributeWithTemplate[]> = {}
  
  attributes.forEach(attribute => {
    const groupName = attribute.template.group?.name || 'Общие'
    
    if (!grouped[groupName]) {
      grouped[groupName] = []
    }
    
    grouped[groupName].push(attribute)
  })
  
  return grouped
}

// Функция для форматирования значения атрибута
function formatAttributeValue(attribute: EquipmentModelAttributeWithTemplate) {
  const { value, template } = attribute
  const { dataType, unit } = template
  
  switch (dataType) {
    case 'STRING':
      return value
    case 'NUMBER':
      const num = parseFloat(value)
      const formatted = isNaN(num) ? value : num.toLocaleString('ru-RU')
      return unit ? `${formatted} ${getUnitDisplayName(unit)}` : formatted
    case 'BOOLEAN':
      return value.toLowerCase() === 'true' ? 'Да' : 'Нет'
    case 'DATE':
      const date = new Date(value)
      return isNaN(date.getTime()) ? value : date.toLocaleDateString('ru-RU')
    case 'JSON':
      try {
        return JSON.stringify(JSON.parse(value), null, 2)
      } catch {
        return value
      }
    default:
      return value
  }
}

// Функция для получения отображаемого названия единицы измерения
function getUnitDisplayName(unit: string | null | undefined): string {
  if (!unit) return ''
  
  const unitNames: Record<string, string> = {
    MM: 'мм',
    INCH: 'дюйм',
    FT: 'фт',
    G: 'г',
    KG: 'кг',
    T: 'т',
    LB: 'фунт',
    ML: 'мл',
    L: 'л',
    GAL: 'гал',
    SEC: 'сек',
    MIN: 'мин',
    H: 'ч',
    PCS: 'шт',
    SET: 'комплект',
    PAIR: 'пара',
    BAR: 'бар',
    PSI: 'psi',
    KW: 'кВт',
    HP: 'л.с.',
    NM: 'Н⋅м',
    RPM: 'об/мин',
    C: '°C',
    F: '°F',
    PERCENT: '%'
  }
  
  return unitNames[unit] || unit
}

// Функция для разворачивания строки при клике на количество атрибутов
async function expandRowForAttributes(data: EquipmentModelWithCount) {
  // Проверяем, не развернута ли уже строка
  const isExpanded = expandedRows.value.some(row => row.id === data.id)
  
  if (!isExpanded) {
    // Добавляем строку в развернутые
    expandedRows.value.push(data)
    
    // Загружаем атрибуты если их еще нет в кэше
    if (data._count.attributes > 0 && !equipmentAttributesCache.value[data.id]) {
      await loadEquipmentAttributes(data.id)
    }
  }
}

// Обработчики событий для управления атрибутами
async function handleAddAttribute(data: any) {
  try {
    // Create the attribute using tRPC
    await trpc.crud.equipmentModelAttribute.create.mutate({
      data: {
        equipmentModelId: data.equipmentId || data.equipmentModelId,
        templateId: data.templateId,
        value: String(data.value)
      }
    })
    
    // Clear the cache for this equipment to force reload
    if (data.equipmentId) {
      delete equipmentAttributesCache.value[data.equipmentId]
      // Reload attributes
      await loadEquipmentAttributes(data.equipmentId)
    }
    
    console.log('Attribute added successfully')
  } catch (error) {
    console.error('Failed to add attribute:', error)
  }
}

function handleEditAttribute(attributeId: number) {
  // TODO: Открыть диалог редактирования атрибута
  console.log('Edit attribute:', attributeId)
}

function handleDeleteAttribute(attributeId: number) {
  // TODO: Удалить атрибут с подтверждением
  console.log('Delete attribute:', attributeId)
}
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-2xl font-bold">Модели техники</h1>
      <Button @click="createEquipment">
        <PlusIcon class="w-5 h-5 mr-2" />
        Создать модель
      </Button>
    </div>

    <DataTable 
      show-headers 
      :value="items" 
      v-model:expandedRows="expandedRows"
      @row-expand="onRowExpand"
      :rowHover="true"
    >
      <template #header>
        <div class="flex justify-end">
          <InputText v-model="searchValue" placeholder="Поиск" />
        </div>
      </template>
      
      <!-- Колонка для разворачивания -->
      <Column :expander="true" headerStyle="width: 3rem" />
      
      <Column
        v-for="key in columnKeys"
        :key="key"
        :field="key"
        :header="keyMapping[key] || key"
      >
        <template #body="{ data }" v-if="key === 'createdAt'">
          {{ formatDate(data[key]) }}
        </template>
        <template #body="{ data }" v-else-if="key === 'id'">
          <div class="font-mono text-sm">
            {{ data[key].substring(0, 8) }}...
          </div>
        </template>
      </Column>
      
      <!-- Отдельная колонка для бренда -->
      <Column field="brand.name" header="Бренд">
        <template #body="{ data }">
          {{ data.brand?.name || '-' }}
        </template>
      </Column>
      
      <!-- Отдельные колонки для вложенных свойств -->
      <Column field="_count.partApplicabilities" header="Применимость деталей">
        <template #body="{ data }">
          <Tag 
            v-if="data._count.partApplicabilities > 0" 
            severity="info" 
            :value="data._count.partApplicabilities.toString()"
          />
          <span v-else class="text-surface-500">0</span>
        </template>
      </Column>
      <Column field="_count.attributes" header="Атрибуты">
        <template #body="{ data }">
          <Tag 
            v-if="data._count.attributes > 0" 
            severity="secondary" 
            :value="data._count.attributes.toString()"
            class="cursor-pointer hover:bg-surface-200 dark:hover:bg-surface-700 transition-colors"
            @click="expandRowForAttributes(data)"
          />
          <span v-else class="text-surface-500">0</span>
        </template>
      </Column>

      <Column header="Действия">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button @click="editEquipment(data)" outlined size="small">
              <PencilIcon class="w-5 h-5" />
            </Button>
            <Button
              @click="deleteEquipment(data)"
              outlined
              severity="danger"
              size="small"
            >
              <TrashIcon class="w-5 h-5" />
            </Button>
          </div>
        </template>
      </Column>

      <!-- Шаблон для развернутого содержимого -->
      <template #expansion="{ data }">
        <div class="p-4 bg-surface-50 dark:bg-surface-800">
          <!-- Секция атрибутов -->
          <div v-if="data._count.attributes > 0 || true" class="mb-6">
            <EquipmentAttributesSection
              :equipment-id="data.id"
              :attributes="equipmentAttributesCache[data.id]"
              @add-attribute="(attributeData) => handleAddAttribute({ ...attributeData, equipmentId: data.id })"
              @edit-attribute="handleEditAttribute"
              @delete-attribute="handleDeleteAttribute"
            />
          </div>

          <!-- Секция запчастей -->
          <div v-if="data._count.partApplicabilities > 0">
            <h5 class="mb-3 font-semibold flex items-center gap-2">
              <Icon name="pi pi-wrench" class="text-blue-600 w-4 h-4" />
              Запчасти для: {{ data.name }}
            </h5>
            
            <div v-if="equipmentPartsCache[data.id] && equipmentPartsCache[data.id].length > 0">
              <div class="grid gap-3">
                <div
                  v-for="applicability in equipmentPartsCache[data.id]"
                  :key="applicability.id"
                  class="p-4 bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 hover:shadow-sm transition-shadow"
                >
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <!-- Название запчасти -->
                      <div class="flex items-center gap-2 mb-2">
                        <h6 class="font-medium text-surface-900 dark:text-surface-0">
                          {{ applicability.part?.name || 'Без названия' }}
                        </h6>
                        <Tag 
                          v-if="applicability.part?.partCategory" 
                          severity="info" 
                          class="text-xs"
                        >
                          {{ applicability.part.partCategory.name }}
                        </Tag>
                      </div>

                      <!-- Краткая информация о запчасти -->
                      <div class="flex items-center gap-4 text-sm text-surface-600 dark:text-surface-400 mb-2">
                        <span class="flex items-center gap-1">
                          <Icon name="pi pi-list" class="w-3 h-3" />
                          {{ applicability.part?._count?.attributes || 0 }} атрибутов
                        </span>
                        <span class="flex items-center gap-1">
                          <Icon name="pi pi-box" class="w-3 h-3" />
                          {{ applicability.part?._count?.applicabilities || 0 }} каталожных позиций
                        </span>
                      </div>

                      <!-- Примечания -->
                      <div v-if="applicability.notes" class="mt-2 p-2 bg-surface-100 dark:bg-surface-800 rounded text-sm text-surface-600 dark:text-surface-400">
                        <Icon name="pi pi-info-circle" class="mr-1 w-4 h-4 inline-block" />
                        {{ applicability.notes }}
                      </div>

                      <!-- Каталожные позиции (компактный вид) -->
                      <div v-if="applicability.part?.applicabilities?.length > 0" class="mt-3">
                        <div class="text-xs font-medium text-surface-700 dark:text-surface-300 mb-2">
                          Каталожные позиции:
                        </div>
                        <div class="flex flex-wrap gap-2">
                          <div
                            v-for="partApp in applicability.part.applicabilities.slice(0, 3)"
                            :key="partApp.id"
                            class="flex items-center gap-1 px-2 py-1 bg-surface-100 dark:bg-surface-800 rounded text-xs"
                          >
                            <span class="font-medium">{{ partApp.catalogItem?.sku }}</span>
                            <span class="text-surface-500">{{ partApp.catalogItem?.brand?.name }}</span>
                            <Tag 
                              :severity="getAccuracySeverity(partApp.accuracy)" 
                              class="text-xs"
                            >
                              {{ getAccuracyLabel(partApp.accuracy) }}
                            </Tag>
                          </div>
                          <div 
                            v-if="applicability.part.applicabilities.length > 3"
                            class="px-2 py-1 bg-surface-200 dark:bg-surface-700 rounded text-xs text-surface-600 dark:text-surface-400"
                          >
                            +{{ applicability.part.applicabilities.length - 3 }} еще
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Действия -->
                    <div class="ml-4 flex flex-col gap-2">
                      <Button 
                        @click="editPart(applicability.part.id)" 
                        outlined 
                        size="small"
                        class="text-xs"
                      >
                        <PencilIcon class="w-3 h-3 mr-1" />
                        Редактировать
                      </Button>
                      <Button 
                        @click="() => navigate(`/admin/parts/${applicability.part.id}`)" 
                        outlined 
                        severity="secondary"
                        size="small"
                        class="text-xs"
                      >
                        <ExternalLinkIcon class="w-3 h-3 mr-1" />
                        Подробнее
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div v-else class="text-center py-6 text-surface-500 dark:text-surface-400">
              <Icon name="pi pi-info-circle" class="text-2xl mb-2 inline-block" />
              Запчасти для данной модели техники не найдены
            </div>
          </div>

          <!-- Сообщение если нет ни атрибутов, ни запчастей -->
          <div v-if="data._count.attributes === 0 && data._count.partApplicabilities === 0" class="text-center py-6 text-surface-500 dark:text-surface-400">
            <Icon name="pi pi-info-circle" class="text-2xl mb-2 inline-block" />
            Для данной модели техники нет дополнительной информации
          </div>
        </div>
      </template>
    </DataTable>

    <EditEquipmentDialog
      v-model:isVisible="dialogVisible"
      :equipment="editingEquipment"
      @save="handleSave"
      @cancel="handleCancel"
    />
  </div>
</template>