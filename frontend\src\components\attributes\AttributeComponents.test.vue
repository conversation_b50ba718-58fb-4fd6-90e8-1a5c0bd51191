<template>
  <div class="p-6 space-y-8 max-w-6xl mx-auto">
    <h1>Attribute Components Test</h1>

    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">AttributeDisplay Component</h2>
      <AttributeDisplay
        entity-type="equipmentModel"
        entity-id="test-equipment-1"
        :attributes="mockAttributes"
        :show-filters="true"
      />
    </div>

    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">AttributeEditor Component</h2>
      <AttributeEditor
        entity-type="equipmentModel"
        entity-id="test-equipment-1"
        title="Редактор атрибутов"
        :readonly="false"
      />
    </div>

    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">AttributeFilter Component</h2>
      <AttributeFilter
        title="Фильтры атрибутов"
        entity-type="equipmentModel"
        :expanded="true"
        @filter-change="onFilterChange"
      />
    </div>

    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">AttributeTemplateSelector Component</h2>
      <AttributeTemplateSelector
        title="Выбор шаблонов"
        :available-templates="mockTemplates"
        :multi-select="true"
        :show-selection="true"
        :show-actions="true"
        @confirm="onTemplatesSelected"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type {
  EquipmentModelAttributeWithTemplate,
  AttributeTemplate
} from '@/types/attributes';
import {
  AttributeDisplay,
  AttributeEditor,
  AttributeFilter,
  AttributeTemplateSelector
} from './index';

// Mock data for testing
const mockAttributes = ref<EquipmentModelAttributeWithTemplate[]>([
  {
    id: 1,
    value: '150',
    numericValue: 150,
    equipmentModelId: 'test-equipment-1',
    templateId: 1,
    template: {
      id: 1,
      name: 'engine_power',
      title: 'Мощность двигателя',
      description: 'Номинальная мощность двигателя',
      dataType: 'NUMBER' as const,
      unit: 'KW' as const,
      isRequired: true,
      minValue: 50,
      maxValue: 500,
      allowedValues: [],
      tolerance: 5,
      groupId: 1,
      group: {
        id: 1,
        name: 'Двигатель',
        description: 'Характеристики двигателя'
      },
      synonymGroups: [],
      partAttributes: [],
      catalogItemAttributes: [],
      equipmentAttributes: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  },
  {
    id: 2,
    value: 'Дизельный',
    numericValue: null,
    equipmentModelId: 'test-equipment-1',
    templateId: 2,
    template: {
      id: 2,
      name: 'fuel_type',
      title: 'Тип топлива',
      description: 'Тип используемого топлива',
      dataType: 'STRING' as const,
      unit: null,
      isRequired: true,
      minValue: null,
      maxValue: null,
      allowedValues: ['Дизельный', 'Бензиновый', 'Электрический'],
      tolerance: 0,
      groupId: 1,
      group: {
        id: 1,
        name: 'Двигатель',
        description: 'Характеристики двигателя'
      },
      synonymGroups: [],
      partAttributes: [],
      catalogItemAttributes: [],
      equipmentAttributes: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  },
  {
    id: 3,
    value: 'true',
    numericValue: null,
    equipmentModelId: 'test-equipment-1',
    templateId: 3,
    template: {
      id: 3,
      name: 'has_turbo',
      title: 'Наличие турбонаддува',
      description: 'Оснащен ли двигатель турбонаддувом',
      dataType: 'BOOLEAN' as const,
      unit: null,
      isRequired: false,
      minValue: null,
      maxValue: null,
      allowedValues: [],
      tolerance: 0,
      groupId: 1,
      group: {
        id: 1,
        name: 'Двигатель',
        description: 'Характеристики двигателя'
      },
      synonymGroups: [],
      partAttributes: [],
      catalogItemAttributes: [],
      equipmentAttributes: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
]);

const mockTemplates = ref<AttributeTemplate[]>([
  {
    id: 4,
    name: 'operating_weight',
    title: 'Эксплуатационная масса',
    description: 'Масса машины в рабочем состоянии',
    dataType: 'NUMBER' as const,
    unit: 'KG' as const,
    isRequired: true,
    minValue: 1000,
    maxValue: 50000,
    allowedValues: [],
    tolerance: 100,
    groupId: 2,
    group: {
      id: 2,
      name: 'Габариты и масса',
      description: 'Габаритные размеры и масса'
    },
    synonymGroups: [],
    partAttributes: [],
    catalogItemAttributes: [],
    equipmentAttributes: [],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 5,
    name: 'bucket_capacity',
    title: 'Объем ковша',
    description: 'Номинальный объем ковша',
    dataType: 'NUMBER' as const,
    unit: 'L' as const,
    isRequired: false,
    minValue: 100,
    maxValue: 5000,
    allowedValues: [],
    tolerance: 50,
    groupId: 3,
    group: {
      id: 3,
      name: 'Рабочее оборудование',
      description: 'Характеристики рабочего оборудования'
    },
    synonymGroups: [],
    partAttributes: [],
    catalogItemAttributes: [],
    equipmentAttributes: [],
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// Event handlers
function onFilterChange(filters: any) {
  console.log('Filter changed:', filters);
}

function onTemplatesSelected(templates: AttributeTemplate[]) {
  console.log('Templates selected:', templates);
}
</script>

