<template>
  <div class="attribute-display">
    <div class="attribute-display__header" v-if="showHeader">
      <h3 class="attribute-display__title">
        {{ title }}
      </h3>
      <div class="attribute-display__summary" v-if="showSummary">
        <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full dark:bg-gray-800 dark:text-gray-200">
          {{ attributeCount.total }} атрибутов
        </span>
        <span v-if="attributeCount.required > 0" class="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900/20 dark:text-blue-400">
          {{ attributeCount.required }} обязательных
        </span>
      </div>
    </div>

    <div class="attribute-display__content">
      <div v-if="loading" class="attribute-display__loading">
        <Spinner size="md" />
        <span>Загрузка атрибутов...</span>
      </div>

      <div v-else-if="error" class="attribute-display__error">
        <Message severity="error" :closable="false">
          {{ error }}
        </Message>
      </div>

      <div v-else-if="!hasAttributes" class="attribute-display__empty">
        <EmptyState
          icon="tag"
          title="Нет атрибутов"
          description="У этой сущности нет атрибутов для отображения"
        />
      </div>

      <div v-else class="attribute-display__groups">
        <div
          v-for="(attributes, groupName) in groupedAttributes"
          :key="groupName"
          class="attribute-group"
          :class="{
            'attribute-group--compact': compact,
            'attribute-group--expanded': !compact
          }"
        >
          <div class="attribute-group__header" v-if="showGroupHeaders">
            <h4 class="attribute-group__title">{{ groupName }}</h4>
            <span class="inline-flex items-center px-1.5 py-0.5 text-xs font-medium bg-gray-100 text-gray-800 rounded dark:bg-gray-800 dark:text-gray-200">
              {{ attributes.length }}
            </span>
          </div>

          <div class="attribute-group__items">
            <div
              v-for="attribute in attributes"
              :key="attribute.id"
              class="attribute-item"
              :class="{
                'attribute-item--required': attribute.template.isRequired,
                'attribute-item--compact': compact
              }"
            >
              <div class="attribute-item__label">
                <span class="attribute-item__name">
                  {{ attribute.template.title }}
                </span>
                <span
                  v-if="attribute.template.isRequired && showRequiredBadges"
                  class="inline-flex items-center px-1.5 py-0.5 text-xs font-medium bg-red-100 text-red-800 rounded dark:bg-red-900/20 dark:text-red-400"
                >
                  Обязательный
                </span>
              </div>

              <div class="attribute-item__value">
                <AttributeValueDisplay
                  :attribute="attribute"
                  :show-unit="showUnits"
                  :format-numbers="formatNumbers"
                />
              </div>

              <div
                v-if="attribute.template.description && showDescriptions"
                class="attribute-item__description"
              >
                {{ attribute.template.description }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div v-if="showFilters" class="attribute-display__filters">
      <div class="attribute-filters">
        <VoltInputText
          v-model="searchQuery"
          placeholder="Поиск по атрибутам..."
          class="attribute-filters__search"
        >
          <template #prefix>
            <Icon name="search" size="16" />
          </template>
        </VoltInputText>

        <VoltSelect
          v-model="selectedGroup"
          :options="groupOptions"
          placeholder="Все группы"
          class="attribute-filters__group"
          :clearable="true"
        />

        <VoltSelect
          v-model="selectedDataType"
          :options="dataTypeOptions"
          placeholder="Все типы"
          class="attribute-filters__type"
          :clearable="true"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import type {
  EquipmentModelAttributeWithTemplate,
  AttributeDataType
} from '@/types/attributes';
import { useAttributes } from '@/composables/useAttributes';
import { getDataTypeDisplayName } from '@/utils/attributes';
import VoltInputText from '@/volt/InputText.vue';
import VoltSelect from '@/volt/Select.vue';
import Message from '@/volt/Message.vue';
import Spinner from '@/components/ui/Spinner.vue';
import EmptyState from '@/components/ui/EmptyState.vue';
import AttributeValueDisplay from './AttributeValueDisplay.vue';
import Icon from '@/components/ui/Icon.vue';

interface Props {
  entityType: 'part' | 'catalogItem' | 'equipmentModel';
  entityId: string | number;
  title?: string;
  compact?: boolean;
  showHeader?: boolean;
  showSummary?: boolean;
  showGroupHeaders?: boolean;
  showRequiredBadges?: boolean;
  showDescriptions?: boolean;
  showUnits?: boolean;
  showFilters?: boolean;
  formatNumbers?: boolean;
  attributes?: EquipmentModelAttributeWithTemplate[];
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Атрибуты',
  compact: false,
  showHeader: true,
  showSummary: true,
  showGroupHeaders: true,
  showRequiredBadges: true,
  showDescriptions: true,
  showUnits: true,
  showFilters: false,
  formatNumbers: true
});

// Convert entityId to ref for composable
const entityIdRef = computed(() => String(props.entityId));

// Use attributes composable only if attributes are not provided
const {
  state,
  groupedAttributes: composableGroupedAttributes,
  attributeCount: composableAttributeCount,
  hasAttributes: composableHasAttributes,
  loadAttributes
} = useAttributes(entityIdRef);

// Filter state
const searchQuery = ref('');
const selectedGroup = ref<string | null>(null);
const selectedDataType = ref<AttributeDataType | null>(null);

// Computed properties
const loading = computed(() => !props.attributes && state.value.loading);
const error = computed(() => !props.attributes && state.value.error);

const allAttributes = computed(() => {
  return props.attributes || state.value.attributes;
});

const filteredAttributes = computed(() => {
  let filtered = allAttributes.value;

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(attr =>
      attr.template.title.toLowerCase().includes(query) ||
      attr.template.name.toLowerCase().includes(query) ||
      attr.value.toLowerCase().includes(query) ||
      (attr.template.description && attr.template.description.toLowerCase().includes(query))
    );
  }

  // Group filter
  if (selectedGroup.value) {
    filtered = filtered.filter(attr =>
      (attr.template.group?.name || 'Общие') === selectedGroup.value
    );
  }

  // Data type filter
  if (selectedDataType.value) {
    filtered = filtered.filter(attr =>
      attr.template.dataType === selectedDataType.value
    );
  }

  return filtered;
});

const groupedAttributes = computed(() => {
  if (props.attributes) {
    // Group the provided attributes
    const grouped: Record<string, EquipmentModelAttributeWithTemplate[]> = {};
    
    filteredAttributes.value.forEach(attribute => {
      const groupName = attribute.template.group?.name || 'Общие';
      
      if (!grouped[groupName]) {
        grouped[groupName] = [];
      }
      
      grouped[groupName].push(attribute);
    });
    
    // Sort attributes within each group
    Object.keys(grouped).forEach(groupName => {
      grouped[groupName].sort((a, b) => a.template.title.localeCompare(b.template.title, 'ru'));
    });
    
    return grouped;
  }
  
  return composableGroupedAttributes.value;
});

const attributeCount = computed(() => {
  if (props.attributes) {
    const total = allAttributes.value.length;
    const required = allAttributes.value.filter(attr => attr.template.isRequired).length;
    const optional = total - required;
    const byGroup: Record<string, number> = {};
    
    allAttributes.value.forEach(attribute => {
      const groupName = attribute.template.group?.name || 'Общие';
      byGroup[groupName] = (byGroup[groupName] || 0) + 1;
    });
    
    return { total, required, optional, byGroup };
  }
  
  return composableAttributeCount.value;
});

const hasAttributes = computed(() => {
  return props.attributes ? props.attributes.length > 0 : composableHasAttributes.value;
});

// Filter options
const groupOptions = computed(() => {
  const groups = new Set<string>();
  allAttributes.value.forEach(attr => {
    groups.add(attr.template.group?.name || 'Общие');
  });
  
  return Array.from(groups).sort().map(group => ({
    label: group,
    value: group
  }));
});

const dataTypeOptions = computed(() => {
  const types = new Set<AttributeDataType>();
  allAttributes.value.forEach(attr => {
    types.add(attr.template.dataType);
  });
  
  return Array.from(types).sort().map(type => ({
    label: getDataTypeDisplayName(type),
    value: type
  }));
});

// Initialize on mount if attributes are not provided
onMounted(() => {
  if (!props.attributes) {
    loadAttributes();
  }
});

watch(() => props.entityId, () => {
  if (!props.attributes) {
    loadAttributes();
  }
});
</script>

<style scoped>
.attribute-display {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.attribute-display__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.attribute-display__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-foreground);
}

.attribute-display__summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.attribute-display__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem 0;
  color: var(--color-muted);
}

.attribute-display__error {
  padding: 1rem 0;
}

.attribute-display__empty {
  padding: 2rem 0;
}

.attribute-display__groups {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.attribute-group {
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  overflow: hidden;
}

.attribute-group--compact {
  border: none;
  border-radius: 0;
}

.attribute-group__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.attribute-group--compact .attribute-group__header {
  padding: 0.5rem 0;
  background-color: transparent;
  border-bottom: none;
}

.attribute-group__title {
  font-weight: 500;
  color: var(--color-foreground);
}

.attribute-group--compact .attribute-group__title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-muted);
}

.attribute-group__items {
  border-top: 1px solid var(--color-border);
}

.attribute-group__items > *:not(:last-child) {
  border-bottom: 1px solid var(--color-border);
}

.attribute-group--compact .attribute-group__items {
  border-top: none;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.attribute-group--compact .attribute-group__items > *:not(:last-child) {
  border-bottom: none;
}

.attribute-item {
  padding: 0.75rem 1rem;
}

.attribute-item--compact {
  padding: 0.25rem 0;
}

.attribute-item--required {
  background-color: rgb(239 246 255 / 0.5);
}

.dark .attribute-item--required {
  background-color: rgb(30 58 138 / 0.2);
}

.attribute-item--compact.attribute-item--required {
  background-color: transparent;
}

.attribute-item__label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.attribute-item--compact .attribute-item__label {
  margin-bottom: 0;
}

.attribute-item__name {
  font-weight: 500;
  color: var(--color-foreground);
}

.attribute-item--compact .attribute-item__name {
  font-size: 0.875rem;
}

.attribute-item__value {
  color: var(--color-muted);
}

.attribute-item--compact .attribute-item__value {
  font-size: 0.875rem;
}

.attribute-item__description {
  font-size: 0.75rem;
  color: var(--color-muted);
  margin-top: 0.25rem;
}

.attribute-display__filters {
  border-top: 1px solid var(--color-border);
  padding-top: 1rem;
}

.attribute-filters {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .attribute-filters {
    grid-template-columns: repeat(3, 1fr);
  }
}

.attribute-filters__search,
.attribute-filters__group,
.attribute-filters__type {
  width: 100%;
}
</style>