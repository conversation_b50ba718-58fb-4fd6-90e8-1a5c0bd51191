<template>
  <div class="attribute-editor">
    <div class="attribute-editor__header">
      <h3 class="attribute-editor__title">
        {{ title }}
      </h3>
      <div class="attribute-editor__actions" v-if="!readonly">
        <VButton
          @click="openAddDialog"
          size="small"
          :disabled="loading"
        >
          <Icon name="plus" size="16" />
          Добавить атрибут
        </VButton>
      </div>
    </div>

    <div class="attribute-editor__content">
      <div v-if="loading" class="attribute-editor__loading">
        <Spinner size="md" />
        <span>Загрузка атрибутов...</span>
      </div>

      <div v-else-if="error" class="attribute-editor__error">
        <Message severity="error" :closable="false">
          {{ error }}
        </Message>
      </div>

      <div v-else-if="!hasAttributes" class="attribute-editor__empty">
        <EmptyState
          icon="tag"
          title="Нет атрибутов"
          description="У этой сущности пока нет атрибутов"
        >
          <template #actions v-if="!readonly">
            <VButton
              @click="openAddDialog"
              size="small"
            >
              Добавить первый атрибут
            </VButton>
          </template>
        </EmptyState>
      </div>

      <div v-else class="attribute-editor__groups">
        <div
          v-for="(attributes, groupName) in groupedAttributes"
          :key="groupName"
          class="attribute-group"
        >
          <div class="attribute-group__header">
            <h4 class="attribute-group__title">{{ groupName }}</h4>
            <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full dark:bg-gray-800 dark:text-gray-200">
              {{ attributes.length }}
            </span>
          </div>

          <div class="attribute-group__items">
            <div
              v-for="attribute in attributes"
              :key="attribute.id"
              class="attribute-item"
              :class="{
                'attribute-item--required': attribute.template.isRequired,
                'attribute-item--readonly': readonly
              }"
            >
              <div class="attribute-item__info">
                <div class="attribute-item__label">
                  {{ attribute.template.title }}
                  <span
                    v-if="attribute.template.isRequired"
                    class="inline-flex items-center px-1.5 py-0.5 text-xs font-medium bg-red-100 text-red-800 rounded dark:bg-red-900/20 dark:text-red-400"
                  >
                    Обязательный
                  </span>
                </div>
                <div class="attribute-item__description" v-if="attribute.template.description">
                  {{ attribute.template.description }}
                </div>
              </div>

              <div class="attribute-item__value">
                <AttributeValueDisplay
                  :attribute="attribute"
                  :editable="!readonly"
                  @edit="openEditDialog(attribute)"
                />
              </div>

              <div class="attribute-item__actions" v-if="!readonly">
                <VButton
                  @click="openEditDialog(attribute)"
                  size="small"
                  outlined
                  :disabled="loading"
                >
                  <Icon name="edit" size="14" />
                </VButton>
                <DangerButton
                  @click="confirmDelete(attribute)"
                  size="small"
                  outlined
                  :disabled="loading || attribute.template.isRequired"
                >
                  <Icon name="trash" size="14" />
                </DangerButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Attribute Dialog -->
    <VoltDialog
      v-model:visible="showAddDialog"
      header="Добавить атрибут"
      :modal="true"
      :closable="true"
      class="attribute-dialog"
    >
      <AttributeForm
        :available-templates="availableTemplates"
        :loading="formLoading"
        @submit="handleAddAttribute"
        @cancel="closeAddDialog"
      />
    </VoltDialog>

    <!-- Edit Attribute Dialog -->
    <VoltDialog
      v-model:visible="showEditDialog"
      header="Редактировать атрибут"
      :modal="true"
      :closable="true"
      class="attribute-dialog"
    >
      <AttributeForm
        v-if="selectedAttribute"
        :attribute="selectedAttribute"
        :loading="formLoading"
        @submit="handleUpdateAttribute"
        @cancel="closeEditDialog"
      />
    </VoltDialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import type {
  EquipmentModelAttributeWithTemplate,
  AttributeFormData,
  EditAttributeFormData
} from '@/types/attributes';
import { useAttributes } from '@/composables/useAttributes';
import { useConfirm } from '@/composables/useConfirm';
import VButton from '@/volt/Button.vue';
import DangerButton from '@/volt/DangerButton.vue';
import VoltDialog from '@/volt/Dialog.vue';
import Message from '@/volt/Message.vue';
import Spinner from '@/components/ui/Spinner.vue';
import EmptyState from '@/components/ui/EmptyState.vue';
import AttributeValueDisplay from './AttributeValueDisplay.vue';
import AttributeForm from './AttributeForm.vue';
import Icon from '@/components/ui/Icon.vue';

interface Props {
  entityType: 'part' | 'catalogItem' | 'equipmentModel';
  entityId: string | number;
  title?: string;
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Атрибуты',
  readonly: false
});

const emit = defineEmits<{
  'attribute-added': [attribute: EquipmentModelAttributeWithTemplate];
  'attribute-updated': [attribute: EquipmentModelAttributeWithTemplate];
  'attribute-deleted': [attributeId: number];
}>();

// Convert entityId to ref for composable
const entityIdRef = computed(() => String(props.entityId));

// Use attributes composable
const {
  state,
  availableTemplates,
  groupedAttributes,
  hasAttributes,
  loadAttributes,
  loadAvailableTemplates,
  addAttribute,
  updateAttribute,
  deleteAttribute,
  formatAttribute
} = useAttributes(entityIdRef);

const { confirm } = useConfirm();

// Local state
const showAddDialog = ref(false);
const showEditDialog = ref(false);
const selectedAttribute = ref<EquipmentModelAttributeWithTemplate | null>(null);
const formLoading = ref(false);

// Computed properties
const loading = computed(() => state.value.loading);
const error = computed(() => state.value.error);

// Methods
async function openAddDialog() {
  await loadAvailableTemplates();
  showAddDialog.value = true;
}

function closeAddDialog() {
  showAddDialog.value = false;
}

function openEditDialog(attribute: EquipmentModelAttributeWithTemplate) {
  selectedAttribute.value = attribute;
  showEditDialog.value = true;
}

function closeEditDialog() {
  showEditDialog.value = false;
  selectedAttribute.value = null;
}

async function handleAddAttribute(data: AttributeFormData) {
  formLoading.value = true;
  try {
    await addAttribute(data);
    emit('attribute-added', state.value.attributes[state.value.attributes.length - 1]);
    closeAddDialog();
  } catch (error) {
    // Error is handled by the composable
  } finally {
    formLoading.value = false;
  }
}

async function handleUpdateAttribute(data: EditAttributeFormData) {
  formLoading.value = true;
  try {
    await updateAttribute(data);
    const updatedAttribute = state.value.attributes.find(attr => attr.id === data.id);
    if (updatedAttribute) {
      emit('attribute-updated', updatedAttribute);
    }
    closeEditDialog();
  } catch (error) {
    // Error is handled by the composable
  } finally {
    formLoading.value = false;
  }
}

async function confirmDelete(attribute: EquipmentModelAttributeWithTemplate) {
  if (attribute.template.isRequired) {
    return;
  }

  const confirmed = await confirm({
    message: `Вы уверены, что хотите удалить атрибут "${attribute.template.title}"?`,
    header: 'Подтверждение удаления',
    acceptLabel: 'Удалить',
    rejectLabel: 'Отмена',
    acceptClass: 'p-button-danger'
  });

  if (confirmed) {
    try {
      await deleteAttribute(attribute.id);
      emit('attribute-deleted', attribute.id);
    } catch (error) {
      // Error is handled by the composable
    }
  }
}

// Initialize on mount and when entityId changes
onMounted(() => {
  loadAttributes();
});

watch(() => props.entityId, () => {
  loadAttributes();
});
</script>

<style scoped>
.attribute-editor {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.attribute-editor__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.attribute-editor__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-foreground);
}

.attribute-editor__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem 0;
  color: var(--color-muted);
}

.attribute-editor__error {
  padding: 1rem 0;
}

.attribute-editor__empty {
  padding: 2rem 0;
}

.attribute-editor__groups {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.attribute-group {
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  overflow: hidden;
}

.attribute-group__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.attribute-group__title {
  font-weight: 500;
  color: var(--color-foreground);
}

.attribute-group__items {
  border-top: 1px solid var(--color-border);
}

.attribute-group__items > *:not(:last-child) {
  border-bottom: 1px solid var(--color-border);
}

.attribute-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  transition: background-color 0.2s;
}

.attribute-item:hover {
  background-color: var(--color-hover);
}

.attribute-item--required {
  background-color: rgb(239 246 255 / 0.5);
}

.dark .attribute-item--required {
  background-color: rgb(30 58 138 / 0.2);
}

.attribute-item--readonly {
  cursor: default;
}

.attribute-item__info {
  flex: 1;
  min-width: 0;
}

.attribute-item__label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--color-foreground);
}

.attribute-item__description {
  font-size: 0.875rem;
  color: var(--color-muted);
  margin-top: 0.25rem;
}

.attribute-item__value {
  flex-shrink: 0;
  margin: 0 1rem;
}

.attribute-item__actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex-shrink: 0;
}

.attribute-dialog {
  width: 100%;
  max-width: 28rem;
}
</style>