<template>
  <div class="attribute-filter">
    <div class="attribute-filter__header">
      <h3 class="attribute-filter__title">
        {{ title }}
      </h3>
      <div class="attribute-filter__actions">
        <button
          @click="clearAllFilters"
          :disabled="!hasActiveFilters"
          class="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800"
        >
          Очистить все
        </button>
        <button
          @click="toggleExpanded"
          class="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800"
        >
          <Icon :name="expanded ? 'chevron-up' : 'chevron-down'" size="16" />
        </button>
      </div>
    </div>

    <div v-if="expanded" class="attribute-filter__content">
      <!-- Search -->
      <div class="attribute-filter__section">
        <label class="attribute-filter__label">Поиск по значению</label>
        <VoltInputText
          v-model="searchQuery"
          placeholder="Введите значение для поиска..."
          :clearable="true"
        >
          <template #prefix>
            <Icon name="search" size="16" />
          </template>
        </VoltInputText>
      </div>

      <!-- Attribute Groups -->
      <div class="attribute-filter__section">
        <label class="attribute-filter__label">Группы атрибутов</label>
        <div class="attribute-filter__checkboxes">
          <VoltCheckbox
            v-for="group in availableGroups"
            :key="group.id"
            v-model="selectedGroups"
            :value="group.id"
            :label="group.name"
            class="attribute-filter__checkbox"
          />
        </div>
      </div>

      <!-- Data Types -->
      <div class="attribute-filter__section">
        <label class="attribute-filter__label">Типы данных</label>
        <div class="attribute-filter__checkboxes">
          <VoltCheckbox
            v-for="dataType in availableDataTypes"
            :key="dataType.value"
            v-model="selectedDataTypes"
            :value="dataType.value"
            :label="dataType.label"
            class="attribute-filter__checkbox"
          />
        </div>
      </div>

      <!-- Numeric Range Filters -->
      <div
        v-for="numericTemplate in numericTemplates"
        :key="numericTemplate.id"
        class="attribute-filter__section"
      >
        <label class="attribute-filter__label">
          {{ numericTemplate.title }}
          <span v-if="numericTemplate.unit" class="attribute-filter__unit">
            ({{ getUnitDisplayName(numericTemplate.unit) }})
          </span>
        </label>
        <div class="attribute-filter__range">
          <VoltInputNumber
            v-model="numericRanges[numericTemplate.id]?.min"
            placeholder="Мин"
            :min="numericTemplate.minValue"
            :max="numericTemplate.maxValue"
            :step="getStepForTemplate(numericTemplate)"
            class="attribute-filter__range-input"
          />
          <span class="attribute-filter__range-separator">—</span>
          <VoltInputNumber
            v-model="numericRanges[numericTemplate.id]?.max"
            placeholder="Макс"
            :min="numericTemplate.minValue"
            :max="numericTemplate.maxValue"
            :step="getStepForTemplate(numericTemplate)"
            class="attribute-filter__range-input"
          />
        </div>
      </div>

      <!-- String Value Filters -->
      <div
        v-for="stringTemplate in stringTemplates"
        :key="stringTemplate.id"
        class="attribute-filter__section"
      >
        <label class="attribute-filter__label">{{ stringTemplate.title }}</label>
        <VoltMultiSelect
          v-model="stringValues[stringTemplate.id]"
          :options="getStringOptions(stringTemplate)"
          :placeholder="`Выберите ${stringTemplate.title.toLowerCase()}`"
          :filter="true"
          :show-clear="true"
          class="attribute-filter__multiselect"
        />
      </div>

      <!-- Boolean Filters -->
      <div
        v-for="booleanTemplate in booleanTemplates"
        :key="booleanTemplate.id"
        class="attribute-filter__section"
      >
        <label class="attribute-filter__label">{{ booleanTemplate.title }}</label>
        <VoltSelectButton
          v-model="booleanValues[booleanTemplate.id]"
          :options="booleanOptions"
          :allow-empty="true"
          class="attribute-filter__select-button"
        />
      </div>

      <!-- Required/Optional Filter -->
      <div class="attribute-filter__section">
        <label class="attribute-filter__label">Обязательность</label>
        <VoltSelectButton
          v-model="requiredFilter"
          :options="requiredOptions"
          :allow-empty="true"
          class="attribute-filter__select-button"
        />
      </div>

      <!-- Active Filters Summary -->
      <div v-if="hasActiveFilters" class="attribute-filter__summary">
        <div class="attribute-filter__summary-header">
          <span class="attribute-filter__summary-title">Активные фильтры:</span>
          <button
            @click="clearAllFilters"
            class="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
          >
            Очистить все
          </button>
        </div>
        <div class="attribute-filter__summary-tags">
          <VoltTag
            v-for="filter in activeFilterTags"
            :key="filter.key"
            :value="filter.label"
            severity="info"
            :removable="true"
            @remove="removeFilter(filter.key)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted, reactive } from 'vue';
import type {
  AttributeTemplate,
  AttributeDataType,
  AttributeUnit
} from '@/types/attributes';
import { getUnitDisplayName, getDataTypeDisplayName } from '@/utils/attributes';
import { useTrpc } from '@/composables/useTrpc';
import VoltInputText from '@/volt/InputText.vue';
import VoltInputNumber from '@/volt/InputNumber.vue';
import VoltCheckbox from '@/volt/Checkbox.vue';
import VoltMultiSelect from '@/volt/MultiSelect.vue';
import VoltSelectButton from '@/volt/SelectButton.vue';
import VoltTag from '@/volt/Tag.vue';
import Icon from '@/components/ui/Icon.vue';

interface AttributeFilterValue {
  searchQuery?: string;
  groupIds?: number[];
  dataTypes?: AttributeDataType[];
  numericRanges?: Record<number, { min?: number; max?: number }>;
  stringValues?: Record<number, string[]>;
  booleanValues?: Record<number, boolean>;
  requiredOnly?: boolean;
  optionalOnly?: boolean;
}

interface Props {
  title?: string;
  entityType?: 'part' | 'catalogItem' | 'equipmentModel';
  initialFilters?: AttributeFilterValue;
  expanded?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Фильтры атрибутов',
  expanded: true
});

const emit = defineEmits<{
  'filter-change': [filters: AttributeFilterValue];
  'clear-filters': [];
}>();

const { $trpc } = useTrpc();

// State
const expanded = ref(props.expanded);
const loading = ref(false);
const availableGroups = ref<any[]>([]);
const availableTemplates = ref<AttributeTemplate[]>([]);

// Filter values
const searchQuery = ref(props.initialFilters?.searchQuery || '');
const selectedGroups = ref<number[]>(props.initialFilters?.groupIds || []);
const selectedDataTypes = ref<AttributeDataType[]>(props.initialFilters?.dataTypes || []);
const numericRanges = reactive<Record<number, { min?: number; max?: number }>>(
  props.initialFilters?.numericRanges || {}
);
const stringValues = reactive<Record<number, string[]>>(
  props.initialFilters?.stringValues || {}
);
const booleanValues = reactive<Record<number, boolean>>(
  props.initialFilters?.booleanValues || {}
);
const requiredFilter = ref<'required' | 'optional' | null>(
  props.initialFilters?.requiredOnly ? 'required' :
  props.initialFilters?.optionalOnly ? 'optional' : null
);

// Computed properties
const availableDataTypes = computed(() => {
  const types = new Set<AttributeDataType>();
  availableTemplates.value.forEach(template => {
    types.add(template.dataType);
  });
  
  return Array.from(types).sort().map(type => ({
    label: getDataTypeDisplayName(type),
    value: type
  }));
});

const numericTemplates = computed(() => {
  return availableTemplates.value.filter(template => template.dataType === 'NUMBER');
});

const stringTemplates = computed(() => {
  return availableTemplates.value.filter(template => template.dataType === 'STRING');
});

const booleanTemplates = computed(() => {
  return availableTemplates.value.filter(template => template.dataType === 'BOOLEAN');
});

const booleanOptions = [
  { label: 'Да', value: true },
  { label: 'Нет', value: false }
];

const requiredOptions = [
  { label: 'Обязательные', value: 'required' },
  { label: 'Опциональные', value: 'optional' }
];

const currentFilters = computed<AttributeFilterValue>(() => {
  const filters: AttributeFilterValue = {};
  
  if (searchQuery.value) {
    filters.searchQuery = searchQuery.value;
  }
  
  if (selectedGroups.value.length > 0) {
    filters.groupIds = selectedGroups.value;
  }
  
  if (selectedDataTypes.value.length > 0) {
    filters.dataTypes = selectedDataTypes.value;
  }
  
  const activeNumericRanges = Object.entries(numericRanges)
    .filter(([_, range]) => range.min !== undefined || range.max !== undefined)
    .reduce((acc, [templateId, range]) => {
      acc[parseInt(templateId)] = range;
      return acc;
    }, {} as Record<number, { min?: number; max?: number }>);
  
  if (Object.keys(activeNumericRanges).length > 0) {
    filters.numericRanges = activeNumericRanges;
  }
  
  const activeStringValues = Object.entries(stringValues)
    .filter(([_, values]) => values.length > 0)
    .reduce((acc, [templateId, values]) => {
      acc[parseInt(templateId)] = values;
      return acc;
    }, {} as Record<number, string[]>);
  
  if (Object.keys(activeStringValues).length > 0) {
    filters.stringValues = activeStringValues;
  }
  
  const activeBooleanValues = Object.entries(booleanValues)
    .filter(([_, value]) => value !== undefined)
    .reduce((acc, [templateId, value]) => {
      acc[parseInt(templateId)] = value;
      return acc;
    }, {} as Record<number, boolean>);
  
  if (Object.keys(activeBooleanValues).length > 0) {
    filters.booleanValues = activeBooleanValues;
  }
  
  if (requiredFilter.value === 'required') {
    filters.requiredOnly = true;
  } else if (requiredFilter.value === 'optional') {
    filters.optionalOnly = true;
  }
  
  return filters;
});

const hasActiveFilters = computed(() => {
  return Object.keys(currentFilters.value).length > 0;
});

const activeFilterTags = computed(() => {
  const tags: Array<{ key: string; label: string }> = [];
  
  if (searchQuery.value) {
    tags.push({
      key: 'search',
      label: `Поиск: "${searchQuery.value}"`
    });
  }
  
  selectedGroups.value.forEach(groupId => {
    const group = availableGroups.value.find(g => g.id === groupId);
    if (group) {
      tags.push({
        key: `group-${groupId}`,
        label: `Группа: ${group.name}`
      });
    }
  });
  
  selectedDataTypes.value.forEach(dataType => {
    tags.push({
      key: `dataType-${dataType}`,
      label: `Тип: ${getDataTypeDisplayName(dataType)}`
    });
  });
  
  Object.entries(numericRanges).forEach(([templateId, range]) => {
    if (range.min !== undefined || range.max !== undefined) {
      const template = availableTemplates.value.find(t => t.id === parseInt(templateId));
      if (template) {
        const rangeText = range.min !== undefined && range.max !== undefined
          ? `${range.min} — ${range.max}`
          : range.min !== undefined
          ? `от ${range.min}`
          : `до ${range.max}`;
        
        tags.push({
          key: `numeric-${templateId}`,
          label: `${template.title}: ${rangeText}`
        });
      }
    }
  });
  
  if (requiredFilter.value) {
    tags.push({
      key: 'required',
      label: requiredFilter.value === 'required' ? 'Только обязательные' : 'Только опциональные'
    });
  }
  
  return tags;
});

// Methods
async function loadAvailableGroups() {
  try {
    loading.value = true;
    const groups = await $trpc.crud.attributeGroup.findMany.query({
      include: {
        _count: {
          select: { templates: true }
        }
      },
      orderBy: { name: 'asc' }
    });
    availableGroups.value = groups || [];
  } catch (error) {
    console.error('Error loading attribute groups:', error);
  } finally {
    loading.value = false;
  }
}

async function loadAvailableTemplates() {
  try {
    loading.value = true;
    const templates = await $trpc.crud.attributeTemplate.findMany.query({
      include: {
        group: true
      },
      orderBy: { title: 'asc' }
    });
    availableTemplates.value = templates as AttributeTemplate[] || [];
  } catch (error) {
    console.error('Error loading attribute templates:', error);
  } finally {
    loading.value = false;
  }
}

function getStringOptions(template: AttributeTemplate) {
  if (template.allowedValues && template.allowedValues.length > 0) {
    return template.allowedValues.map(value => ({
      label: value,
      value: value
    }));
  }
  
  // TODO: Load actual values from database
  return [];
}

function getStepForTemplate(template: AttributeTemplate): number {
  if (template.unit) {
    // Adjust step based on unit
    switch (template.unit) {
      case 'MM':
        return 0.1;
      case 'INCH':
        return 0.01;
      case 'G':
      case 'KG':
        return 0.1;
      default:
        return 1;
    }
  }
  return 1;
}

function toggleExpanded() {
  expanded.value = !expanded.value;
}

function clearAllFilters() {
  searchQuery.value = '';
  selectedGroups.value = [];
  selectedDataTypes.value = [];
  Object.keys(numericRanges).forEach(key => {
    delete numericRanges[parseInt(key)];
  });
  Object.keys(stringValues).forEach(key => {
    delete stringValues[parseInt(key)];
  });
  Object.keys(booleanValues).forEach(key => {
    delete booleanValues[parseInt(key)];
  });
  requiredFilter.value = null;
  
  emit('clear-filters');
}

function removeFilter(key: string) {
  if (key === 'search') {
    searchQuery.value = '';
  } else if (key.startsWith('group-')) {
    const groupId = parseInt(key.replace('group-', ''));
    selectedGroups.value = selectedGroups.value.filter(id => id !== groupId);
  } else if (key.startsWith('dataType-')) {
    const dataType = key.replace('dataType-', '') as AttributeDataType;
    selectedDataTypes.value = selectedDataTypes.value.filter(type => type !== dataType);
  } else if (key.startsWith('numeric-')) {
    const templateId = parseInt(key.replace('numeric-', ''));
    delete numericRanges[templateId];
  } else if (key === 'required') {
    requiredFilter.value = null;
  }
}

// Watch for filter changes
watch(currentFilters, (newFilters) => {
  emit('filter-change', newFilters);
}, { deep: true });

// Initialize
onMounted(() => {
  loadAvailableGroups();
  loadAvailableTemplates();
});
</script>

<style scoped>
.attribute-filter {
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  overflow: hidden;
}

.attribute-filter__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.attribute-filter__title {
  font-weight: 500;
  color: var(--color-foreground);
}

.attribute-filter__actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.attribute-filter__content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.attribute-filter__section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.attribute-filter__label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-foreground);
}

.attribute-filter__unit {
  color: var(--color-muted);
  font-weight: normal;
}

.attribute-filter__checkboxes {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.attribute-filter__checkbox {
  display: block;
}

.attribute-filter__range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.attribute-filter__range-input {
  flex: 1;
}

.attribute-filter__range-separator {
  color: var(--color-muted);
}

.attribute-filter__multiselect,
.attribute-filter__select-button {
  width: 100%;
}

.attribute-filter__summary {
  border-top: 1px solid var(--color-border);
  padding-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.attribute-filter__summary-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.attribute-filter__summary-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-foreground);
}

.attribute-filter__summary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}
</style>