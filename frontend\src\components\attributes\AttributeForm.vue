<template>
  <form @submit.prevent="handleSubmit" class="attribute-form">
    <div class="attribute-form__content">
      <!-- Template Selection (for new attributes) -->
      <div v-if="!attribute" class="attribute-form__field">
        <label class="attribute-form__label">
          Шаблон атрибута *
        </label>
        <VoltSelect
          v-model="formData.templateId"
          :options="templateOptions"
          placeholder="Выберите шаблон атрибута"
          :filter="true"
          :show-clear="false"
          :invalid="!!errors.templateId"
          @change="onTemplateChange"
        />
        <div v-if="errors.templateId" class="attribute-form__error">
          {{ errors.templateId }}
        </div>
      </div>

      <!-- Template Info (for editing) -->
      <div v-else class="attribute-form__info">
        <div class="attribute-form__template-info">
          <h4 class="attribute-form__template-title">
            {{ attribute.template.title }}
          </h4>
          <div class="attribute-form__template-details">
            <span
              :class="getDataTypeBadgeClass(attribute.template.dataType)"
              class="inline-flex items-center px-2 py-1 text-sm font-medium rounded"
            >
              {{ getDataTypeDisplayName(attribute.template.dataType) }}
            </span>
            <span v-if="attribute.template.unit" class="attribute-form__unit">
              {{ getUnitDisplayName(attribute.template.unit) }}
            </span>
          </div>
          <div v-if="attribute.template.description" class="attribute-form__template-description">
            {{ attribute.template.description }}
          </div>
        </div>
      </div>

      <!-- Value Input -->
      <div class="attribute-form__field">
        <label class="attribute-form__label">
          Значение *
          <span v-if="selectedTemplate?.unit" class="attribute-form__unit-label">
            ({{ getUnitDisplayName(selectedTemplate.unit) }})
          </span>
        </label>

        <!-- String Input -->
        <VoltInputText
          v-if="selectedTemplate?.dataType === 'STRING' && !hasAllowedValues"
          v-model="formData.value"
          :placeholder="getValuePlaceholder()"
          :invalid="!!errors.value"
        />

        <!-- String Select (with allowed values) -->
        <VoltSelect
          v-else-if="selectedTemplate?.dataType === 'STRING' && hasAllowedValues"
          v-model="formData.value"
          :options="allowedValueOptions"
          :placeholder="getValuePlaceholder()"
          :invalid="!!errors.value"
        />

        <!-- Number Input -->
        <VoltInputNumber
          v-else-if="selectedTemplate?.dataType === 'NUMBER'"
          v-model="formData.value"
          :placeholder="getValuePlaceholder()"
          :min="selectedTemplate.minValue"
          :max="selectedTemplate.maxValue"
          :step="getNumberStep()"
          :invalid="!!errors.value"
        />

        <!-- Boolean Input -->
        <VoltSelectButton
          v-else-if="selectedTemplate?.dataType === 'BOOLEAN'"
          v-model="formData.value"
          :options="booleanOptions"
          :invalid="!!errors.value"
        />

        <!-- Date Input -->
        <VoltInputText
          v-else-if="selectedTemplate?.dataType === 'DATE'"
          v-model="formData.value"
          type="date"
          :placeholder="getValuePlaceholder()"
          :invalid="!!errors.value"
        />

        <!-- JSON Input -->
        <VoltTextarea
          v-else-if="selectedTemplate?.dataType === 'JSON'"
          v-model="formData.value"
          :placeholder="getValuePlaceholder()"
          :rows="4"
          :invalid="!!errors.value"
        />

        <div v-if="errors.value" class="attribute-form__error">
          {{ errors.value }}
        </div>

        <!-- Value constraints info -->
        <div v-if="selectedTemplate && hasConstraints" class="attribute-form__constraints">
          <div v-if="selectedTemplate.minValue !== null || selectedTemplate.maxValue !== null" class="attribute-form__constraint">
            <Icon name="info" size="14" />
            <span>
              Диапазон: 
              {{ selectedTemplate.minValue !== null ? selectedTemplate.minValue : '−∞' }} — 
              {{ selectedTemplate.maxValue !== null ? selectedTemplate.maxValue : '+∞' }}
            </span>
          </div>
          <div v-if="selectedTemplate.tolerance && selectedTemplate.tolerance > 0" class="attribute-form__constraint">
            <Icon name="target" size="14" />
            <span>Допуск: ±{{ selectedTemplate.tolerance }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="attribute-form__actions">
      <button
        type="button"
        @click="$emit('cancel')"
        :disabled="loading"
        class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-[--color-card] text-[--color-foreground] border border-[--color-border] rounded-md hover:bg-[--color-hover] transition-colors text-sm font-medium min-w-[100px] disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Отмена
      </button>
      <button
        type="submit"
        :disabled="!isValid || loading"
        class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-[--color-primary] text-[--color-primary-foreground] rounded-md hover:bg-[--color-primary-hover] transition-colors text-sm font-medium min-w-[100px] disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <Spinner v-if="loading" size="xs" class="text-current" />
        {{ attribute ? 'Сохранить' : 'Добавить' }}
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import type {
  AttributeTemplate,
  AttributeFormData,
  EditAttributeFormData,
  EquipmentModelAttributeWithTemplate,
  AttributeDataType
} from '@/types/attributes';
import { getUnitDisplayName, getDataTypeDisplayName, convertValueToType } from '@/utils/attributes';
import { createAttributeValueSchema } from '@/types/attributes';
import VoltInputText from '@/volt/InputText.vue';
import VoltInputNumber from '@/volt/InputNumber.vue';
import VoltSelect from '@/volt/Select.vue';
import VoltSelectButton from '@/volt/SelectButton.vue';
import VoltTextarea from '@/volt/Textarea.vue';
import Spinner from '@/components/ui/Spinner.vue';
import Icon from '@/components/ui/Icon.vue';

interface Props {
  attribute?: EquipmentModelAttributeWithTemplate;
  availableTemplates?: AttributeTemplate[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<{
  'submit': [data: AttributeFormData | EditAttributeFormData];
  'cancel': [];
}>();

// Form data
const formData = ref<{
  templateId: number | null;
  value: any;
}>({
  templateId: null,
  value: ''
});

// Form state
const errors = ref<Record<string, string>>({});

// Computed properties
const selectedTemplate = computed(() => {
  if (props.attribute) {
    return props.attribute.template;
  }
  
  if (formData.value.templateId && props.availableTemplates) {
    return props.availableTemplates.find(t => t.id === formData.value.templateId);
  }
  
  return null;
});

const templateOptions = computed(() => {
  if (!props.availableTemplates) return [];
  
  return props.availableTemplates.map(template => ({
    label: `${template.title} (${getDataTypeDisplayName(template.dataType)})`,
    value: template.id,
    template: template
  }));
});

const hasAllowedValues = computed(() => {
  return selectedTemplate.value?.allowedValues && selectedTemplate.value.allowedValues.length > 0;
});

const allowedValueOptions = computed(() => {
  if (!hasAllowedValues.value) return [];
  
  return selectedTemplate.value!.allowedValues.map(value => ({
    label: value,
    value: value
  }));
});

const booleanOptions = [
  { label: 'Да', value: true },
  { label: 'Нет', value: false }
];

const hasConstraints = computed(() => {
  if (!selectedTemplate.value) return false;
  
  return selectedTemplate.value.minValue !== null ||
         selectedTemplate.value.maxValue !== null ||
         (selectedTemplate.value.tolerance && selectedTemplate.value.tolerance > 0);
});

const isValid = computed(() => {
  return Object.keys(errors.value).length === 0 &&
         formData.value.templateId !== null &&
         formData.value.value !== '' &&
         formData.value.value !== null &&
         formData.value.value !== undefined;
});

// Methods
function getValuePlaceholder(): string {
  if (!selectedTemplate.value) return '';
  
  switch (selectedTemplate.value.dataType) {
    case 'STRING':
      return 'Введите текстовое значение';
    case 'NUMBER':
      return 'Введите числовое значение';
    case 'DATE':
      return 'Выберите дату';
    case 'JSON':
      return 'Введите JSON объект';
    default:
      return '';
  }
}

function getNumberStep(): number {
  if (!selectedTemplate.value?.unit) return 1;
  
  switch (selectedTemplate.value.unit) {
    case 'MM':
      return 0.1;
    case 'INCH':
      return 0.01;
    case 'G':
    case 'KG':
      return 0.1;
    default:
      return 1;
  }
}

function getDataTypeBadgeClass(dataType: AttributeDataType): string {
  switch (dataType) {
    case 'STRING': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    case 'NUMBER': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'BOOLEAN': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'DATE': return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    case 'JSON': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
  }
}

function onTemplateChange() {
  // Reset value when template changes
  if (selectedTemplate.value) {
    switch (selectedTemplate.value.dataType) {
      case 'STRING':
        formData.value.value = '';
        break;
      case 'NUMBER':
        formData.value.value = selectedTemplate.value.minValue || 0;
        break;
      case 'BOOLEAN':
        formData.value.value = false;
        break;
      case 'DATE':
        formData.value.value = new Date().toISOString().split('T')[0];
        break;
      case 'JSON':
        formData.value.value = '{}';
        break;
    }
  }
  
  validateForm();
}

function validateForm() {
  errors.value = {};
  
  // Validate template selection
  if (!formData.value.templateId) {
    errors.value.templateId = 'Выберите шаблон атрибута';
  }
  
  // Validate value
  if (!selectedTemplate.value) return;
  
  try {
    const schema = createAttributeValueSchema(selectedTemplate.value);
    
    let valueToValidate = formData.value.value;
    
    // Convert value to appropriate type for validation
    if (selectedTemplate.value.dataType === 'NUMBER') {
      valueToValidate = Number(valueToValidate);
    } else if (selectedTemplate.value.dataType === 'BOOLEAN') {
      valueToValidate = Boolean(valueToValidate);
    } else if (selectedTemplate.value.dataType === 'DATE') {
      valueToValidate = new Date(valueToValidate);
    }
    
    schema.parse(valueToValidate);
  } catch (error: any) {
    if (error.errors && error.errors.length > 0) {
      errors.value.value = error.errors[0].message;
    } else {
      errors.value.value = 'Неверное значение';
    }
  }
}

function handleSubmit() {
  validateForm();
  
  if (!isValid.value) return;
  
  if (props.attribute) {
    // Editing existing attribute
    const data: EditAttributeFormData = {
      id: props.attribute.id,
      value: convertValueToType(String(formData.value.value), selectedTemplate.value!.dataType),
      template: selectedTemplate.value!
    };
    emit('submit', data);
  } else {
    // Adding new attribute
    const data: AttributeFormData = {
      templateId: formData.value.templateId!,
      value: convertValueToType(String(formData.value.value), selectedTemplate.value!.dataType),
      template: selectedTemplate.value
    };
    emit('submit', data);
  }
}

// Initialize form
function initializeForm() {
  if (props.attribute) {
    // Editing mode
    formData.value.templateId = props.attribute.templateId;
    
    // Set initial value based on data type
    switch (props.attribute.template.dataType) {
      case 'NUMBER':
        formData.value.value = parseFloat(props.attribute.value) || 0;
        break;
      case 'BOOLEAN':
        formData.value.value = props.attribute.value.toLowerCase() === 'true';
        break;
      case 'DATE':
        const date = new Date(props.attribute.value);
        formData.value.value = isNaN(date.getTime()) ? 
          new Date().toISOString().split('T')[0] : 
          date.toISOString().split('T')[0];
        break;
      default:
        formData.value.value = props.attribute.value;
    }
  } else {
    // Adding mode
    formData.value.templateId = null;
    formData.value.value = '';
  }
  
  validateForm();
}

// Watch for changes
watch(() => formData.value.value, validateForm);
watch(() => props.attribute, initializeForm, { immediate: true });

onMounted(() => {
  initializeForm();
});
</script>

<style scoped>
.attribute-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.attribute-form__content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.attribute-form__field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.attribute-form__label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-foreground);
}

.attribute-form__unit-label {
  color: var(--color-muted);
  font-weight: normal;
}

.attribute-form__error {
  color: rgb(239 68 68);
  font-size: 0.875rem;
}

.attribute-form__info {
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: var(--color-background);
}

.attribute-form__template-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.attribute-form__template-title {
  font-weight: 500;
  color: var(--color-foreground);
}

.attribute-form__template-details {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.attribute-form__unit {
  font-size: 0.875rem;
  color: var(--color-muted);
}

.attribute-form__template-description {
  font-size: 0.875rem;
  color: var(--color-muted);
}

.attribute-form__constraints {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: var(--color-muted);
}

.attribute-form__constraint {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.attribute-form__actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
  border-top: 1px solid var(--color-border);
  padding-top: 1rem;
}
</style>