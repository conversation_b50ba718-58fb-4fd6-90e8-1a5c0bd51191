<template>
  <div class="attribute-template-selector">
    <div class="attribute-template-selector__header">
      <h3 class="attribute-template-selector__title">
        {{ title }}
      </h3>
      <div class="attribute-template-selector__actions">
        <button
          @click="toggleViewMode"
          class="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800"
        >
          <Icon :name="viewMode === 'grid' ? 'list' : 'grid'" size="16" />
          {{ viewMode === 'grid' ? 'Список' : 'Сетка' }}
        </button>
      </div>
    </div>

    <div class="attribute-template-selector__filters">
      <div class="template-filters">
        <VoltInputText
          v-model="searchQuery"
          placeholder="Поиск шаблонов..."
          :clearable="true"
          class="template-filters__search"
        >
          <template #prefix>
            <Icon name="search" size="16" />
          </template>
        </VoltInputText>

        <VoltSelect
          v-model="selectedGroup"
          :options="groupOptions"
          placeholder="Все группы"
          :clearable="true"
          class="template-filters__group"
        />

        <VoltSelect
          v-model="selectedDataType"
          :options="dataTypeOptions"
          placeholder="Все типы"
          :clearable="true"
          class="template-filters__type"
        />

        <VoltSelectButton
          v-model="requiredFilter"
          :options="requiredOptions"
          :allow-empty="true"
          class="template-filters__required"
        />
      </div>
    </div>

    <div class="attribute-template-selector__content">
      <div v-if="loading" class="attribute-template-selector__loading">
        <Spinner size="md" />
        <span>Загрузка шаблонов...</span>
      </div>

      <div v-else-if="error" class="attribute-template-selector__error">
        <Message severity="error" :closable="false">
          {{ error }}
        </Message>
      </div>

      <div v-else-if="filteredTemplates.length === 0" class="attribute-template-selector__empty">
        <EmptyState
          icon="template"
          title="Шаблоны не найдены"
          description="Попробуйте изменить критерии поиска"
        />
      </div>

      <div v-else class="attribute-template-selector__templates">
        <!-- Grid View -->
        <div
          v-if="viewMode === 'grid'"
          class="template-grid"
        >
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            class="template-card"
            :class="{
              'template-card--selected': isSelected(template),
              'template-card--disabled': isDisabled(template)
            }"
            @click="toggleTemplate(template)"
          >
            <div class="template-card__header">
              <div class="template-card__title">
                {{ template.title }}
              </div>
              <div class="template-card__badges">
                <span
                  :class="getDataTypeBadgeClass(template.dataType)"
                  class="inline-flex items-center px-1.5 py-0.5 text-xs font-medium rounded"
                >
                  {{ getDataTypeDisplayName(template.dataType) }}
                </span>
                <span
                  v-if="template.isRequired"
                  class="inline-flex items-center px-1.5 py-0.5 text-xs font-medium bg-red-100 text-red-800 rounded dark:bg-red-900/20 dark:text-red-400"
                >
                  Обязательный
                </span>
              </div>
            </div>

            <div class="template-card__content">
              <div class="template-card__name">
                {{ template.name }}
              </div>
              <div v-if="template.description" class="template-card__description">
                {{ template.description }}
              </div>
              <div v-if="template.group" class="template-card__group">
                <Icon name="folder" size="14" />
                {{ template.group.name }}
              </div>
              <div v-if="template.unit" class="template-card__unit">
                <Icon name="ruler" size="14" />
                {{ getUnitDisplayName(template.unit) }}
              </div>
            </div>

            <div class="template-card__footer">
              <VoltCheckbox
                :model-value="isSelected(template)"
                :disabled="isDisabled(template)"
                @click.stop
                @change="toggleTemplate(template)"
              />
            </div>
          </div>
        </div>

        <!-- List View -->
        <div
          v-else
          class="template-list"
        >
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            class="template-item"
            :class="{
              'template-item--selected': isSelected(template),
              'template-item--disabled': isDisabled(template)
            }"
            @click="toggleTemplate(template)"
          >
            <div class="template-item__checkbox">
              <VoltCheckbox
                :model-value="isSelected(template)"
                :disabled="isDisabled(template)"
                @click.stop
                @change="toggleTemplate(template)"
              />
            </div>

            <div class="template-item__content">
              <div class="template-item__header">
                <div class="template-item__title">
                  {{ template.title }}
                </div>
                <div class="template-item__badges">
                  <span
                    :class="getDataTypeBadgeClass(template.dataType)"
                    class="inline-flex items-center px-1.5 py-0.5 text-xs font-medium rounded"
                  >
                    {{ getDataTypeDisplayName(template.dataType) }}
                  </span>
                  <span
                    v-if="template.isRequired"
                    class="inline-flex items-center px-1.5 py-0.5 text-xs font-medium bg-red-100 text-red-800 rounded dark:bg-red-900/20 dark:text-red-400"
                  >
                    Обязательный
                  </span>
                </div>
              </div>

              <div class="template-item__details">
                <span class="template-item__name">{{ template.name }}</span>
                <span v-if="template.group" class="template-item__group">
                  <Icon name="folder" size="12" />
                  {{ template.group.name }}
                </span>
                <span v-if="template.unit" class="template-item__unit">
                  <Icon name="ruler" size="12" />
                  {{ getUnitDisplayName(template.unit) }}
                </span>
              </div>

              <div v-if="template.description" class="template-item__description">
                {{ template.description }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="showSelection" class="attribute-template-selector__selection">
      <div class="selection-summary">
        <div class="selection-summary__header">
          <span class="selection-summary__title">
            Выбрано шаблонов: {{ selectedTemplates.length }}
          </span>
          <button
            v-if="selectedTemplates.length > 0"
            @click="clearSelection"
            class="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
          >
            Очистить выбор
          </button>
        </div>

        <div v-if="selectedTemplates.length > 0" class="selection-summary__tags">
          <VoltTag
            v-for="template in selectedTemplates"
            :key="template.id"
            :value="template.title"
            severity="info"
            :removable="true"
            @remove="deselectTemplate(template)"
          />
        </div>
      </div>
    </div>

    <div v-if="showActions" class="attribute-template-selector__actions-footer">
      <button
        @click="$emit('cancel')"
        class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-[--color-card] text-[--color-foreground] border border-[--color-border] rounded-md hover:bg-[--color-hover] transition-colors text-sm font-medium min-w-[100px]"
      >
        Отмена
      </button>
      <button
        @click="confirmSelection"
        :disabled="selectedTemplates.length === 0"
        class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-[--color-primary] text-[--color-primary-foreground] rounded-md hover:bg-[--color-primary-hover] transition-colors text-sm font-medium min-w-[100px] disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Выбрать ({{ selectedTemplates.length }})
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import type {
  AttributeTemplate,
  AttributeDataType,
  AttributeTemplateFilter
} from '@/types/attributes';
import { getUnitDisplayName, getDataTypeDisplayName } from '@/utils/attributes';
import { useTrpc } from '@/composables/useTrpc';
import VoltInputText from '@/volt/InputText.vue';
import VoltSelect from '@/volt/Select.vue';
import VoltSelectButton from '@/volt/SelectButton.vue';
import VoltCheckbox from '@/volt/Checkbox.vue';
import VoltTag from '@/volt/Tag.vue';
import Message from '@/volt/Message.vue';
import Spinner from '@/components/ui/Spinner.vue';
import EmptyState from '@/components/ui/EmptyState.vue';
import Icon from '@/components/ui/Icon.vue';

interface Props {
  title?: string;
  availableTemplates?: AttributeTemplate[];
  excludeTemplateIds?: number[];
  multiSelect?: boolean;
  showSelection?: boolean;
  showActions?: boolean;
  initialSelection?: AttributeTemplate[];
  filter?: AttributeTemplateFilter;
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Выбор шаблонов атрибутов',
  multiSelect: true,
  showSelection: true,
  showActions: true,
  initialSelection: () => []
});

const emit = defineEmits<{
  'template-select': [template: AttributeTemplate];
  'template-deselect': [template: AttributeTemplate];
  'selection-change': [templates: AttributeTemplate[]];
  'confirm': [templates: AttributeTemplate[]];
  'cancel': [];
}>();

const { $trpc } = useTrpc();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const allTemplates = ref<AttributeTemplate[]>([]);
const selectedTemplates = ref<AttributeTemplate[]>([...props.initialSelection]);
const viewMode = ref<'grid' | 'list'>('grid');

// Filters
const searchQuery = ref('');
const selectedGroup = ref<number | null>(null);
const selectedDataType = ref<AttributeDataType | null>(null);
const requiredFilter = ref<'required' | 'optional' | null>(null);

// Computed properties
const templates = computed(() => {
  return props.availableTemplates || allTemplates.value;
});

const filteredTemplates = computed(() => {
  let filtered = templates.value;

  // Exclude templates
  if (props.excludeTemplateIds && props.excludeTemplateIds.length > 0) {
    const excludeIds = new Set(props.excludeTemplateIds);
    filtered = filtered.filter(template => !excludeIds.has(template.id));
  }

  // Apply filter prop
  if (props.filter) {
    if (props.filter.groupId) {
      filtered = filtered.filter(template => template.groupId === props.filter!.groupId);
    }
    if (props.filter.dataType) {
      filtered = filtered.filter(template => template.dataType === props.filter!.dataType);
    }
    if (props.filter.searchQuery) {
      const query = props.filter.searchQuery.toLowerCase();
      filtered = filtered.filter(template =>
        template.title.toLowerCase().includes(query) ||
        template.name.toLowerCase().includes(query) ||
        (template.description && template.description.toLowerCase().includes(query))
      );
    }
  }

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(template =>
      template.title.toLowerCase().includes(query) ||
      template.name.toLowerCase().includes(query) ||
      (template.description && template.description.toLowerCase().includes(query))
    );
  }

  // Group filter
  if (selectedGroup.value) {
    filtered = filtered.filter(template => template.groupId === selectedGroup.value);
  }

  // Data type filter
  if (selectedDataType.value) {
    filtered = filtered.filter(template => template.dataType === selectedDataType.value);
  }

  // Required filter
  if (requiredFilter.value === 'required') {
    filtered = filtered.filter(template => template.isRequired);
  } else if (requiredFilter.value === 'optional') {
    filtered = filtered.filter(template => !template.isRequired);
  }

  return filtered.sort((a, b) => a.title.localeCompare(b.title, 'ru'));
});

const groupOptions = computed(() => {
  const groups = new Set<{ id: number; name: string }>();
  templates.value.forEach(template => {
    if (template.group) {
      groups.add({ id: template.group.id, name: template.group.name });
    }
  });
  
  return Array.from(groups)
    .sort((a, b) => a.name.localeCompare(b.name, 'ru'))
    .map(group => ({
      label: group.name,
      value: group.id
    }));
});

const dataTypeOptions = computed(() => {
  const types = new Set<AttributeDataType>();
  templates.value.forEach(template => {
    types.add(template.dataType);
  });
  
  return Array.from(types).sort().map(type => ({
    label: getDataTypeDisplayName(type),
    value: type
  }));
});

const requiredOptions = [
  { label: 'Обязательные', value: 'required' },
  { label: 'Опциональные', value: 'optional' }
];

// Methods
async function loadTemplates() {
  if (props.availableTemplates) return;

  loading.value = true;
  error.value = null;

  try {
    const templates = await $trpc.crud.attributeTemplate.findMany.query({
      include: {
        group: true
      },
      orderBy: { title: 'asc' }
    });
    
    allTemplates.value = templates as AttributeTemplate[] || [];
  } catch (err: any) {
    error.value = err.message || 'Ошибка загрузки шаблонов';
  } finally {
    loading.value = false;
  }
}

function isSelected(template: AttributeTemplate): boolean {
  return selectedTemplates.value.some(t => t.id === template.id);
}

function isDisabled(template: AttributeTemplate): boolean {
  return false; // Add custom logic if needed
}

function toggleTemplate(template: AttributeTemplate) {
  if (isDisabled(template)) return;

  if (isSelected(template)) {
    deselectTemplate(template);
  } else {
    selectTemplate(template);
  }
}

function selectTemplate(template: AttributeTemplate) {
  if (!props.multiSelect) {
    selectedTemplates.value = [template];
  } else {
    if (!isSelected(template)) {
      selectedTemplates.value.push(template);
    }
  }
  
  emit('template-select', template);
  emit('selection-change', selectedTemplates.value);
}

function deselectTemplate(template: AttributeTemplate) {
  selectedTemplates.value = selectedTemplates.value.filter(t => t.id !== template.id);
  emit('template-deselect', template);
  emit('selection-change', selectedTemplates.value);
}

function clearSelection() {
  selectedTemplates.value = [];
  emit('selection-change', selectedTemplates.value);
}

function confirmSelection() {
  emit('confirm', selectedTemplates.value);
}

function toggleViewMode() {
  viewMode.value = viewMode.value === 'grid' ? 'list' : 'grid';
}

function getDataTypeBadgeClass(dataType: AttributeDataType): string {
  switch (dataType) {
    case 'STRING': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    case 'NUMBER': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'BOOLEAN': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'DATE': return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    case 'JSON': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
  }
}

// Initialize
onMounted(() => {
  loadTemplates();
});

// Watch for prop changes
watch(() => props.initialSelection, (newSelection) => {
  selectedTemplates.value = [...newSelection];
}, { deep: true });
</script>

<style scoped>
.attribute-template-selector {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.attribute-template-selector__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.attribute-template-selector__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-foreground);
}

.attribute-template-selector__filters {
  border-bottom: 1px solid var(--color-border);
  padding-bottom: 1rem;
}

.template-filters {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .template-filters {
    grid-template-columns: repeat(4, 1fr);
  }
}

.template-filters__search,
.template-filters__group,
.template-filters__type,
.template-filters__required {
  width: 100%;
}

.attribute-template-selector__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem 0;
  color: var(--color-muted);
}

.attribute-template-selector__error {
  padding: 1rem 0;
}

.attribute-template-selector__empty {
  padding: 2rem 0;
}

.template-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .template-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .template-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.template-card {
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.template-card:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  border-color: rgb(59 130 246);
}

.dark .template-card:hover {
  border-color: rgb(96 165 250);
}

.template-card--selected {
  border-color: rgb(59 130 246);
  background-color: rgb(239 246 255 / 0.5);
}

.dark .template-card--selected {
  border-color: rgb(96 165 250);
  background-color: rgb(30 58 138 / 0.2);
}

.template-card--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.template-card__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.template-card__title {
  font-weight: 500;
  color: var(--color-foreground);
  flex: 1;
}

.template-card__badges {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-left: 0.5rem;
}

.template-card__content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.template-card__name {
  font-size: 0.875rem;
  color: var(--color-muted);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.template-card__description {
  font-size: 0.875rem;
  color: var(--color-muted);
}

.template-card__group,
.template-card__unit {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--color-muted);
}

.template-card__footer {
  display: flex;
  justify-content: flex-end;
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.template-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.template-item:hover {
  background-color: var(--color-hover);
}

.template-item--selected {
  border-color: rgb(59 130 246);
  background-color: rgb(239 246 255 / 0.5);
}

.dark .template-item--selected {
  border-color: rgb(96 165 250);
  background-color: rgb(30 58 138 / 0.2);
}

.template-item--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.template-item__checkbox {
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.template-item__content {
  flex: 1;
  min-width: 0;
}

.template-item__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.template-item__title {
  font-weight: 500;
  color: var(--color-foreground);
}

.template-item__badges {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-left: 0.5rem;
}

.template-item__details {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: var(--color-muted);
  margin-bottom: 0.25rem;
}

.template-item__name {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.template-item__group,
.template-item__unit {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.template-item__description {
  font-size: 0.875rem;
  color: var(--color-muted);
}

.attribute-template-selector__selection {
  border-top: 1px solid var(--color-border);
  padding-top: 1rem;
}

.selection-summary__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.selection-summary__title {
  font-weight: 500;
  color: var(--color-foreground);
}

.selection-summary__tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.attribute-template-selector__actions-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
  border-top: 1px solid var(--color-border);
  padding-top: 1rem;
}
</style>