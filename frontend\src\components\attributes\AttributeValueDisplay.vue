<template>
  <div class="attribute-value-display">
    <div
      v-if="!editable || !editing"
      class="attribute-value-display__view"
      :class="{ 'attribute-value-display__view--editable': editable }"
      @click="startEdit"
    >
      <span class="attribute-value-display__value">
        {{ formattedValue.displayValue }}
      </span>
      <button
        v-if="editable"
        class="attribute-value-display__edit-button inline-flex items-center justify-center p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-800"
        @click.stop="startEdit"
      >
        <Icon name="edit" size="12" />
      </button>
    </div>

    <div v-else class="attribute-value-display__edit">
      <component
        :is="getInputComponent()"
        v-model="editValue"
        :placeholder="getPlaceholder()"
        :min="attribute.template.minValue"
        :max="attribute.template.maxValue"
        :options="getSelectOptions()"
        :class="getInputClass()"
        @keydown.enter="saveEdit"
        @keydown.escape="cancelEdit"
        @blur="saveEdit"
        ref="inputRef"
      />
      <div class="attribute-value-display__edit-actions">
        <button
          @click="saveEdit"
          class="inline-flex items-center justify-center p-1 bg-green-600 text-white hover:bg-green-700 rounded transition-colors"
        >
          <Icon name="check" size="12" />
        </button>
        <button
          @click="cancelEdit"
          class="inline-flex items-center justify-center p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-800"
        >
          <Icon name="x" size="12" />
        </button>
      </div>
    </div>

    <div v-if="error" class="attribute-value-display__error">
      <span class="text-red-500 text-xs">{{ error }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, watch } from 'vue';
import type { EquipmentModelAttributeWithTemplate } from '@/types/attributes';
import { formatAttributeValue, convertValueToType, convertValueToString } from '@/utils/attributes';
import VoltInputText from '@/volt/InputText.vue';
import VoltInputNumber from '@/volt/InputNumber.vue';
import VoltSelect from '@/volt/Select.vue';
import VoltCheckbox from '@/volt/Checkbox.vue';
import Icon from '@/components/ui/Icon.vue';

interface Props {
  attribute: EquipmentModelAttributeWithTemplate;
  editable?: boolean;
  showUnit?: boolean;
  formatNumbers?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  editable: false,
  showUnit: true,
  formatNumbers: true
});

const emit = defineEmits<{
  'edit': [];
  'save': [value: string | number | boolean | Date];
  'cancel': [];
}>();

// State
const editing = ref(false);
const editValue = ref<any>(null);
const error = ref<string | null>(null);
const inputRef = ref<any>(null);

// Computed properties
const formattedValue = computed(() => {
  return formatAttributeValue(props.attribute);
});

// Methods
function getInputComponent() {
  switch (props.attribute.template.dataType) {
    case 'NUMBER':
      return VoltInputNumber;
    case 'BOOLEAN':
      return VoltCheckbox;
    case 'STRING':
      if (props.attribute.template.allowedValues && props.attribute.template.allowedValues.length > 0) {
        return VoltSelect;
      }
      return VoltInputText;
    case 'DATE':
      return VoltInputText; // TODO: Use date picker component
    case 'JSON':
      return VoltInputText; // TODO: Use JSON editor component
    default:
      return VoltInputText;
  }
}

function getPlaceholder(): string {
  switch (props.attribute.template.dataType) {
    case 'NUMBER':
      return 'Введите число...';
    case 'STRING':
      return 'Введите текст...';
    case 'DATE':
      return 'YYYY-MM-DD';
    case 'JSON':
      return 'Введите JSON...';
    default:
      return '';
  }
}

function getSelectOptions() {
  if (props.attribute.template.dataType === 'STRING' && 
      props.attribute.template.allowedValues && 
      props.attribute.template.allowedValues.length > 0) {
    return props.attribute.template.allowedValues.map(value => ({
      label: value,
      value: value
    }));
  }
  return [];
}

function getInputClass(): string {
  return 'attribute-value-display__input';
}

async function startEdit() {
  if (!props.editable) return;

  editing.value = true;
  error.value = null;
  
  // Set initial edit value
  const currentValue = formattedValue.value.rawValue;
  
  switch (props.attribute.template.dataType) {
    case 'NUMBER':
      editValue.value = typeof currentValue === 'number' ? currentValue : parseFloat(String(currentValue)) || 0;
      break;
    case 'BOOLEAN':
      editValue.value = Boolean(currentValue);
      break;
    case 'DATE':
      if (currentValue instanceof Date) {
        editValue.value = currentValue.toISOString().split('T')[0];
      } else {
        editValue.value = new Date().toISOString().split('T')[0];
      }
      break;
    default:
      editValue.value = String(currentValue);
  }

  emit('edit');

  // Focus input after DOM update
  await nextTick();
  if (inputRef.value && inputRef.value.focus) {
    inputRef.value.focus();
  }
}

function cancelEdit() {
  editing.value = false;
  editValue.value = null;
  error.value = null;
  emit('cancel');
}

function saveEdit() {
  if (!editing.value) return;

  try {
    error.value = null;
    
    let convertedValue: string | number | boolean | Date;
    
    switch (props.attribute.template.dataType) {
      case 'NUMBER':
        convertedValue = Number(editValue.value);
        if (isNaN(convertedValue)) {
          throw new Error('Неверное числовое значение');
        }
        
        // Check min/max constraints
        if (props.attribute.template.minValue !== null && convertedValue < props.attribute.template.minValue) {
          throw new Error(`Значение должно быть не менее ${props.attribute.template.minValue}`);
        }
        if (props.attribute.template.maxValue !== null && convertedValue > props.attribute.template.maxValue) {
          throw new Error(`Значение должно быть не более ${props.attribute.template.maxValue}`);
        }
        break;
        
      case 'BOOLEAN':
        convertedValue = Boolean(editValue.value);
        break;
        
      case 'DATE':
        convertedValue = new Date(editValue.value);
        if (isNaN(convertedValue.getTime())) {
          throw new Error('Неверный формат даты');
        }
        break;
        
      case 'JSON':
        try {
          JSON.parse(editValue.value);
          convertedValue = editValue.value;
        } catch {
          throw new Error('Неверный формат JSON');
        }
        break;
        
      default:
        convertedValue = String(editValue.value);
        
        // Check allowed values
        if (props.attribute.template.allowedValues && 
            props.attribute.template.allowedValues.length > 0 &&
            !props.attribute.template.allowedValues.includes(convertedValue)) {
          throw new Error('Значение не входит в список разрешенных');
        }
    }

    editing.value = false;
    emit('save', convertedValue);
    
  } catch (err: any) {
    error.value = err.message;
  }
}

// Watch for attribute changes to reset editing state
watch(() => props.attribute, () => {
  if (editing.value) {
    cancelEdit();
  }
});
</script>

<style scoped>
.attribute-value-display {
  position: relative;
}

.attribute-value-display__view {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.attribute-value-display__view--editable {
  cursor: pointer;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  transition: background-color 0.2s;
}

.attribute-value-display__view--editable:hover {
  background-color: var(--color-hover);
}

.attribute-value-display__value {
  color: var(--color-foreground);
}

.attribute-value-display__edit-button {
  opacity: 0;
  transition: opacity 0.2s;
}

.attribute-value-display__view:hover .attribute-value-display__edit-button {
  opacity: 1;
}

.attribute-value-display__edit {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.attribute-value-display__input {
  flex: 1;
  min-width: 0;
}

.attribute-value-display__edit-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex-shrink: 0;
}

.attribute-value-display__error {
  margin-top: 0.25rem;
}
</style>