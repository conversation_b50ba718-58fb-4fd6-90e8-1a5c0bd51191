# Attribute Components

This directory contains Vue components for working with dynamic attributes in the PartTec3 system. These components provide a complete solution for displaying, editing, filtering, and managing attributes for different entity types (parts, catalog items, equipment models).

## Components Overview

### AttributeEditor.vue
A comprehensive attribute management component that allows users to view, add, edit, and delete attributes for an entity.

**Features:**
- Display attributes grouped by attribute groups
- Add new attributes using template selection
- Edit existing attribute values inline
- Delete attributes (with protection for required attributes)
- Support for all attribute data types (STRING, NUMBER, BOOLEAN, DATE, JSON)
- Real-time validation
- Loading states and error handling

**Props:**
- `entityType`: Type of entity ('part' | 'catalogItem' | 'equipmentModel')
- `entityId`: ID of the entity
- `title`: Component title (default: 'Атрибуты')
- `readonly`: Whether the component is read-only

**Events:**
- `attribute-added`: Emitted when a new attribute is added
- `attribute-updated`: Emitted when an attribute is updated
- `attribute-deleted`: Emitted when an attribute is deleted

### AttributeDisplay.vue
A read-only component for displaying attributes in an organized, user-friendly format.

**Features:**
- Group attributes by attribute groups
- Compact and expanded display modes
- Optional filtering capabilities
- Customizable display options (show units, descriptions, etc.)
- Support for external attribute data or automatic loading

**Props:**
- `entityType`: Type of entity
- `entityId`: ID of the entity
- `title`: Component title
- `compact`: Use compact display mode
- `showHeader`: Show component header
- `showSummary`: Show attribute count summary
- `showGroupHeaders`: Show group headers
- `showRequiredBadges`: Show required attribute badges
- `showDescriptions`: Show attribute descriptions
- `showUnits`: Show units in values
- `showFilters`: Show filtering interface
- `formatNumbers`: Format numeric values
- `attributes`: Optional external attribute data

### AttributeFilter.vue
A comprehensive filtering component for attributes with support for various filter types.

**Features:**
- Text search across attribute values
- Filter by attribute groups
- Filter by data types
- Numeric range filters for NUMBER attributes
- Multi-select filters for STRING attributes with allowed values
- Boolean filters for BOOLEAN attributes
- Required/optional filter
- Active filter summary with removable tags
- Collapsible interface

**Props:**
- `title`: Component title
- `entityType`: Type of entity (optional)
- `initialFilters`: Initial filter values
- `expanded`: Whether the component is initially expanded

**Events:**
- `filter-change`: Emitted when filters change
- `clear-filters`: Emitted when all filters are cleared

### AttributeTemplateSelector.vue
A component for selecting attribute templates when adding new attributes.

**Features:**
- Grid and list view modes
- Search and filter templates
- Multi-select or single-select modes
- Template information display (data type, units, constraints)
- Selection summary with removable tags
- Support for external template data or automatic loading

**Props:**
- `title`: Component title
- `availableTemplates`: Optional external template data
- `excludeTemplateIds`: Template IDs to exclude
- `multiSelect`: Allow multiple selection
- `showSelection`: Show selection summary
- `showActions`: Show action buttons
- `initialSelection`: Initial selected templates
- `filter`: Additional filter criteria

**Events:**
- `template-select`: Emitted when a template is selected
- `template-deselect`: Emitted when a template is deselected
- `selection-change`: Emitted when selection changes
- `confirm`: Emitted when selection is confirmed
- `cancel`: Emitted when selection is cancelled

### AttributeValueDisplay.vue
A component for displaying and optionally editing individual attribute values.

**Features:**
- Format values based on data type and units
- Inline editing with appropriate input components
- Real-time validation during editing
- Support for all attribute data types
- Constraint information display

**Props:**
- `attribute`: The attribute to display
- `editable`: Whether the value can be edited
- `showUnit`: Show units in display
- `formatNumbers`: Format numeric values

**Events:**
- `edit`: Emitted when editing starts
- `save`: Emitted when a new value is saved
- `cancel`: Emitted when editing is cancelled

### AttributeForm.vue
A form component for adding new attributes or editing existing ones.

**Features:**
- Template selection for new attributes
- Appropriate input components based on data type
- Real-time validation with constraint checking
- Template information display
- Support for all attribute data types and constraints

**Props:**
- `attribute`: Existing attribute for editing (optional)
- `availableTemplates`: Available templates for selection
- `loading`: Whether the form is in loading state

**Events:**
- `submit`: Emitted when form is submitted
- `cancel`: Emitted when form is cancelled

## Usage Examples

### Basic Attribute Display
```vue
<template>
  <AttributeDisplay
    entity-type="equipmentModel"
    entity-id="CAT-320D"
    title="Характеристики техники"
  />
</template>
```

### Attribute Editor with Events
```vue
<template>
  <AttributeEditor
    entity-type="part"
    :entity-id="partId"
    @attribute-added="onAttributeAdded"
    @attribute-updated="onAttributeUpdated"
    @attribute-deleted="onAttributeDeleted"
  />
</template>

<script setup>
function onAttributeAdded(attribute) {
  console.log('New attribute added:', attribute);
}

function onAttributeUpdated(attribute) {
  console.log('Attribute updated:', attribute);
}

function onAttributeDeleted(attributeId) {
  console.log('Attribute deleted:', attributeId);
}
</script>
```

### Attribute Filter with Custom Handling
```vue
<template>
  <AttributeFilter
    title="Фильтры запчастей"
    :initial-filters="currentFilters"
    @filter-change="applyFilters"
  />
</template>

<script setup>
const currentFilters = ref({
  searchQuery: '',
  groupIds: [],
  dataTypes: ['NUMBER'],
  numericRanges: {
    1: { min: 10, max: 100 }
  }
});

function applyFilters(filters) {
  currentFilters.value = filters;
  // Apply filters to your data
}
</script>
```

### Template Selector for Adding Attributes
```vue
<template>
  <AttributeTemplateSelector
    :available-templates="templates"
    :exclude-template-ids="usedTemplateIds"
    @confirm="addSelectedAttributes"
  />
</template>

<script setup>
function addSelectedAttributes(templates) {
  templates.forEach(template => {
    // Add attribute with default value
    addAttribute({
      templateId: template.id,
      value: getDefaultValue(template.dataType)
    });
  });
}
</script>
```

## Data Types Support

The components support all attribute data types defined in the system:

- **STRING**: Text values with optional allowed values
- **NUMBER**: Numeric values with min/max constraints and tolerance
- **BOOLEAN**: True/false values
- **DATE**: Date values
- **JSON**: Complex structured data

## Integration with Backend

The components integrate with the tRPC API through the `useAttributes` composable and `useTrpc` composable. They expect the following API endpoints:

- `crud.attributeTemplate.findMany` - Get available templates
- `crud.attributeGroup.findMany` - Get attribute groups
- `equipmentModelAttribute.findByEquipmentId` - Get attributes for entity
- `equipmentModelAttribute.create` - Create new attribute
- `equipmentModelAttribute.update` - Update existing attribute
- `equipmentModelAttribute.delete` - Delete attribute

## Styling

Components use Tailwind CSS classes and are designed to work with both light and dark themes. They follow the design system established by the Volt UI components.

## Testing

A test component (`AttributeComponents.test.vue`) is provided to demonstrate and test all components with mock data.