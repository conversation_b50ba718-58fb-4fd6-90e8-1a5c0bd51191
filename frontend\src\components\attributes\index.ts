// Attribute Components
export { default as AttributeEditor } from './AttributeEditor.vue';
export { default as AttributeDisplay } from './AttributeDisplay.vue';
export { default as AttributeFilter } from './AttributeFilter.vue';
export { default as AttributeTemplateSelector } from './AttributeTemplateSelector.vue';
export { default as AttributeValueDisplay } from './AttributeValueDisplay.vue';
export { default as AttributeForm } from './AttributeForm.vue';

// Re-export types for convenience
export type {
  EquipmentModelAttributeWithTemplate,
  AttributeTemplate,
  AttributeFormData,
  EditAttributeFormData,
  AttributeManagementState,
  AttributeTemplateFilter,
  GroupedAttributes,
  FormattedAttributeValue,
  AttributeComponentProps,
  AttributeEvents
} from '@/types/attributes';