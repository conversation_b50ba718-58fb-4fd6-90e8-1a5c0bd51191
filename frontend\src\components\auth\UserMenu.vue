<template>
  <div class="relative">
    <!-- Кнопка меню пользователя с Volt Avatar -->
    <SecondaryButton
      @click="toggleMenu"
      text
      class="flex items-center space-x-3"
    >
      <Avatar
        :image="userAvatar"
        :label="userInitials"
        size="normal"
        shape="circle"
      />

      <!-- Имя пользователя -->
      <div class="hidden md:block text-left">
        <p class="text-sm font-medium text-surface-700">
          {{ displayName }}
        </p>
        <p class="text-xs text-surface-500">
          {{ roleLabel }}
        </p>
      </div>

      <!-- Стрелка -->
      <Icon name="pi pi-chevron-down" class="text-surface-400 transition-transform duration-200" :class="{ 'rotate-180': isMenuOpen }" />
    </SecondaryButton>

    <!-- Выпадающее меню с Volt Menu -->
    <Menu
      ref="menu"
      :model="menuItems"
      :popup="true"
      class="w-56"
    >
      <template #start>
        <div class="px-4 py-3 border-b border-surface-200">
          <p class="text-sm font-medium text-surface-900">
            {{ displayName }}
          </p>
          <p class="text-sm text-surface-500">
            {{ user?.email }}
          </p>
          <p class="text-xs text-surface-400 mt-1">
            {{ roleLabel }}
          </p>
        </div>
      </template>
    </Menu>

    <!-- Overlay для закрытия меню -->
    <div
      v-if="isMenuOpen"
      class="fixed inset-0 z-40"
      @click="closeMenu"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAuth } from '@/composables/useAuth'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import Avatar from '@/volt/Avatar.vue'
import Menu from '@/volt/Menu.vue'
import { navigate } from 'astro:transitions/client'
import Icon from '@/components/ui/Icon.vue'

// Composables
const {
  user,
  displayName,
  userAvatar,
  userRole,
  signOut,
  loadingState
} = useAuth()

// Локальное состояние
const menu = ref()
const isMenuOpen = ref(false)

// Вычисляемые свойства
const userInitials = computed(() => {
  if (!displayName.value) return 'U'
  return displayName.value
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
})

const roleLabel = computed(() => {
  switch (userRole.value) {
    case 'ADMIN':
      return 'Администратор'
    case 'SHOP':
      return 'Владелец магазина'
    case 'USER':
      return 'Пользователь'
    case 'GUEST':
      return 'Гость'
    default:
      return 'Пользователь'
  }
})

const isSigningOut = computed(() => loadingState.signOut)

// Пункты меню
const menuItems = computed(() => [
  {
    label: 'Профиль',
    icon: 'pi pi-user',
    command: () => {
      navigate('/admin/profile')
    }
  },
  {
    label: 'Настройки',
    icon: 'pi pi-cog',
    command: () => {
      navigate('/admin/settings')
    }
  },
  {
    separator: true
  },
  {
    label: isSigningOut.value ? 'Выход...' : 'Выйти',
    icon: 'pi pi-sign-out',
    class: 'text-red-600',
    command: handleSignOut
  }
])

// Методы
const toggleMenu = (event: Event) => {
  menu.value.toggle(event)
}

const handleSignOut = async () => {
  // Перенаправляем на страницу выхода, которая выполнит выход и перенаправит на логин
  navigate('/admin/logout')
}
</script>

<style scoped>
/* Дополнительные стили для анимаций */
.rotate-180 {
  transform: rotate(180deg);
}
</style>
