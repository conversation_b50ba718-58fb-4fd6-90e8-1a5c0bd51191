<template>
  <div class="catalog-browser">
    <!-- Заголовок и переключатели режимов отображения -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center gap-4">
        <h1 class="text-2xl font-semibold text-surface-900 dark:text-surface-0">
          Каталог запчастей
        </h1>
        <div v-if="totalCount !== null" class="text-surface-500 dark:text-surface-400">
          {{ totalCount }} {{ pluralize(totalCount, ['запчасть', 'запчасти', 'запчастей']) }}
        </div>
      </div>

      <!-- Переключатели режимов отображения -->
      <div class="flex items-center gap-2">
        <SecondaryButton :class="{ 'bg-primary text-primary-contrast': viewPreferences.mode === 'grid' }"
          @click="setViewMode('grid')" icon-only title="Сетка">
          <template #icon>
            <GridIcon />
          </template>
        </SecondaryButton>
        <SecondaryButton :class="{ 'bg-primary text-primary-contrast': viewPreferences.mode === 'list' }"
          @click="setViewMode('list')" icon-only title="Список">
          <template #icon>
            <ListIcon />
          </template>
        </SecondaryButton>
        <SecondaryButton :class="{ 'bg-primary text-primary-contrast': viewPreferences.mode === 'table' }"
          @click="setViewMode('table')" icon-only title="Таблица">
          <template #icon>
            <TableIcon />
          </template>
        </SecondaryButton>
      </div>
    </div>

    <!-- Панель фильтров -->
    <div v-if="showFilters" class="mb-6">
      <Card>
        <template #content>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Поиск -->
            <div>
              <label class="block text-sm font-medium mb-2">Поиск</label>
              <InputText v-model="searchQuery" placeholder="Поиск запчастей..." @input="onSearchInput" />
            </div>

            <!-- Категории -->
            <div>
              <label class="block text-sm font-medium mb-2">Категории</label>
              <MultiSelect v-model="selectedCategoryIds" :options="categories" option-label="name" option-value="id"
                placeholder="Выберите категории" @change="setCategoryIds($event.value || [])" />
            </div>

            <!-- Бренды -->
            <div>
              <label class="block text-sm font-medium mb-2">Бренды</label>
              <MultiSelect v-model="selectedBrandIds" :options="brands" option-label="name" option-value="id"
                placeholder="Выберите бренды" @change="setBrandIds($event.value || [])" />
            </div>

            <!-- Кнопка сброса фильтров -->
            <div class="flex items-end">
              <SecondaryButton v-if="hasFilters" @click="resetFilters" outlined>
                Сбросить фильтры
              </SecondaryButton>
            </div>
          </div>
        </template>
      </Card>
    </div>

    <!-- Контент каталога -->
    <div class="catalog-content">
      <!-- Состояние загрузки -->
      <div v-if="loading" class="flex justify-center py-12">
        <Spinner size="lg" variant="primary" label="Загрузка..." :centered="true" />
      </div>

      <!-- Ошибка -->
      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-500 mb-4">{{ error }}</div>
        <SecondaryButton @click="refetch">Повторить</SecondaryButton>
      </div>

      <!-- Пустое состояние -->
      <div v-else-if="!parts?.length" class="text-center py-12">
        <div class="text-surface-500 dark:text-surface-400 mb-4">
          {{ hasFilters ? 'Запчасти не найдены' : 'Каталог пуст' }}
        </div>
        <SecondaryButton v-if="hasFilters" @click="resetFilters">
          Сбросить фильтры
        </SecondaryButton>
      </div>

      <!-- Отображение запчастей -->
      <div v-else>
        <!-- Сетка -->
        <CatalogGrid v-if="viewPreferences.mode === 'grid'" :parts="parts" @part-click="onPartClick" />

        <!-- Список -->
        <CatalogList v-else-if="viewPreferences.mode === 'list'" :parts="parts" @part-click="onPartClick" />

        <!-- Таблица -->
        <CatalogTable v-else-if="viewPreferences.mode === 'table'" :parts="parts" @part-click="onPartClick" />

        <!-- Пагинация -->
        <CatalogPagination v-if="totalCount && totalCount > viewPreferences.itemsPerPage" :current-page="currentPage"
          :total-items="totalCount" :items-per-page="viewPreferences.itemsPerPage" @page-change="onPageChange"
          @items-per-page-change="setItemsPerPage" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { useTrpc } from '../../composables/useTrpc';
import { useCatalogFilters } from '../../composables/useCatalogFilters';
import { useDebounce } from '../../composables/useDebounce';
import Card from '../../volt/Card.vue';
import InputText from '../../volt/InputText.vue';
import MultiSelect from '../../volt/MultiSelect.vue';
import SecondaryButton from '../../volt/SecondaryButton.vue';
import CatalogGrid from './CatalogGrid.vue';
import CatalogList from './CatalogList.vue';
import CatalogTable from './CatalogTable.vue';
import CatalogPagination from './CatalogPagination.vue';
import Spinner from '@/components/ui/Spinner.vue';

// Иконки (предполагаем, что они доступны)
const GridIcon = () => '⊞';
const ListIcon = () => '☰';
const TableIcon = () => '⊟';

interface Props {
  initialFilters?: any;
  showFilters?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showFilters: true,
});

const emit = defineEmits<{
  partClick: [part: any];
}>();

// Composables
const trpc = useTrpc();
const {
  filters,
  viewPreferences,
  hasFilters,
  queryParams,
  setSearch,
  setCategoryIds,
  setBrandIds,
  resetFilters,
  setViewMode,
  setItemsPerPage,
} = useCatalogFilters(props.initialFilters);

// Состояние
const loading = ref(false);
const error = ref<string | null>(null);
const parts = ref<any[]>([]);
const totalCount = ref<number | null>(null);
const currentPage = ref(1);

// Данные для фильтров
const categories = ref<any[]>([]);
const brands = ref<any[]>([]);

// Локальные состояния для фильтров
const searchQuery = ref(filters.value.search || '');
const selectedCategoryIds = ref(filters.value.categoryIds || []);
const selectedBrandIds = ref(filters.value.brandIds || []);

// Debounced поиск
const debouncedSearch = useDebounce(searchQuery, 300);

// Загрузка данных для фильтров
const loadFilterData = async () => {
  try {
    const [categoriesResult, brandsResult] = await Promise.all([
      trpc.crud.partCategory.findMany(),
      trpc.crud.brand.findMany(),
    ]);

    if (categoriesResult) categories.value = categoriesResult as any[];
    if (brandsResult) brands.value = brandsResult as any[];
  } catch (err) {
    console.error('Ошибка загрузки данных для фильтров:', err);
  }
};

// Загрузка запчастей
const loadParts = async () => {
  if (loading.value) return; // Предотвращаем множественные запросы
  
  try {
    loading.value = true;
    error.value = null;

    const query = {
      ...queryParams.value,
      take: viewPreferences.value.itemsPerPage,
      skip: (currentPage.value - 1) * viewPreferences.value.itemsPerPage,
      orderBy: viewPreferences.value.sortBy ? {
        [viewPreferences.value.sortBy]: viewPreferences.value.sortOrder
      } : undefined,
      include: {
        partCategory: true,
        image: true,
        attributes: {
          include: {
            template: true,
          },
        },
      },
    };

    const result = await trpc.crud.part.findMany(query);

    if (result) {
      parts.value = result as any[];
      // В реальном приложении totalCount должен приходить отдельно
      totalCount.value = (result as any[]).length;
    }
  } catch (err: any) {
    error.value = err.message || 'Ошибка загрузки запчастей';
    console.error('Ошибка загрузки запчастей:', err);
  } finally {
    loading.value = false;
  }
};

// Обработчики событий
const onSearchInput = () => {
  // Поиск обрабатывается через debounced watcher
};

const onPartClick = (part: any) => {
  emit('partClick', part);
};

const onPageChange = (page: number) => {
  currentPage.value = page;
  loadParts();
};

const refetch = () => {
  loadParts();
};

// Утилита для склонения слов
const pluralize = (count: number, forms: [string, string, string]): string => {
  const cases = [2, 0, 1, 1, 1, 2];
  return forms[(count % 100 > 4 && count % 100 < 20) ? 2 : cases[Math.min(count % 10, 5)]];
};

// Watchers
watch(debouncedSearch, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    setSearch(newValue);
    currentPage.value = 1;
    loadParts();
  }
});

watch(queryParams, (newValue, oldValue) => {
  if (JSON.stringify(newValue) !== JSON.stringify(oldValue)) {
    currentPage.value = 1;
    loadParts();
  }
}, { deep: true });

watch(() => viewPreferences.value.itemsPerPage, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    currentPage.value = 1;
    loadParts();
  }
});

// Инициализация
onMounted(() => {
  loadFilterData();
  loadParts();
});
</script>

<style scoped>
.catalog-browser {
  width: 100%;
}

.catalog-content {
  min-height: 24rem;
}
</style>