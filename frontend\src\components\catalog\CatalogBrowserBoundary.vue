<template>
  <ErrorBoundary variant="detailed" @retry="onRetry">
    <CatalogBrowser :key="key" />
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ErrorBoundary from '@/components/ui/ErrorBoundary.vue'
import CatalogBrowser from '@/components/catalog/CatalogBrowser.vue'

const key = ref(0)
const onRetry = () => {
  // Перемонтируем дочерний узел при повторе
  key.value++
}
</script>

