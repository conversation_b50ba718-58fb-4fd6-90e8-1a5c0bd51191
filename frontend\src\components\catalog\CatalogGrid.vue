<template>
  <div class="catalog-grid">
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <Card
        v-for="part in parts"
        :key="part.id"
        class="part-card cursor-pointer hover:shadow-lg transition-shadow duration-200"
        @click="$emit('partClick', part)"
      >
        <template #header>
          <div class="relative aspect-square bg-surface-100 dark:bg-surface-800 rounded-t-xl overflow-hidden">
            <img
              v-if="part.image?.url"
              :src="part.image.url"
              :alt="part.name || 'Запчасть'"
              class="w-full h-full object-cover"
              loading="lazy"
              @error="handleImageError"
            />
            <div
              v-else
              class="w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500"
            >
              <ImageIcon class="w-12 h-12" />
            </div>
            
            <!-- Бейдж категории -->
            <div
              v-if="part.partCategory"
              class="absolute top-2 left-2 bg-primary text-primary-contrast px-2 py-1 rounded-md text-xs font-medium"
            >
              {{ part.partCategory.name }}
            </div>
          </div>
        </template>

        <template #content>
          <div class="space-y-3">
            <!-- Название -->
            <h3 class="font-medium text-surface-900 dark:text-surface-0 line-clamp-2">
              {{ part.name || `Запчасть #${part.id}` }}
            </h3>

            <!-- Путь в иерархии -->
            <div v-if="part.path" class="text-sm text-surface-500 dark:text-surface-400">
              Путь: {{ part.path }}
            </div>

            <!-- Основные атрибуты -->
            <div v-if="getPartMainAttributes(part).length" class="space-y-1">
              <div
                v-for="attr in getPartMainAttributes(part)"
                :key="attr.id"
                class="flex justify-between text-sm"
              >
                <span class="text-surface-600 dark:text-surface-300">
                  {{ attr.template.title }}:
                </span>
                <span class="text-surface-900 dark:text-surface-0 font-medium">
                  {{ formatAttributeValue(attr) }}
                </span>
              </div>
            </div>

            <!-- Количество совместимых позиций -->
            <div v-if="part.applicabilities?.length" class="text-sm text-surface-500 dark:text-surface-400">
              {{ part.applicabilities.length }} {{ pluralize(part.applicabilities.length, ['позиция', 'позиции', 'позиций']) }}
            </div>
          </div>
        </template>

        <template #footer>
          <div class="flex items-center justify-between pt-3 border-t border-surface-200 dark:border-surface-700">
            <div class="text-xs text-surface-500 dark:text-surface-400">
              ID: {{ part.id }}
            </div>
            <SecondaryButton
              size="small"
              outlined
              @click.stop="$emit('partClick', part)"
            >
              Подробнее
            </SecondaryButton>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
// Removed unused computed import
import Card from '../../volt/Card.vue';
import SecondaryButton from '../../volt/SecondaryButton.vue';

// Простая иконка изображения
const ImageIcon = () => '🖼️';

interface Props {
  parts: any[];
}

const props = defineProps<Props>();

defineEmits<{
  partClick: [part: any];
}>();

// Получение основных атрибутов для отображения в карточке
const getMainAttributes = (part: any) => {
  if (!part.attributes?.length) return [];
  
  // Показываем только первые 3 атрибута
  return part.attributes
    .filter((attr: any) => attr.template && attr.value)
    .slice(0, 3);
};

// Получение основных атрибутов для каждой части
const getPartMainAttributes = (part: any) => getMainAttributes(part);

// Форматирование значения атрибута
const formatAttributeValue = (attr: any): string => {
  if (!attr.value) return '';
  
  const value = attr.value;
  const unit = attr.template.unit;
  
  if (unit) {
    return `${value} ${unit.symbol || unit.name}`;
  }
  
  return value;
};

// Обработка ошибок загрузки изображений
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.style.display = 'none';
  console.warn('Ошибка загрузки изображения:', img.src);
};

// Утилита для склонения слов
const pluralize = (count: number, forms: [string, string, string]): string => {
  const cases = [2, 0, 1, 1, 1, 2];
  return forms[(count % 100 > 4 && count % 100 < 20) ? 2 : cases[Math.min(count % 10, 5)]];
};
</script>

<style scoped>
.catalog-grid {
  width: 100%;
}

.part-card {
  transform: scale(1);
  transition: transform 0.2s ease-in-out;
}

.part-card:hover {
  transform: scale(1.05);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>