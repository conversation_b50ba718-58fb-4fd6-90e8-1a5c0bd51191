<template>
  <div class="catalog-list">
    <div class="space-y-4">
      <Card
        v-for="part in parts"
        :key="part.id"
        class="part-item cursor-pointer hover:shadow-md transition-shadow duration-200"
        @click="$emit('partClick', part)"
      >
        <template #content>
          <div class="flex items-start gap-4">
            <!-- Изображение -->
            <div class="flex-shrink-0 w-20 h-20 bg-surface-100 dark:bg-surface-800 rounded-lg overflow-hidden">
              <img
                v-if="part.image?.url"
                :src="part.image.url"
                :alt="part.name || 'Запчасть'"
                class="w-full h-full object-cover"
                loading="lazy"
              />
              <div
                v-else
                class="w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500"
              >
                <ImageIcon class="w-8 h-8" />
              </div>
            </div>

            <!-- Основная информация -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <!-- Заголовок и категория -->
                  <div class="flex items-center gap-2 mb-2">
                    <h3 class="font-medium text-surface-900 dark:text-surface-0 truncate">
                      {{ part.name || `Запчасть #${part.id}` }}
                    </h3>
                    <Badge
                      v-if="part.partCategory"
                      :value="part.partCategory.name"
                      severity="info"
                      class="text-xs"
                    />
                  </div>

                  <!-- Путь в иерархии -->
                  <div v-if="part.path" class="text-sm text-surface-500 dark:text-surface-400 mb-2">
                    Путь: {{ part.path }}
                  </div>

                  <!-- Атрибуты в строку -->
                  <div v-if="part.attributes?.length" class="flex flex-wrap gap-x-4 gap-y-1 text-sm">
                    <div
                      v-for="attr in part.attributes.slice(0, 5)"
                      :key="attr.id"
                      class="text-surface-600 dark:text-surface-300"
                    >
                      <span class="font-medium">{{ attr.template.title }}:</span>
                      <span class="ml-1 text-surface-900 dark:text-surface-0">
                        {{ formatAttributeValue(attr) }}
                      </span>
                    </div>
                    <div
                      v-if="part.attributes.length > 5"
                      class="text-surface-500 dark:text-surface-400 text-xs"
                    >
                      +{{ part.attributes.length - 5 }} еще
                    </div>
                  </div>
                </div>

                <!-- Действия -->
                <div class="flex-shrink-0 ml-4">
                  <SecondaryButton
                    size="small"
                    outlined
                    @click.stop="$emit('partClick', part)"
                  >
                    Подробнее
                  </SecondaryButton>
                </div>
              </div>

              <!-- Дополнительная информация -->
              <div class="flex items-center justify-between mt-3 pt-3 border-t border-surface-200 dark:border-surface-700">
                <div class="flex items-center gap-4 text-sm text-surface-500 dark:text-surface-400">
                  <span>ID: {{ part.id }}</span>
                  <span v-if="part.applicabilities?.length">
                    {{ part.applicabilities.length }} {{ pluralize(part.applicabilities.length, ['позиция', 'позиции', 'позиций']) }}
                  </span>
                  <span v-if="part.equipmentApplicabilities?.length">
                    {{ part.equipmentApplicabilities.length }} {{ pluralize(part.equipmentApplicabilities.length, ['модель', 'модели', 'моделей']) }} техники
                  </span>
                </div>

                <div class="text-xs text-surface-400 dark:text-surface-500">
                  {{ formatDate(part.updatedAt) }}
                </div>
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import Card from '../../volt/Card.vue';
import Badge from '../../volt/Badge.vue';
import SecondaryButton from '../../volt/SecondaryButton.vue';

// Простая иконка изображения
const ImageIcon = () => '🖼️';

interface Props {
  parts: any[];
}

defineProps<Props>();

defineEmits<{
  partClick: [part: any];
}>();

// Форматирование значения атрибута
const formatAttributeValue = (attr: any): string => {
  if (!attr.value) return '';
  
  const value = attr.value;
  const unit = attr.template.unit;
  
  if (unit) {
    return `${value} ${unit.symbol || unit.name}`;
  }
  
  return value;
};

// Форматирование даты
const formatDate = (date: string | Date): string => {
  if (!date) return '';
  
  const d = new Date(date);
  return d.toLocaleDateString('ru-RU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

// Утилита для склонения слов
const pluralize = (count: number, forms: [string, string, string]): string => {
  const cases = [2, 0, 1, 1, 1, 2];
  return forms[(count % 100 > 4 && count % 100 < 20) ? 2 : cases[Math.min(count % 10, 5)]];
};
</script>

<style scoped>
.catalog-list {
  width: 100%;
}

.part-item {
  transition: all 0.2s ease-in-out;
}

.part-item:hover {
  transform: scale(1.01);
}
</style>