<template>
  <div class="catalog-pagination">
    <Card>
      <template #content>
        <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
          <!-- Информация о записях -->
          <div class="text-sm text-surface-600 dark:text-surface-300">
            Показано {{ startItem }}-{{ endItem }} из {{ totalItems }} записей
          </div>

          <!-- Пагинация -->
          <div class="flex items-center gap-2">
            <!-- Первая страница -->
            <SecondaryButton
              :disabled="currentPage === 1"
              @click="goToPage(1)"
              icon-only
              title="Первая страница"
            >
              <template #icon>
                <FirstPageIcon />
              </template>
            </SecondaryButton>

            <!-- Предыдущая страница -->
            <SecondaryButton
              :disabled="currentPage === 1"
              @click="goToPage(currentPage - 1)"
              icon-only
              title="Предыдущая страница"
            >
              <template #icon>
                <PrevPageIcon />
              </template>
            </SecondaryButton>

            <!-- Номера страниц -->
            <div class="hidden sm:flex items-center gap-1">
              <SecondaryButton
                v-for="page in visiblePages"
                :key="page"
                :class="{
                  'bg-primary text-primary-contrast': page === currentPage,
                  'text-primary': page !== currentPage
                }"
                @click="goToPage(page)"
                size="small"
              >
                {{ page }}
              </SecondaryButton>
            </div>

            <!-- Мобильная версия - текущая страница -->
            <div class="sm:hidden flex items-center gap-2">
              <span class="text-sm text-surface-600 dark:text-surface-300">
                Страница {{ currentPage }} из {{ totalPages }}
              </span>
            </div>

            <!-- Следующая страница -->
            <SecondaryButton
              :disabled="currentPage === totalPages"
              @click="goToPage(currentPage + 1)"
              icon-only
              title="Следующая страница"
            >
              <template #icon>
                <NextPageIcon />
              </template>
            </SecondaryButton>

            <!-- Последняя страница -->
            <SecondaryButton
              :disabled="currentPage === totalPages"
              @click="goToPage(totalPages)"
              icon-only
              title="Последняя страница"
            >
              <template #icon>
                <LastPageIcon />
              </template>
            </SecondaryButton>
          </div>

          <!-- Выбор количества элементов на странице -->
          <div class="flex items-center gap-2 text-sm">
            <span class="text-surface-600 dark:text-surface-300">На странице:</span>
            <Select
              :model-value="itemsPerPage"
              :options="itemsPerPageOptions"
              @update:model-value="changeItemsPerPage"
              class="w-20"
            />
          </div>
        </div>

        <!-- Виртуальная прокрутка (опционально) -->
        <div v-if="enableVirtualScroll" class="mt-4">
          <div class="flex items-center justify-center">
            <SecondaryButton
              v-if="hasMoreItems"
              :loading="loadingMore"
              @click="loadMore"
              outlined
            >
              Загрузить еще
            </SecondaryButton>
          </div>
        </div>
      </template>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import Card from '../../volt/Card.vue';
import SecondaryButton from '../../volt/SecondaryButton.vue';
import Select from '../../volt/Select.vue';

// Простые иконки
const FirstPageIcon = () => '⏮';
const PrevPageIcon = () => '◀';
const NextPageIcon = () => '▶';
const LastPageIcon = () => '⏭';

interface Props {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  enableVirtualScroll?: boolean;
  hasMoreItems?: boolean;
  loadingMore?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  enableVirtualScroll: false,
  hasMoreItems: false,
  loadingMore: false,
});

const emit = defineEmits<{
  pageChange: [page: number];
  itemsPerPageChange: [count: number];
  loadMore: [];
}>();

// Опции для выбора количества элементов на странице
const itemsPerPageOptions = [
  { label: '10', value: 10 },
  { label: '20', value: 20 },
  { label: '50', value: 50 },
  { label: '100', value: 100 },
];

// Вычисляемые свойства
const totalPages = computed(() => Math.ceil(props.totalItems / props.itemsPerPage));

const startItem = computed(() => {
  if (props.totalItems === 0) return 0;
  return (props.currentPage - 1) * props.itemsPerPage + 1;
});

const endItem = computed(() => {
  const end = props.currentPage * props.itemsPerPage;
  return Math.min(end, props.totalItems);
});

// Видимые страницы для пагинации
const visiblePages = computed(() => {
  const pages: number[] = [];
  const total = totalPages.value;
  const current = props.currentPage;
  
  if (total <= 7) {
    // Показываем все страницы если их мало
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // Показываем первую, последнюю и страницы вокруг текущей
    pages.push(1);
    
    if (current > 3) {
      pages.push(-1); // Placeholder для "..."
    }
    
    const start = Math.max(2, current - 1);
    const end = Math.min(total - 1, current + 1);
    
    for (let i = start; i <= end; i++) {
      if (!pages.includes(i)) {
        pages.push(i);
      }
    }
    
    if (current < total - 2) {
      pages.push(-2); // Placeholder для "..."
    }
    
    if (!pages.includes(total)) {
      pages.push(total);
    }
  }
  
  return pages.filter(page => page > 0); // Убираем placeholders для простоты
});

// Методы
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value && page !== props.currentPage) {
    emit('pageChange', page);
  }
};

const changeItemsPerPage = (count: number) => {
  emit('itemsPerPageChange', count);
};

const loadMore = () => {
  emit('loadMore');
};
</script>

<style scoped>
.catalog-pagination {
  margin-top: 1.5rem;
}
</style>