<template>
  <div class="catalog-table">
    <DataTable
      :value="parts"
      :loading="loading"
      striped-rows
      hover
      @row-click="onRowClick"
      class="cursor-pointer"
    >
      <!-- Изображение -->
      <Column header="Фото" :style="{ width: '80px' }">
        <template #body="{ data }">
          <div class="w-12 h-12 bg-surface-100 dark:bg-surface-800 rounded overflow-hidden">
            <img
              v-if="data.image?.url"
              :src="data.image.url"
              :alt="data.name || 'Запчасть'"
              class="w-full h-full object-cover"
              loading="lazy"
            />
            <div
              v-else
              class="w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500"
            >
              <ImageIcon class="w-6 h-6" />
            </div>
          </div>
        </template>
      </Column>

      <!-- ID -->
      <Column field="id" header="ID" sortable :style="{ width: '80px' }">
        <template #body="{ data }">
          <span class="font-mono text-sm">{{ data.id }}</span>
        </template>
      </Column>

      <!-- Название -->
      <Column field="name" header="Название" sortable>
        <template #body="{ data }">
          <div>
            <div class="font-medium text-surface-900 dark:text-surface-0">
              {{ data.name || `Запчасть #${data.id}` }}
            </div>
            <div v-if="data.path" class="text-sm text-surface-500 dark:text-surface-400">
              {{ data.path }}
            </div>
          </div>
        </template>
      </Column>

      <!-- Категория -->
      <Column field="partCategory.name" header="Категория" sortable>
        <template #body="{ data }">
          <Badge
            v-if="data.partCategory"
            :value="data.partCategory.name"
            severity="info"
          />
          <span v-else class="text-surface-400 dark:text-surface-500">—</span>
        </template>
      </Column>

      <!-- Основные атрибуты -->
      <Column header="Атрибуты">
        <template #body="{ data }">
          <div v-if="data.attributes?.length" class="space-y-1">
            <div
              v-for="attr in data.attributes.slice(0, 3)"
              :key="attr.id"
              class="text-sm"
            >
              <span class="text-surface-600 dark:text-surface-300">
                {{ attr.template.title }}:
              </span>
              <span class="ml-1 text-surface-900 dark:text-surface-0 font-medium">
                {{ formatAttributeValue(attr) }}
              </span>
            </div>
            <div
              v-if="data.attributes.length > 3"
              class="text-xs text-surface-500 dark:text-surface-400"
            >
              +{{ data.attributes.length - 3 }} еще
            </div>
          </div>
          <span v-else class="text-surface-400 dark:text-surface-500">—</span>
        </template>
      </Column>

      <!-- Совместимость -->
      <Column header="Совместимость">
        <template #body="{ data }">
          <div class="space-y-1 text-sm">
            <div v-if="data.applicabilities?.length" class="text-surface-600 dark:text-surface-300">
              {{ data.applicabilities.length }} {{ pluralize(data.applicabilities.length, ['позиция', 'позиции', 'позиций']) }}
            </div>
            <div v-if="data.equipmentApplicabilities?.length" class="text-surface-600 dark:text-surface-300">
              {{ data.equipmentApplicabilities.length }} {{ pluralize(data.equipmentApplicabilities.length, ['модель', 'модели', 'моделей']) }}
            </div>
            <span v-if="!data.applicabilities?.length && !data.equipmentApplicabilities?.length" class="text-surface-400 dark:text-surface-500">—</span>
          </div>
        </template>
      </Column>

      <!-- Дата обновления -->
      <Column field="updatedAt" header="Обновлено" sortable :style="{ width: '120px' }">
        <template #body="{ data }">
          <span class="text-sm text-surface-500 dark:text-surface-400">
            {{ formatDate(data.updatedAt) }}
          </span>
        </template>
      </Column>

      <!-- Действия -->
      <Column header="Действия" :style="{ width: '120px' }">
        <template #body="{ data }">
          <SecondaryButton
            size="small"
            outlined
            @click.stop="$emit('partClick', data)"
          >
            Подробнее
          </SecondaryButton>
        </template>
      </Column>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import DataTable from '../../volt/DataTable.vue';
import Column from 'primevue/column';
import Badge from '../../volt/Badge.vue';
import SecondaryButton from '../../volt/SecondaryButton.vue';

// Простая иконка изображения
const ImageIcon = () => '🖼️';

interface Props {
  parts: any[];
  loading?: boolean;
  totalRecords?: number;
  itemsPerPage?: number;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  totalRecords: 0,
  itemsPerPage: 20,
});

const emit = defineEmits<{
  partClick: [part: any];
}>();

// Обработчики событий
const onRowClick = (event: any) => {
  emit('partClick', event.data);
};

// Removed onPageChange since we use separate pagination component

// Форматирование значения атрибута
const formatAttributeValue = (attr: any): string => {
  if (!attr.value) return '';
  
  const value = attr.value;
  const unit = attr.template.unit;
  
  if (unit) {
    return `${value} ${unit.symbol || unit.name}`;
  }
  
  return value;
};

// Форматирование даты
const formatDate = (date: string | Date): string => {
  if (!date) return '';
  
  const d = new Date(date);
  return d.toLocaleDateString('ru-RU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

// Утилита для склонения слов
const pluralize = (count: number, forms: [string, string, string]): string => {
  const cases = [2, 0, 1, 1, 1, 2];
  return forms[(count % 100 > 4 && count % 100 < 20) ? 2 : cases[Math.min(count % 10, 5)]];
};
</script>

<style scoped>
.catalog-table {
  width: 100%;
}

:deep(.p-datatable-tbody > tr) {
  cursor: pointer;
}

:deep(.p-datatable-tbody > tr:hover) {
  background-color: var(--p-surface-100);
}

[data-theme="dark"] :deep(.p-datatable-tbody > tr:hover) {
  background-color: var(--p-surface-800);
}
</style>