<template>
  <div class="relative">
    <div class="absolute inset-0 bg-zinc-900/5 rounded-2xl" />
    <Ripple />

    <div class="relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-800 rounded-2xl p-6 shadow-2xl">
      <div class="grid lg:grid-cols-2 gap-8">
        <!-- AI Search Interface -->
        <div class="space-y-3">
          <div class="flex items-center gap-3 mb-3">
            <div class="relative">
              <div class="w-12 h-12 bg-zinc-800 rounded-xl flex items-center justify-center">
                <Brain class="w-6 h-6 text-white" />
              </div>
              <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse" />
            </div>
            <div>
              <h3 class="text-2xl font-semibold text-white">ИИ-Ассистент поиска запчастей</h3>
              <p class="text-base text-gray-100">Умный поиск с голосовым вводом</p>
            </div>
          </div>

          <div class="relative">
            <div class="absolute -inset-0.5 bg-blue-500 rounded-lg blur opacity-30" />
            <div class="relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-800 rounded-lg p-4">
              <div class="flex items-center gap-3 mb-4">
                <Search class="w-5 h-5 text-gray-300" />
                <input
                  type="text"
                  placeholder="Опишите нужную запчасть или технику..."
                  v-model="searchQuery"
                  class="flex-1 bg-transparent text-white placeholder-gray-300 focus:outline-none"
                />
                <Motion
                  @click="toggleListening"
                  :class="`p-2 rounded-lg transition-all ${
                    isListening
                      ? 'bg-red-500 text-white animate-pulse'
                      : 'bg-zinc-800 text-gray-300 hover:bg-zinc-700'
                  }`"
                  :whileHover="{ scale: 1.05 }"
                  :whileTap="{ scale: 0.95 }"
                >
                  <Mic class="w-4 h-4" />
                </Motion>
              </div>

              <Motion
                v-if="aiResponse"
                :initial="{ opacity: 0, y: 20 }"
                :animate="{ opacity: 1, y: 0 }"
                class="bg-zinc-800 border border-zinc-700 rounded-lg p-4"
              >
                <div class="flex items-start gap-3">
                  <Bot class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                  <div class="text-base text-gray-100">{{ aiResponse }}</div>
                </div>
              </Motion>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg p-4">
              <div class="flex items-center gap-2 mb-2">
                <MessageSquare class="w-4 h-4 text-blue-400" />
                <span class="text-base font-medium text-white">Текстовый ввод</span>
              </div>
              <p class="text-sm text-gray-100">Опишите деталь естественным языком</p>
            </div>
            <div class="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg p-4">
              <div class="flex items-center gap-2 mb-2">
                <Headphones class="w-4 h-4 text-blue-400" />
                <span class="text-base font-medium text-white">Голосовой ввод</span>
              </div>
              <p class="text-sm text-gray-100">Говорите - ИИ поймет и найдет</p>
            </div>
          </div>
        </div>

        <!-- AI Features -->
        <div class="space-y-3">
          <h4 class="text-2xl font-semibold text-white flex items-center gap-2">
            <Wand2 class="w-5 h-5 text-blue-400" />
            Возможности ИИ-поиска
          </h4>

          <div class="space-y-4">
            <Motion
              v-for="(feature, index) in features"
              :key="index"
              :initial="{ opacity: 0, x: 20 }"
              :whileInView="{ opacity: 1, x: 0 }"
              :transition="{ delay: index * 0.1 }"
              :viewport="{ once: true }"
              class="flex items-start gap-4 p-4 bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg transition-all duration-300"
            >
              <div class="w-10 h-10 bg-zinc-700 rounded-lg flex items-center justify-center flex-shrink-0">
                <component :is="feature.icon" class="w-5 h-5 text-white" />
              </div>
              <div>
                <h5 class="font-medium text-white mb-1">{{ feature.title }}</h5>
                <p class="text-base text-gray-100">{{ feature.description }}</p>
              </div>
            </Motion>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Motion } from 'motion-v'
import { 
  Search, 
  Brain, 
  Mic, 
  Bot, 
  MessageSquare, 
  Headphones, 
  Wand2,
  Target,
  Zap
} from 'lucide-vue-next'
import Ripple from './Ripple.vue'

const isListening = ref(false)
const searchQuery = ref('')
const aiResponse = ref('')
const step = ref(0)

const demoSteps = [
  {
    query: "Нужен сальник для гидроцилиндра Caterpillar 320D",
    response: "Анализирую запрос... Найдено 15 совместимых сальников для гидроцилиндра Caterpillar 320D. Показываю варианты с экономией до 45%.",
  },
  {
    query: "Масляный фильтр для двигателя Cummins ISX15",
    response: "Обрабатываю... Найдено 8 аналогов масляного фильтра. Все варианты в наличии, совместимость 98-100%.",
  },
]

const features = [
  {
    icon: Brain,
    title: "Понимание контекста",
    description: "ИИ анализирует техническое описание и находит точные аналоги",
  },
  {
    icon: MessageSquare,
    title: "Диалоговый режим",
    description: "Задавайте уточняющие вопросы для точного поиска",
  },
  {
    icon: Target,
    title: "Умная фильтрация",
    description: "Автоматический подбор по техническим характеристикам",
  },
  {
    icon: Zap,
    title: "Мгновенный результат",
    description: "Поиск за секунды вместо часов навигации по каталогу",
  },
]

let interval: NodeJS.Timeout | null = null

const toggleListening = () => {
  isListening.value = !isListening.value
}

onMounted(() => {
  interval = setInterval(() => {
    const currentStep = demoSteps[step.value]
    searchQuery.value = currentStep.query
    
    setTimeout(() => {
      aiResponse.value = currentStep.response
    }, 1500)
    
    setTimeout(() => {
      step.value = (step.value + 1) % demoSteps.length
      searchQuery.value = ""
      aiResponse.value = ""
    }, 4000)
  }, 6000)
})

onUnmounted(() => {
  if (interval) {
    clearInterval(interval)
  }
})
</script>