<template>
  <div class="relative w-full h-full overflow-hidden">
    <div class="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-pink-900/20" />
    
    <!-- Aurora effect -->
    <div class="absolute inset-0 mix-blend-screen opacity-70">
      <div 
        v-for="(aurora, index) in auroras" 
        :key="index"
        class="absolute rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"
        :class="aurora.color"
        :style="{
          left: `${aurora.x}%`,
          top: `${aurora.y}%`,
          width: `${aurora.size}px`,
          height: `${aurora.size}px`,
          animationDelay: `${aurora.delay}s`,
          animationDuration: `${aurora.duration}s`
        }"
      />
    </div>
    
    <!-- Content slot -->
    <div class="relative z-10">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Aurora {
  x: number
  y: number
  size: number
  color: string
  delay: number
  duration: number
}

const auroras = ref<Aurora[]>([])

const colors = [
  'bg-blue-500/30',
  'bg-purple-500/30',
  'bg-pink-500/30',
  'bg-green-500/30',
  'bg-yellow-500/30',
  'bg-indigo-500/30'
]

const generateAurora = (): Aurora => ({
  x: Math.random() * 100,
  y: Math.random() * 100,
  size: Math.random() * 400 + 200,
  color: colors[Math.floor(Math.random() * colors.length)],
  delay: Math.random() * 5,
  duration: Math.random() * 10 + 5
})

onMounted(() => {
  // Generate initial auroras
  for (let i = 0; i < 6; i++) {
    auroras.value.push(generateAurora())
  }
})
</script>