<template>
  <div class="space-y-12">
    <Motion
      v-for="(section, index) in catalogSectionsData"
      :key="section.id"
      :initial="{ opacity: 0, x: index % 2 === 0 ? -60 : 60 }"
      :whileInView="{ opacity: 1, x: 0 }"
      :transition="{ duration: 0.8, delay: index * 0.1 }"
      :viewport="{ once: true }"
    >
      <Card
        variant="elevated"
        :class="`${section.active ? '' : 'opacity-75'}`"
      >
        <div class="grid lg:grid-cols-3 gap-0">
          <!-- Header Section -->
          <div class="p-6 bg-zinc-900">
            <div class="flex items-center gap-4 mb-3">
              <div
                :class="`w-16 h-16 rounded-2xl flex items-center justify-center ${
                  section.active ? 'bg-blue-500' : 'bg-zinc-700'
                }`"
              >
                <component :is="section.icon" class="w-8 h-8 text-white" />
              </div>
              <div>
                <h3 class="text-3xl font-bold text-white">{{ section.name }}</h3>
                <div v-if="section.active" class="flex items-center gap-2 mt-2">
                  <div class="w-2 h-2 bg-green-400 rounded-full" />
                  <span class="text-green-400 text-sm font-medium">Активен</span>
                </div>
              </div>
            </div>

            <p class="text-lg text-gray-100 mb-4">{{ section.description }}</p>

            <div class="grid grid-cols-2 gap-6">
              <div class="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4">
                <div
                  :class="`text-3xl font-bold mb-1 ${
                    section.active ? 'text-blue-400' : 'text-gray-400'
                  }`"
                >
                  {{ section.detailedInfo.totalParts }}
                </div>
                <div class="text-sm text-gray-100">Позиций в каталоге</div>
              </div>
              <div class="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4">
                <div
                  :class="`text-3xl font-bold mb-1 ${
                    section.active ? 'text-green-400' : 'text-gray-400'
                  }`"
                >
                  {{ section.detailedInfo.manufacturers }}
                </div>
                <div class="text-sm text-gray-100">Производителей</div>
              </div>
            </div>

            <div v-if="!section.active" class="mt-4 p-4 bg-zinc-800 border border-zinc-700 rounded-xl backdrop-blur-xl">
              <div class="flex items-center gap-2 text-yellow-400 text-sm font-medium">
                <Clock class="w-4 h-4" />
                Раздел появится в ближайшее время
              </div>
            </div>
          </div>

          <!-- Categories Section -->
          <div class="p-6 bg-zinc-900 border-l border-zinc-700">
            <h4 class="font-semibold text-white mb-4 flex items-center gap-3">
              <div class="w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center">
                <Cpu class="w-4 h-4 text-white" />
              </div>
              Категории деталей
            </h4>

            <div class="space-y-3 mb-4">
              <Motion
                v-for="(category, i) in section.detailedInfo.categories"
                :key="i"
                :initial="{ opacity: 0, x: -20 }"
                :whileInView="{ opacity: 1, x: 0 }"
                :transition="{ delay: i * 0.1 }"
                :viewport="{ once: true }"
                class="flex items-center gap-3 text-sm group"
              >
                <ChevronRight class="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors" />
                <span
                  :class="`${section.active ? 'text-gray-300' : 'text-gray-500'} group-hover:text-white transition-colors`"
                >
                  {{ category }}
                </span>
              </Motion>
            </div>

            <div class="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4">
              <h5 class="font-medium text-white mb-3 flex items-center gap-2">
                <Settings class="w-4 h-4 text-blue-400" />
                Технические характеристики:
              </h5>
              <div class="space-y-2">
                <div
                  v-for="(spec, i) in section.detailedInfo.specifications"
                  :key="i"
                  class="text-sm text-gray-100 flex items-center gap-2"
                >
                  <div class="w-1 h-1 bg-blue-400 rounded-full" />
                  {{ spec }}
                </div>
              </div>
            </div>
          </div>

          <!-- Applications Section -->
          <div class="p-6 bg-zinc-900 border-l border-zinc-700">
            <h4 class="font-semibold text-white mb-4 flex items-center gap-3">
              <div class="w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center">
                <Factory class="w-4 h-4 text-white" />
              </div>
              Области применения
            </h4>

            <div class="space-y-4 mb-4">
              <Motion
                v-for="(app, i) in section.detailedInfo.applications"
                :key="i"
                :initial="{ opacity: 0, y: 20 }"
                :whileInView="{ opacity: 1, y: 0 }"
                :transition="{ delay: i * 0.1 }"
                :viewport="{ once: true }"
              >
                <div class="flex items-center gap-4 p-4 bg-zinc-800 backdrop-blur-xl rounded-xl border border-zinc-700 hover:border-zinc-600 transition-all duration-300">
                  <Building2 class="w-5 h-5 text-gray-400" />
                  <span :class="`text-sm ${section.active ? 'text-gray-300' : 'text-gray-500'}`">
                    {{ app }}
                  </span>
                </div>
              </Motion>
            </div>

            <Button
              v-if="section.active"
              variant="primary"
              class="w-full flex items-center justify-center gap-2"
            >
              Перейти к каталогу
              <ArrowRight class="w-4 h-4" />
            </Button>
          </div>
        </div>
      </Card>
    </Motion>
  </div>
</template>

<script setup lang="ts">
import { Motion } from 'motion-v'
import { 
  Settings, 
  Database, 
  Clock, 
  Cpu, 
  ChevronRight, 
  Factory, 
  Building2, 
  ArrowRight 
} from 'lucide-vue-next'
import Card from '../ui/Card.vue'
import Button from '../ui/Button.vue'

const catalogSectionsData = [
  {
    id: "seals",
    name: "Сальники и уплотнения",
    icon: Settings,
    active: true,
    description: "Комплексная база данных сальников, манжет и уплотнительных элементов",
    detailedInfo: {
      totalParts: "52,847",
      manufacturers: "247",
      categories: [
        "Радиальные сальники (ГОСТ 8752-79, DIN 3760)",
        "Манжеты гидроцилиндров (ГОСТ 14896-84)",
        "V-образные манжеты (ГОСТ 22704-77)",
        "Уплотнения поршневые и штоковые",
        "O-кольца (ГОСТ 9833-73, ISO 3601)",
        "Грязесъемники и направляющие кольца",
      ],
      specifications: [
        "Диаметры: от 6мм до 2000мм",
        "Материалы: NBR, FKM, PTFE, PU, EPDM",
        "Температурный диапазон: -60°C до +300°C",
        "Давление: до 700 бар",
      ],
      applications: [
        "Гидравлические системы",
        "Пневматические системы",
        "Автомобильная промышленность",
        "Сельскохозяйственная техника",
        "Строительная техника",
        "Промышленное оборудование",
      ],
    },
  },
  {
    id: "filters",
    name: "Фильтрующие элементы",
    icon: Database,
    active: false,
    description: "Масляные, воздушные, топливные и гидравлические фильтры",
    detailedInfo: {
      totalParts: "Скоро",
      manufacturers: "150+",
      categories: [
        "Масляные фильтры двигателей",
        "Воздушные фильтры и элементы",
        "Топливные фильтры и сепараторы",
        "Гидравлические фильтры",
        "Салонные фильтры",
        "Сепараторы масла и воздуха",
      ],
      specifications: [
        "Степень фильтрации: от 1 до 200 микрон",
        "Рабочее давление: до 350 бар",
        "Температурный режим: -40°C до +150°C",
        "Типы соединений: резьбовые, фланцевые, байонетные",
      ],
      applications: [
        "Двигатели внутреннего сгорания",
        "Гидравлические системы",
        "Компрессорное оборудование",
        "Системы вентиляции",
      ],
    },
  },
]
</script>