<template>
  <div class="flex flex-col items-center gap-2">
    <div
      ref="circleRef"
      :class="cn(
        'z-10 flex size-12 items-center justify-center rounded-full border-2 border-border bg-white p-3 shadow-[0_0_20px_-12px_rgba(0,0,0,0.8)]',
        className
      )"
    >
      <slot />
    </div>
    <div v-if="label" class="text-center">
      <div class="text-white text-xs md:text-sm font-medium px-1.5 py-0.5 md:px-2 md:py-1 bg-zinc-800/80 rounded-lg border border-zinc-700">
        {{ label }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { cn } from '@/lib/utils'

interface Props {
  className?: string
  label?: string
}

const props = withDefaults(defineProps<Props>(), {
  className: '',
  label: ''
})

const circleRef = ref<HTMLDivElement>()

defineExpose({
  $el: circleRef
})
</script>