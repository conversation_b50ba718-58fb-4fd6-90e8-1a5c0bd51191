<template>
  <div class="min-h-screen bg-black text-white overflow-hidden">
    <!-- Hero Section (2025-grade) -->
    <section class="relative min-h-[92vh] grid place-items-center">
      <AuroraBackground class="absolute inset-0 z-0" />
      <div
        class="absolute inset-0 z-10 bg-[radial-gradient(ellipse_at_center,rgba(0,0,0,0)_0%,rgba(0,0,0,0.6)_60%,rgba(0,0,0,0.9)_100%)]" />
      <FlickeringGrid class="absolute inset-0 z-20 pointer-events-none" :square-size="6" :grid-gap="8"
        :flicker-chance="0.35" color="#60a5fa" :max-opacity="0.5" />

      <div class="relative z-30 w-full">
        <div
          class="w-full max-w-5xl mx-auto px-4 md:px-6 text-center">
          <Motion :initial="{ opacity: 0, y: 30 }" :animate="{ opacity: 1, y: 0 }" :transition="{ duration: 0.9 }"
            class="w-full">
            <div class="bg-zinc-900/70 border border-zinc-800 rounded-xl p-4">

              <h1
                class="text-[40px] leading-[1.05] sm:text-6xl lg:text-7xl font-bold mb-6 max-w-4xl mx-auto text-center">
                Профессиональная база данных <span
                  class="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-300 to-cyan-300">взаимозаменяемых</span>
                запчастей
              </h1>
              <p class="text-lg sm:text-xl text-gray-200/90 leading-relaxed max-w-3xl mx-auto text-center">
                Идентификация и связывание физически идентичных деталей разных брендов. Надёжные инженерные данные,
                проверенные экспертами. API‑интеграции уровня Enterprise.
              </p>
            </div>
            <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center items-center mx-auto">
              <Button size="lg" variant="primary" class="flex items-center gap-2 px-6 py-5">
                Запросить доступ
                <ArrowRight class="w-5 h-5" />
              </Button>
              <Button size="lg" variant="outline" class="px-6 py-5">Смотреть документацию</Button>
            </div>
            <div class="mt-8 grid grid-cols-2 sm:grid-cols-3 gap-4 max-w-3xl mx-auto">
              <div v-for="(stat, index) in stats" :key="index"
                class="bg-zinc-900/70 border border-zinc-800 rounded-xl p-4">
                <AnimatedCounter :value="stat.value" class="text-3xl font-bold text-blue-400 mb-1" />
                <div class="text-gray-300 text-sm">{{ stat.label }}</div>
              </div>
            </div>
          </Motion>
        </div>
      </div>
    </section>

    <!-- Trusted by -->
    <AnimatedSection class="py-8 relative">
      <div class="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
      <div class="relative container mx-auto px-4">
        <div class="text-center text-gray-400 text-sm mb-5">Нам доверяют производители и интеграторы</div>
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-6 items-center opacity-90">
          <div v-for="(logo,i) in trustedLogos" :key="i"
            class="flex items-center justify-center py-3 rounded-lg bg-zinc-900/60 border border-zinc-800">
            <component :is="logo" class="h-6 sm:h-7 text-white/80" />
          </div>
        </div>
      </div>
    </AnimatedSection>

    <!-- Key Features -->
    <AnimatedSection class="py-20 relative">
      <div class="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
      <GridPattern class="opacity-20" />
      <div class="relative container mx-auto px-4">
        <div class="text-center mb-12">
          <Motion :initial="{ opacity: 0, scale: 0.95 }" :whileInView="{ opacity: 1, scale: 1 }"
            :transition="{ duration: 0.6 }" :viewport="{ once: true }"
            class="inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-blue-300 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Star class="w-4 h-4" />
            Ключевые преимущества
          </Motion>
          <h2 class="text-4xl lg:text-5xl font-bold mb-4">Инженерная точность. Enterprise‑уровень.</h2>
          <p class="text-lg text-gray-300 max-w-3xl mx-auto">Современная архитектура с упором на надёжность,
            масштабируемость и скорость интеграции.</p>
        </div>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Motion v-for="(f,i) in features" :key="i" :initial="{ opacity: 0, y: 20 }"
            :whileInView="{ opacity: 1, y: 0 }" :transition="{ delay: i * 0.05 }" :viewport="{ once: true }"
            class="bg-zinc-900/80 border border-zinc-800 rounded-2xl p-6">
            <div class="w-12 h-12 rounded-xl bg-zinc-800 flex items-center justify-center mb-4">
              <component :is="f.icon" class="w-6 h-6 text-white" />
            </div>
            <div class="text-white font-semibold mb-1">{{ f.title }}</div>
            <div class="text-gray-200 text-sm">{{ f.description }}</div>
          </Motion>
        </div>
      </div>
    </AnimatedSection>

    <!-- How it works -->
    <AnimatedSection class="py-20 relative">
      <div class="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
      <GridPattern class="opacity-10" />
      <div class="relative container mx-auto px-4">
        <div class="text-center mb-12">
          <Motion :initial="{ opacity: 0, scale: 0.95 }" :whileInView="{ opacity: 1, scale: 1 }"
            :transition="{ duration: 0.6 }" :viewport="{ once: true }"
            class="inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-green-300 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <CheckCircle class="w-4 h-4" />
            Как это работает
          </Motion>
          <h2 class="text-4xl lg:text-5xl font-bold mb-4">Три шага к точному аналогу</h2>
          <p class="text-lg text-gray-300 max-w-3xl mx-auto">ИИ и стандартизированные атрибуты обеспечивают высокую
            точность сопоставления.</p>
        </div>
        <div class="grid md:grid-cols-3 gap-6">
          <div v-for="(s,i) in steps" :key="i" class="bg-zinc-900/80 border border-zinc-800 rounded-2xl p-6">
            <div class="w-10 h-10 rounded-full bg-zinc-800 flex items-center justify-center mb-3 text-sm text-gray-300">
              {{ i+1 }}</div>
            <div class="text-white font-semibold mb-1">{{ s.title }}</div>
            <div class="text-gray-200 text-sm">{{ s.description }}</div>
          </div>
        </div>
      </div>
    </AnimatedSection>

    <!-- AI Search Demo (kept) -->
    <AnimatedSection class="py-20 relative">
      <div class="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
      <GridPattern class="opacity-20" />
      <div class="relative container mx-auto px-4">
        <div class="text-center mb-12">
          <Motion :initial="{ opacity: 0, scale: 0.8 }" :whileInView="{ opacity: 1, scale: 1 }"
            :transition="{ duration: 0.8 }" :viewport="{ once: true }"
            class="inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-blue-400 px-4 py-2 rounded-full text-base font-medium mb-4">
            <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
            ИИ-Ассистент поиска
          </Motion>
          <h2 class="text-4xl lg:text-5xl font-bold mb-4"><span class="text-gray-300">Умный поиск с</span> <span
              class="text-blue-400">голосовым вводом</span></h2>
          <p class="text-xl text-gray-300 max-w-3xl mx-auto">ИИ анализирует техническое описание и находит точные
            аналоги за секунды</p>
        </div>
        <AISearchDemo />
      </div>
    </AnimatedSection>

    <!-- Technical Schema (kept) -->
    <AnimatedSection class="py-20 relative">
      <div class="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
      <GridPattern class="opacity-20" />
      <div class="relative container mx-auto px-4">
        <div class="text-center mb-12">
          <Motion :initial="{ opacity: 0, scale: 0.8 }" :whileInView="{ opacity: 1, scale: 1 }"
            :transition="{ duration: 0.8 }" :viewport="{ once: true }"
            class="inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-green-400 px-4 py-2 rounded-full text-base font-medium mb-4">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            Техническая схема
          </Motion>
          <h2 class="text-4xl lg:text-5xl font-bold mb-4"><span class="text-gray-300">Поиск взаимозаменяемых</span>
            <span class="text-green-400">деталей</span></h2>
          <p class="text-xl text-gray-300 max-w-3xl mx-auto">Система анализирует технические характеристики и находит
            совместимые аналоги</p>
        </div>
        <TechnicalSchema />
      </div>
    </AnimatedSection>

    <!-- Manufacturers Network (kept) -->
    <AnimatedSection class="py-20 relative mt-4">
      <div class="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
      <GridPattern class="opacity-20" />
      <div class="relative container mx-auto px-4">
        <div class="text-center mb-8"> 
          <h2 class="text-5xl font-bold mb-3"><span class="text-gray-300">Единая экосистема</span> <span
              class="text-blue-400">производителей</span></h2>
        </div>
        <!-- Демо 1-в-1 из React: Animated beams с иконками -->
        <AnimatedBeamDemo class="mt-6" />
      </div>
    </AnimatedSection>

    <!-- Examples (kept) -->
    <AnimatedSection class="py-16 relative">
      <div class="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
      <div class="relative container mx-auto px-4">
        <div class="text-center mb-12">
          <Motion :initial="{ opacity: 0, scale: 0.8 }" :whileInView="{ opacity: 1, scale: 1 }"
            :transition="{ duration: 0.8 }" :viewport="{ once: true }"
            class="inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-green-400 px-4 py-2 rounded-full text-base font-medium mb-3">
            <CheckCircle class="w-4 h-4" />
            Реальные кейсы
          </Motion>
          <h2 class="text-5xl font-bold mb-3"><span class="text-gray-300">Примеры взаимозаменяемости</span> <span
              class="text-green-400">деталей</span></h2>
          <p class="text-2xl text-gray-100">Реальные кейсы поиска аналогов с техническими характеристиками и
            экономическим эффектом</p>
        </div>
        <ExamplesSection />
      </div>
    </AnimatedSection>

    <!-- Catalog (kept) -->
    <AnimatedSection class="py-16 relative">
      <div class="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
      <GridPattern class="opacity-10" />
      <div class="relative container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-5xl font-bold mb-3"><span class="text-gray-300">Разделы промышленного</span> <span
              class="text-blue-400">каталога</span></h2>
          <p class="text-2xl text-gray-100">Комплексная система каталогизации запчастей по техническим категориям</p>
        </div>
        <CatalogSection />
      </div>
    </AnimatedSection>

    <!-- Security & Compliance -->
    <AnimatedSection class="py-16 relative">
      <div class="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
      <div class="relative container mx-auto px-4">
        <div class="text-center mb-10">
          <Motion :initial="{ opacity: 0, scale: 0.95 }" :whileInView="{ opacity: 1, scale: 1 }"
            :transition="{ duration: 0.6 }" :viewport="{ once: true }"
            class="inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-teal-300 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Shield class="w-4 h-4" />
            Безопасность и соответствие
          </Motion>
          <h2 class="text-4xl lg:text-5xl font-bold mb-4">Корпоративные стандарты безопасности</h2>
          <p class="text-lg text-gray-300 max-w-3xl mx-auto">Шифрование, аудит действий, разграничение ролей и
            полноценные журналы событий.</p>
        </div>
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div v-for="(b,i) in securityBadges" :key="i"
            class="bg-zinc-900/80 border border-zinc-800 rounded-2xl p-6 text-center">
            <div class="w-12 h-12 mx-auto rounded-xl bg-zinc-800 flex items-center justify-center mb-3">
              <component :is="b.icon" class="w-6 h-6 text-white" />
            </div>
            <div class="text-white font-semibold mb-1">{{ b.title }}</div>
            <div class="text-gray-200 text-sm">{{ b.description }}</div>
          </div>
        </div>
      </div>
    </AnimatedSection>

    <!-- Pricing (kept) -->
    <AnimatedSection class="py-16 relative">
      <div class="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
      <div class="relative container mx-auto px-4">
        <div class="text-center mb-12">
          <Motion :initial="{ opacity: 0, scale: 0.8 }" :whileInView="{ opacity: 1, scale: 1 }"
            :transition="{ duration: 0.8 }" :viewport="{ once: true }"
            class="inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-green-400 px-4 py-2 rounded-full text-base font-medium mb-3">
            <Star class="w-4 h-4" />
            Тарифные планы
          </Motion>
          <h2 class="text-5xl font-bold mb-3"><span class="text-gray-300">Тарифные планы для</span> <span
              class="text-green-400">профессионалов</span></h2>
          <p class="text-2xl text-gray-100">Выберите подходящий уровень доступа к промышленной базе данных</p>
        </div>
        <PricingSection />
      </div>
    </AnimatedSection>

    <!-- FAQ -->
    <AnimatedSection class="py-16 relative">
      <div class="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
      <div class="relative container mx-auto px-4 max-w-5xl">
        <div class="text-center mb-10">
          <Motion :initial="{ opacity: 0, scale: 0.95 }" :whileInView="{ opacity: 1, scale: 1 }"
            :transition="{ duration: 0.6 }" :viewport="{ once: true }"
            class="inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-blue-300 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <HelpCircle class="w-4 h-4" />
            Частые вопросы
          </Motion>
          <h2 class="text-4xl lg:text-5xl font-bold">Ответы для быстрого старта</h2>
        </div>
        <div class="space-y-4">
          <div v-for="(q,i) in faqs" :key="i" class="bg-zinc-900/80 border border-zinc-800 rounded-xl">
            <details class="group rounded-xl">
              <summary class="cursor-pointer list-none px-6 py-4 flex items-center justify-between">
                <span class="text-white font-medium">{{ q.q }}</span>
                <svg class="w-5 h-5 text-gray-400 transition-transform group-open:rotate-180" viewBox="0 0 24 24"
                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </summary>
              <div class="px-6 pb-5 text-gray-200 text-sm leading-relaxed">{{ q.a }}</div>
            </details>
          </div>
        </div>
      </div>
    </AnimatedSection>

    <!-- Final CTA -->
    <AnimatedSection class="py-16 relative">
      <div class="absolute inset-0 bg-gradient-to-br from-zinc-900 via-black to-zinc-950" />
      <div class="relative container mx-auto px-4 text-center">
        <Motion :initial="{ opacity: 0, y: 30 }" :whileInView="{ opacity: 1, y: 0 }" :transition="{ duration: 0.8 }"
          :viewport="{ once: true }">
          <h2 class="text-5xl lg:text-6xl font-bold mb-4"><span class="text-gray-300">Готовы оптимизировать</span> <span
              class="text-blue-400">закупки запчастей?</span></h2>
          <p class="text-2xl text-gray-100 mb-6 max-w-4xl mx-auto leading-relaxed">Присоединяйтесь к предприятиям,
            экономящим миллионы благодаря нашей системе поиска взаимозаменяемых деталей</p>
          <div class="flex flex-col sm:flex-row gap-6 justify-center mb-10">
            <Button size="lg" variant="primary" class="flex items-center gap-2 px-10 py-5">Запросить демонстрацию
              <ArrowRight class="w-5 h-5" />
            </Button>
            <Button size="lg" variant="outline" class="px-10 py-5">Техническая консультация</Button>
          </div>
          <div class="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
            <div v-for="(feature, index) in ctaFeatures" :key="index"
              class="bg-zinc-900/80 backdrop-blur-xl border border-zinc-700/50 rounded-2xl p-6 text-center">
              <div class="w-16 h-16 bg-zinc-800 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <component :is="feature.icon" class="w-8 h-8 text-white" />
              </div>
              <div class="font-semibold text-white text-lg mb-2">{{ feature.title }}</div>
              <div class="text-sm text-gray-100">{{ feature.description }}</div>
            </div>
          </div>
        </Motion>
      </div>
    </AnimatedSection>

    <!-- Footer -->
    <footer class="border-t border-zinc-800/80 bg-black/60">
      <div
        class="container mx-auto px-4 py-8 text-sm text-gray-400 flex flex-col sm:flex-row items-center justify-between gap-3">
        <div>© {{ new Date().getFullYear() }} PartTec. Все права защищены.</div>
        <div class="flex items-center gap-4">
          <a href="/privacy" class="hover:text-gray-200">Политика конфиденциальности</a>
          <a href="/terms" class="hover:text-gray-200">Условия использования</a>
          <a href="/security" class="hover:text-gray-200">Безопасность</a>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { Motion } from 'motion-v'
import {
  Database,
  Sparkles,
  ArrowRight,
  Globe,
  CheckCircle,
  Star,
  Shield,
  Zap,
  Users,
  Lock,
  Server,
  FileSearch,
  Cpu,
  Activity,
  HelpCircle
} from 'lucide-vue-next'

import AnimatedSection from './AnimatedSection.vue'
import AISearchDemo from './AISearchDemo.vue'
import TechnicalSchema from './TechnicalSchema.vue'
import ManufacturersNetwork from './ManufacturersNetwork.vue'
import AnimatedBeamDemo from './AnimatedBeamDemo.vue'
import ExamplesSection from './ExamplesSection.vue'
import CatalogSection from './CatalogSection.vue'
import PricingSection from './PricingSection.vue'
import AuroraBackground from './AuroraBackground.vue'
import GridPattern from './effects/GridPattern.vue'
import FlickeringGrid from './effects/FlickeringGrid.vue'
import Button from '../ui/Button.vue'
import AnimatedCounter from '../ui/AnimatedCounter.vue'

// Logos (reuse brand icons for the trust row)
import CaterpillarIcon from './icons/CaterpillarIcon.vue'
import JohnDeereIcon from './icons/JohnDeereIcon.vue'
import KomatsuIcon from './icons/KomatsuIcon.vue'
import VolvoIcon from './icons/VolvoIcon.vue'
import LiebherrIcon from './icons/LiebherrIcon.vue'
import CatalogIcon from './icons/CatalogIcon.vue'

// Effects (не используются в этой версии секций)

const stats = [
  { value: '500K+', label: 'Запчастей в базе' },
  { value: '2,847', label: 'Производителей' },
  { value: 'до 50%', label: 'Экономия на закупках' },
]

const trustedLogos = [CaterpillarIcon, JohnDeereIcon, KomatsuIcon, VolvoIcon, LiebherrIcon, CatalogIcon]

const features = [
  { icon: FileSearch, title: 'Идентичности и атрибуты', description: 'Точная модель данных: физическая идентичность, степени совместимости, стандартизированные единицы.' },
  { icon: Cpu, title: 'ИИ‑сопоставление', description: 'Автоматический анализ описаний, объединение синонимов и типизация характеристик.' },
  { icon: Server, title: 'N‑уровневая архитектура', description: 'Bun + Hono + tRPC + PostgreSQL + Prisma + ZenStack. Горизонтальное масштабирование.' },
  { icon: Shield, title: 'Ролевая модель и аудит', description: 'RBAC, детальные журналы действий, контроль версий и трассировка изменений.' },
  { icon: Lock, title: 'Шифрование и изоляция', description: 'TLS, безопасное хранение токенов, сегментация данных и защита периметра.' },
  { icon: Activity, title: 'Надёжность и SLA', description: 'Мониторинг, алёртинг, SLO/SLA, отказоустойчивые конфигурации.' },
]

const steps = [
  { title: 'Описание', description: 'Задайте запрос голосом или текстом. Укажите ключевые параметры и применение.' },
  { title: 'Сопоставление', description: 'ИИ сопоставляет характеристики и находит физически идентичные или частично совместимые детали.' },
  { title: 'Верификация', description: 'Экспертная проверка и прозрачные метки точности: EXACT, PARTIAL, WITH_NOTES, MODIFICATION.' },
]

const securityBadges = [
  { icon: Shield, title: 'RBAC + Audit', description: 'Разграничение доступа и детальные журналы событий' },
  { icon: Lock, title: 'Шифрование', description: 'TLS в транзите, защищённое хранение секретов' },
  { icon: Server, title: 'Резервирование', description: 'Бэкапы, точка восстановления, план DR' },
  { icon: Database, title: 'Целостность данных', description: 'Типобезопасность end‑to‑end и схемы валидации' },
]

const ctaFeatures = [
  {
    icon: Shield,
    title: "Гарантия качества",
    description: "Все связи проверены экспертами",
  },
  {
    icon: Zap,
    title: "Быстрая интеграция",
    description: "API готов к работе за 24 часа",
  },
  {
    icon: Users,
    title: "Экспертная поддержка",
    description: "Команда инженеров 24/7",
  },
]

const faqs = [
  { q: 'Можно ли интегрировать в нашу ERP/CRM?', a: 'Да. Доступны tRPC/REST endpoints, вебхуки, ключи доступа и троттлинг. Помогаем с архитектурой интеграции.' },
  { q: 'Как обеспечивается точность сопоставления?', a: 'Комбинация формализованных атрибутов, единиц измерения и ИИ‑моделей. Каждый матч помечен меткой точности.' },
  { q: 'Есть ли тестовый доступ?', a: 'Предоставляем sandbox‑окружение с ограниченными датасетами и rate‑limit. Запросите у команды продаж.' },
  { q: 'Как устроена безопасность и контроль доступа?', a: 'RBAC по ролям (GUEST/USER/SHOP/ADMIN), аудит действий, шифрование и сегментация данных.' },
]
</script>
