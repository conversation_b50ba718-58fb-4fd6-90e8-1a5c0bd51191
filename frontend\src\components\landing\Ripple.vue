<template>
  <div class="absolute inset-0 overflow-hidden rounded-2xl">
    <Motion
      v-for="(ripple, index) in ripples" 
      :key="`${ripple.id}-${index}`"
      :initial="{ scale: 0, opacity: 0.8 }"
      :animate="{ scale: 1, opacity: 0 }"
      :transition="{ 
        duration: ripple.duration, 
        delay: ripple.delay,
        ease: 'easeOut'
      }"
      class="absolute rounded-full bg-blue-500/20"
      :style="{
        left: `${ripple.x}%`,
        top: `${ripple.y}%`,
        width: `${ripple.size}px`,
        height: `${ripple.size}px`,
        transform: 'translate(-50%, -50%)'
      }"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Motion } from 'motion-v'

interface RippleItem {
  id: number
  x: number
  y: number
  size: number
  delay: number
  duration: number
}

const ripples = ref<RippleItem[]>([])
let rippleId = 0

const generateRipple = (): RippleItem => ({
  id: rippleId++,
  x: Math.random() * 100,
  y: Math.random() * 100,
  size: Math.random() * 100 + 50,
  delay: Math.random() * 2,
  duration: Math.random() * 3 + 2
})

let interval: NodeJS.Timeout | null = null

onMounted(() => {
  // Generate initial ripples
  for (let i = 0; i < 5; i++) {
    ripples.value.push(generateRipple())
  }

  // Add new ripples periodically
  interval = setInterval(() => {
    if (ripples.value.length < 8) {
      ripples.value.push(generateRipple())
    } else {
      // Replace oldest ripple
      ripples.value.shift()
      ripples.value.push(generateRipple())
    }
  }, 2000)
})

onUnmounted(() => {
  if (interval) {
    clearInterval(interval)
  }
})
</script>