<template>
  <div class="relative">
    <div class="absolute inset-0 bg-zinc-900/5 rounded-2xl" />

    <div class="relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-800 rounded-2xl p-6 shadow-2xl">
      <div class="grid lg:grid-cols-2 gap-8">
        <!-- Search Interface -->
        <div class="space-y-3">
          <div class="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg">
            <div class="p-6 border-b border-zinc-700">
              <div class="flex items-center gap-3 mb-3">
                <div class="w-10 h-10 bg-zinc-700 rounded-lg flex items-center justify-center">
                  <Search class="w-5 h-5 text-white" />
                </div>
                <h3 class="text-2xl font-semibold text-white">Поиск взаимозаменяемых деталей</h3>
              </div>

              <div class="relative group">
                <div class="absolute -inset-0.5 bg-blue-500 rounded-lg blur opacity-30 group-hover:opacity-60 transition duration-300" />
                <div class="relative">
                  <Search class="absolute left-4 top-4 w-5 h-5 text-gray-300" />
                  <input
                    type="text"
                    placeholder="Введите артикул или OEM номер..."
                    v-model="searchQuery"
                    class="w-full pl-12 pr-4 py-4 bg-zinc-900/50 backdrop-blur-xl border border-zinc-700 rounded-lg text-white placeholder-gray-300 focus:border-blue-500/50 focus:outline-none focus:ring-2 focus:ring-blue-500/20 transition-all duration-300"
                  />
                </div>
              </div>
            </div>

            <div class="p-6 space-y-3">
              <Motion
                v-for="part in parts"
                :key="part.id"
                :class="`p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                  activeConnection === part.id
                    ? 'bg-blue-500/20 border border-blue-500/50'
                    : 'bg-zinc-800 backdrop-blur-xl border border-zinc-700 hover:border-zinc-600'
                }`"
                @click="setActiveConnection(part.id)"
                :whileHover="{ scale: 1.02 }"
                :whileTap="{ scale: 0.98 }"
              >
                <div class="flex items-center justify-between mb-3">
                  <span class="font-mono text-base text-white font-medium">{{ part.original }}</span>
                  <div class="bg-zinc-700 text-white text-sm border-0 px-2 py-1 rounded">{{ part.category }}</div>
                </div>
                <div class="text-sm text-gray-100 mb-3">{{ part.specs }}</div>
                <div class="flex items-center gap-4 text-sm">
                  <span class="text-green-400 flex items-center gap-1">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                    Совместимость: {{ part.compatibility }}%
                  </span>
                  <span class="text-blue-400 flex items-center gap-1">
                    <TrendingUp class="w-3 h-3" />
                    Экономия: до {{ part.savings }}%
                  </span>
                </div>
              </Motion>
            </div>
          </div>
        </div>

        <!-- Results Panel -->
        <div class="space-y-3">
          <div class="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg">
            <div class="p-6 border-b border-zinc-700">
              <div class="flex items-center gap-3 mb-3">
                <div class="w-10 h-10 bg-zinc-700 rounded-lg flex items-center justify-center">
                  <Database class="w-5 h-5 text-white" />
                </div>
                <h3 class="text-2xl font-semibold text-white">Результаты анализа</h3>
              </div>
            </div>

            <div class="p-6">
              <div v-if="activeConnection" class="space-y-4">
                <Motion
                  :initial="{ opacity: 0, y: 20 }"
                  :animate="{ opacity: 1, y: 0 }"
                  class="space-y-4"
                >
                  <div class="bg-zinc-800 backdrop-blur-xl rounded-xl p-4 border border-zinc-700">
                    <div class="text-base font-medium text-white mb-2 flex items-center gap-2">
                      <Target class="w-4 h-4 text-blue-400" />
                      Оригинальная деталь:
                    </div>
                    <div class="font-mono text-blue-400 text-2xl">{{ selectedPart?.original }}</div>
                    <div class="text-sm text-gray-100 mt-2">{{ selectedPart?.specs }}</div>
                  </div>

                  <div class="space-y-3">
                    <div class="text-base font-medium text-white flex items-center gap-2">
                      <CheckCircle class="w-4 h-4 text-green-400" />
                      Найденные аналоги:
                    </div>
                    <Motion
                      v-for="(alt, index) in selectedPart?.alternatives"
                      :key="index"
                      :initial="{ opacity: 0, x: -20 }"
                      :animate="{ opacity: 1, x: 0 }"
                      :transition="{ delay: index * 0.1 }"
                      class="flex items-center gap-3 p-3 bg-zinc-800 rounded-xl border border-zinc-700 backdrop-blur-xl"
                    >
                      <CheckCircle2 class="w-4 h-4 text-green-400 flex-shrink-0" />
                      <div class="flex-1">
                        <div class="font-mono text-base text-white">{{ alt }}</div>
                        <div class="text-sm text-gray-100">Физическая совместимость подтверждена</div>
                      </div>
                      <div class="text-sm text-green-400 font-medium">-{{ selectedPart?.savings }}%</div>
                    </Motion>
                  </div>

                  <div class="bg-zinc-800 border border-zinc-700 rounded-xl p-4 backdrop-blur-xl">
                    <div class="flex items-center gap-2 text-blue-400 text-base font-medium mb-2">
                      <Info class="w-4 h-4" />
                      Техническое заключение
                    </div>
                    <div class="text-sm text-gray-100">
                      Все найденные аналоги имеют идентичные технические характеристики и могут использоваться
                      как прямая замена без модификаций.
                    </div>
                  </div>
                </Motion>
              </div>

              <div v-else class="py-12 text-center">
                <Motion class="w-16 h-16 bg-zinc-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Database class="w-8 h-8 text-white" />
                </Motion>
                <div class="text-gray-300">Выберите деталь для анализа совместимости</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Motion } from 'motion-v'
import { 
  Search, 
  Database, 
  Target, 
  CheckCircle, 
  CheckCircle2, 
  Info, 
  TrendingUp 
} from 'lucide-vue-next'

const activeConnection = ref<number | null>(null)
const searchQuery = ref('')

const parts = [
  {
    id: 1,
    original: "Corteco 12345-ABC",
    specs: "25×47×7mm, NBR, -40°C/+120°C",
    alternatives: ["SKF 789-XYZ", "Febi 456-DEF", "NOK 321-GHI"],
    category: "Сальники",
    compatibility: 98,
    savings: 35,
  },
  {
    id: 2,
    original: "John Deere RE12345",
    specs: "Передаточное число 1:4.5, крутящий момент 850 Нм",
    alternatives: ["Komatsu 708-1W-00151", "Caterpillar 123-4567"],
    category: "Редукторы",
    compatibility: 95,
    savings: 45,
  },
]

const selectedPart = computed(() => {
  return parts.find(p => p.id === activeConnection.value)
})

const setActiveConnection = (id: number) => {
  activeConnection.value = activeConnection.value === id ? null : id
}
</script>