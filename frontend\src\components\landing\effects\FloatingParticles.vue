<template>
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      v-for="(particle, index) in particles"
      :key="index"
      class="absolute w-1 h-1 bg-blue-400/30 rounded-full animate-pulse"
      :style="{
        left: `${particle.x}%`,
        top: `${particle.y}%`,
        animationDelay: `${particle.delay}s`,
        animationDuration: `${particle.duration}s`,
        transform: `scale(${particle.scale})`
      }"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Particle {
  x: number
  y: number
  scale: number
  delay: number
  duration: number
}

const particles = ref<Particle[]>([])

const generateParticle = (): Particle => ({
  x: Math.random() * 100,
  y: Math.random() * 100,
  scale: Math.random() * 2 + 0.5,
  delay: Math.random() * 5,
  duration: Math.random() * 3 + 2
})

onMounted(() => {
  // Generate particles
  for (let i = 0; i < 50; i++) {
    particles.value.push(generateParticle())
  }
})
</script>