<template>
  <div class="relative">
    <div 
      class="absolute rounded-full animate-pulse"
      :class="[
        size === 'sm' ? 'w-32 h-32' : size === 'md' ? 'w-48 h-48' : 'w-64 h-64',
        color === 'blue' ? 'bg-blue-500/20' : color === 'purple' ? 'bg-purple-500/20' : 'bg-pink-500/20'
      ]"
      :style="{
        filter: 'blur(40px)',
        animationDuration: `${duration}s`
      }"
    />
    <div 
      class="absolute rounded-full animate-ping"
      :class="[
        size === 'sm' ? 'w-16 h-16' : size === 'md' ? 'w-24 h-24' : 'w-32 h-32',
        color === 'blue' ? 'bg-blue-400/40' : color === 'purple' ? 'bg-purple-400/40' : 'bg-pink-400/40'
      ]"
      :style="{
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        animationDuration: `${duration * 1.5}s`
      }"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  size?: 'sm' | 'md' | 'lg'
  color?: 'blue' | 'purple' | 'pink'
  duration?: number
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  color: 'blue',
  duration: 3
})
</script>