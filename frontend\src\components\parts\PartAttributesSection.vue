<template>
  <div class="part-attributes-section">
    <div class="section-header">
      <h3 class="section-title">Атрибуты</h3>
      <div class="section-actions">
        <button
          v-if="showGroups && hasGroups"
          @click="toggleGroupView"
          class="toggle-view-button"
        >
          <Icon :name="groupView ? 'list' : 'grid'" size="16" />
          {{ groupView ? 'Список' : 'Группы' }}
        </button>
        
        <button
          @click="toggleExpanded"
          class="expand-button"
        >
          <Icon :name="expanded ? 'chevron-up' : 'chevron-down'" size="16" />
          {{ expanded ? 'Свернуть' : 'Развернуть' }}
        </button>
      </div>
    </div>

    <div v-show="expanded" class="attributes-content">
      <!-- Группированное отображение -->
      <div v-if="groupView && hasGroups" class="grouped-attributes">
        <div
          v-for="group in attributeGroups"
          :key="group.id || 'ungrouped'"
          class="attribute-group"
        >
          <h4 class="group-title">
            <Icon
              v-if="group.icon"
              :name="group.icon"
              size="16"
              class="group-icon"
            />
            {{ group.name || 'Без группы' }}
            <span class="group-count">({{ group.attributes.length }})</span>
          </h4>
          
          <div class="group-attributes">
            <div
              v-for="attribute in group.attributes"
              :key="attribute.id"
              class="attribute-item"
            >
              <AttributeDisplay
                :attribute="attribute"
                :show-description="showDescriptions"
                :compact="compactMode"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Обычное отображение -->
      <div v-else class="flat-attributes">
        <div
          v-for="attribute in sortedAttributes"
          :key="attribute.id"
          class="attribute-item"
        >
          <AttributeDisplay
            :attribute="attribute"
            :show-description="showDescriptions"
            :compact="compactMode"
          />
        </div>
      </div>

      <!-- Пустое состояние -->
      <div v-if="attributes.length === 0" class="empty-attributes">
        <Icon name="info" size="24" class="empty-icon" />
        <p class="empty-text">Атрибуты не указаны</p>
      </div>
    </div>

    <!-- Статистика -->
    <div v-if="showStats && attributes.length > 0" class="attributes-stats">
      <div class="stats-item">
        <span class="stats-label">Всего атрибутов:</span>
        <span class="stats-value">{{ attributes.length }}</span>
      </div>
      <div v-if="hasGroups" class="stats-item">
        <span class="stats-label">Групп:</span>
        <span class="stats-value">{{ attributeGroups.length }}</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">Заполнено:</span>
        <span class="stats-value">{{ filledAttributesCount }} / {{ attributes.length }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import AttributeDisplay from '@/components/attributes/AttributeDisplay.vue';
import Icon from '@/components/ui/Icon.vue';

export interface PartAttribute {
  id: number;
  value: string | number | boolean | null;
  template: {
    id: number;
    name: string;
    title: string;
    description?: string;
    dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
    unit?: {
      id: number;
      name: string;
      symbol: string;
    };
    group?: {
      id: number;
      name: string;
      description?: string;
      icon?: string;
      sortOrder: number;
    };
    isRequired: boolean;
    sortOrder: number;
  };
}

export interface AttributeGroup {
  id: number | null;
  name: string;
  description?: string;
  icon?: string;
  sortOrder: number;
  attributes: PartAttribute[];
}

export interface PartAttributesSectionProps {
  attributes: PartAttribute[];
  showGroups?: boolean;
  showDescriptions?: boolean;
  showStats?: boolean;
  compactMode?: boolean;
  initialExpanded?: boolean;
}

const props = withDefaults(defineProps<PartAttributesSectionProps>(), {
  showGroups: true,
  showDescriptions: false,
  showStats: true,
  compactMode: false,
  initialExpanded: true
});

// Реактивные данные
const expanded = ref(props.initialExpanded);
const groupView = ref(true);

// Вычисляемые свойства
const hasGroups = computed(() => {
  return props.attributes.some(attr => attr.template.group);
});

const attributeGroups = computed((): AttributeGroup[] => {
  const groups = new Map<number | null, AttributeGroup>();
  
  // Инициализируем группы
  props.attributes.forEach(attr => {
    const group = attr.template.group;
    const groupId = group?.id || null;
    
    if (!groups.has(groupId)) {
      groups.set(groupId, {
        id: groupId,
        name: group?.name || 'Без группы',
        description: group?.description,
        icon: group?.icon,
        sortOrder: group?.sortOrder || 999,
        attributes: []
      });
    }
    
    groups.get(groupId)!.attributes.push(attr);
  });
  
  // Сортируем группы и атрибуты внутри групп
  const sortedGroups = Array.from(groups.values()).sort((a, b) => {
    if (a.id === null) return 1; // "Без группы" в конец
    if (b.id === null) return -1;
    return a.sortOrder - b.sortOrder;
  });
  
  // Сортируем атрибуты внутри каждой группы
  sortedGroups.forEach(group => {
    group.attributes.sort((a, b) => {
      if (a.template.isRequired && !b.template.isRequired) return -1;
      if (!a.template.isRequired && b.template.isRequired) return 1;
      return a.template.sortOrder - b.template.sortOrder;
    });
  });
  
  return sortedGroups;
});

const sortedAttributes = computed(() => {
  return [...props.attributes].sort((a, b) => {
    // Сначала обязательные
    if (a.template.isRequired && !b.template.isRequired) return -1;
    if (!a.template.isRequired && b.template.isRequired) return 1;
    
    // Потом по группам
    const aGroupOrder = a.template.group?.sortOrder || 999;
    const bGroupOrder = b.template.group?.sortOrder || 999;
    if (aGroupOrder !== bGroupOrder) {
      return aGroupOrder - bGroupOrder;
    }
    
    // Потом по порядку внутри группы
    return a.template.sortOrder - b.template.sortOrder;
  });
});

const filledAttributesCount = computed(() => {
  return props.attributes.filter(attr => {
    const value = attr.value;
    return value !== null && value !== undefined && value !== '';
  }).length;
});

// Методы
const toggleExpanded = () => {
  expanded.value = !expanded.value;
};

const toggleGroupView = () => {
  groupView.value = !groupView.value;
};
</script>

<style scoped>
.part-attributes-section { display: grid; gap: 1rem; }
.section-header { display: flex; justify-content: space-between; align-items: center; }
.section-title { font-size: 1.125rem; font-weight: 600; }
.section-actions { display: inline-flex; gap: .5rem; }
.toggle-view-button, .expand-button { display: inline-flex; align-items: center; gap: .25rem; padding: .25rem .75rem; font-size: .875rem; border: 1px solid #d1d5db; border-radius: .375rem; }
.attributes-content { display: grid; gap: 1rem; }

.grouped-attributes { display: grid; gap: 1.5rem; }
.attribute-group { display: grid; gap: .75rem; }
.group-title { display: inline-flex; align-items: center; gap: .5rem; font-size: 1rem; font-weight: 600; padding-bottom: .5rem; border-bottom: 1px solid var(--surface-300); }
.group-icon { color: #9ca3af; }
.group-count { font-size: .875rem; color: #6b7280; font-weight: 400; }
.group-attributes { display: grid; grid-template-columns: 1fr; gap: .75rem; }
@media (min-width: 768px) { .group-attributes { grid-template-columns: 1fr 1fr; } }

.flat-attributes { display: grid; grid-template-columns: 1fr; gap: .75rem; }
@media (min-width: 768px) { .flat-attributes { grid-template-columns: 1fr 1fr; } }
@media (min-width: 1024px) { .flat-attributes { grid-template-columns: 1fr 1fr 1fr; } }
.attribute-item { background: #f9fafb; border: 1px solid var(--surface-300); border-radius: .5rem; padding: .75rem; }
.empty-attributes { display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 2rem 0; color: #6b7280; }
.empty-icon { margin-bottom: .5rem; }
.empty-text { font-size: .875rem; }
.attributes-stats { display: flex; flex-wrap: wrap; gap: 1rem; padding-top: 1rem; border-top: 1px solid var(--surface-300); font-size: .875rem; }
stats-item { display: inline-flex; align-items: center; gap: .25rem; }
.stats-label { color: #6b7280; }
.stats-value { font-weight: 600; }

/* Анимации */
.attributes-content {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>