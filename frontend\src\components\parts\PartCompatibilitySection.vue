<template>
  <div class="part-compatibility-section">
    <div class="section-header">
      <h3 class="section-title">Совместимые каталожные позиции</h3>
      <div class="section-actions">
        <div class="view-controls">
          <button
            @click="setViewMode('grid')"
            :class="['view-button', { active: viewMode === 'grid' }]"
          >
            <Icon name="grid" size="16" />
          </button>
          <button
            @click="setViewMode('list')"
            :class="['view-button', { active: viewMode === 'list' }]"
          >
            <Icon name="list" size="16" />
          </button>
        </div>
        
        <div class="filter-controls">
          <select
            v-model="accuracyFilter"
            class="accuracy-filter"
          >
            <option value="">Все уровни точности</option>
            <option value="EXACT_MATCH">Точное соответствие</option>
            <option value="MATCH_WITH_NOTES">С примечаниями</option>
            <option value="REQUIRES_MODIFICATION">Требует доработки</option>
            <option value="PARTIAL_MATCH">Частичное соответствие</option>
          </select>
          
          <select
            v-model="brandFilter"
            class="brand-filter"
          >
            <option value="">Все бренды</option>
            <option
              v-for="brand in availableBrands"
              :key="brand.id"
              :value="brand.id"
            >
              {{ brand.name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Загрузка -->
    <div v-if="loading" class="loading-state">
      <Spinner size="md" />
      <span>Загрузка совместимых позиций...</span>
    </div>

    <!-- Ошибка -->
    <div v-else-if="error" class="error-state">
      <Icon name="alert-circle" size="24" class="error-icon" />
      <p class="error-text">Ошибка загрузки данных</p>
      <button @click="loadCompatibility" class="retry-button">
        Повторить
      </button>
    </div>

    <!-- Контент -->
    <div v-else-if="filteredItems.length > 0" class="compatibility-content">
      <!-- Статистика -->
      <div class="compatibility-stats">
        <div class="stats-item">
          <span class="stats-label">Найдено позиций:</span>
          <span class="stats-value">{{ filteredItems.length }}</span>
        </div>
        <div class="accuracy-legend">
          <div
            v-for="level in accuracyLevels"
            :key="level.value"
            class="legend-item"
          >
            <div :class="['legend-indicator', level.class]"></div>
            <span class="legend-label">{{ level.label }}</span>
            <span class="legend-count">({{ getAccuracyCount(level.value) }})</span>
          </div>
        </div>
      </div>

      <!-- Сетка -->
      <div v-if="viewMode === 'grid'" class="compatibility-grid">
        <div
          v-for="item in paginatedItems"
          :key="item.id"
          class="compatibility-card"
        >
          <div class="card-header">
            <div class="item-info">
              <h4 class="item-sku">{{ item.sku }}</h4>
              <p class="item-brand">{{ item.brand?.name }}</p>
            </div>
            <div :class="['accuracy-badge', getAccuracyClass(item.accuracy)]">
              {{ getAccuracyLabel(item.accuracy) }}
            </div>
          </div>
          
          <div class="card-content">
            <p v-if="item.description" class="item-description">
              {{ item.description }}
            </p>
            
            <div v-if="item.notes" class="item-notes">
              <Icon name="info" size="14" />
              <span>{{ item.notes }}</span>
            </div>
            
            <div v-if="item.attributes?.length" class="item-attributes">
              <div
                v-for="attr in item.attributes.slice(0, 3)"
                :key="attr.id"
                class="attribute-chip"
              >
                <span class="attr-name">{{ attr.template.title }}:</span>
                <span class="attr-value">{{ formatAttributeValue(attr) }}</span>
              </div>
              <div v-if="item.attributes.length > 3" class="more-attributes">
                +{{ item.attributes.length - 3 }} еще
              </div>
            </div>
          </div>
          
          <div class="card-actions">
            <button
              @click="viewCatalogItem(item)"
              class="view-button"
            >
              <Icon name="eye" size="16" />
              Подробнее
            </button>
            <button
              v-if="canEdit"
              @click="editCompatibility(item)"
              class="edit-button"
            >
              <Icon name="edit" size="16" />
              Изменить
            </button>
          </div>
        </div>
      </div>

      <!-- Список -->
      <div v-else class="compatibility-list">
        <div class="list-header">
          <div class="header-cell">SKU / Бренд</div>
          <div class="header-cell">Описание</div>
          <div class="header-cell">Точность</div>
          <div class="header-cell">Действия</div>
        </div>
        
        <div
          v-for="item in paginatedItems"
          :key="item.id"
          class="list-row"
        >
          <div class="row-cell">
            <div class="item-primary">
              <span class="item-sku">{{ item.sku }}</span>
              <span class="item-brand">{{ item.brand?.name }}</span>
            </div>
          </div>
          
          <div class="row-cell">
            <p class="item-description">{{ item.description || 'Без описания' }}</p>
            <div v-if="item.notes" class="item-notes">
              <Icon name="info" size="12" />
              <span>{{ item.notes }}</span>
            </div>
          </div>
          
          <div class="row-cell">
            <div :class="['accuracy-badge', getAccuracyClass(item.accuracy)]">
              {{ getAccuracyLabel(item.accuracy) }}
            </div>
          </div>
          
          <div class="row-cell">
            <div class="row-actions">
              <button
                @click="viewCatalogItem(item)"
                class="action-button"
              >
                <Icon name="eye" size="14" />
              </button>
              <button
                v-if="canEdit"
                @click="editCompatibility(item)"
                class="action-button"
              >
                <Icon name="edit" size="14" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Пагинация -->
      <div v-if="totalPages > 1" class="pagination">
        <button
          @click="previousPage"
          :disabled="currentPage === 1"
          class="pagination-button"
        >
          <Icon name="chevron-left" size="16" />
          Назад
        </button>
        
        <div class="pagination-info">
          Страница {{ currentPage }} из {{ totalPages }}
        </div>
        
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="pagination-button"
        >
          Вперед
          <Icon name="chevron-right" size="16" />
        </button>
      </div>
    </div>

    <!-- Пустое состояние -->
    <div v-else class="empty-state">
      <Icon name="package" size="48" class="empty-icon" />
      <h4 class="empty-title">Совместимые позиции не найдены</h4>
      <p class="empty-description">
        Для этой запчасти пока не указаны совместимые каталожные позиции
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import { useAuth } from '@/composables/useAuth';
import Icon from '@/components/ui/Icon.vue';
import Spinner from '@/components/ui/Spinner.vue';

export interface CompatibilityItem {
  id: number;
  sku: string;
  description?: string;
  brand?: {
    id: number;
    name: string;
  };
  accuracy: 'EXACT_MATCH' | 'MATCH_WITH_NOTES' | 'REQUIRES_MODIFICATION' | 'PARTIAL_MATCH';
  notes?: string;
  attributes?: Array<{
    id: number;
    value: any;
    template: {
      id: number;
      title: string;
      dataType: string;
      unit?: {
        symbol: string;
      };
    };
  }>;
}

export interface PartCompatibilitySectionProps {
  partId: number;
  initialViewMode?: 'grid' | 'list';
  itemsPerPage?: number;
}

const props = withDefaults(defineProps<PartCompatibilitySectionProps>(), {
  initialViewMode: 'grid',
  itemsPerPage: 12
});

const trpc = useTrpc();
const { user } = useAuth();

// Реактивные данные
const loading = ref(false);
const error = ref<Error | null>(null);
const items = ref<CompatibilityItem[]>([]);
const viewMode = ref(props.initialViewMode);
const accuracyFilter = ref('');
const brandFilter = ref('');
const currentPage = ref(1);

// Константы
const accuracyLevels = [
  { value: 'EXACT_MATCH', label: 'Точное соответствие', class: 'exact' },
  { value: 'MATCH_WITH_NOTES', label: 'С примечаниями', class: 'notes' },
  { value: 'REQUIRES_MODIFICATION', label: 'Требует доработки', class: 'mod' },
  { value: 'PARTIAL_MATCH', label: 'Частичное соответствие', class: 'partial' }
];

// Вычисляемые свойства
const canEdit = computed(() => {
  return user.value?.role === 'ADMIN' || user.value?.role === 'SHOP';
});

const availableBrands = computed(() => {
  const brands = new Map();
  items.value.forEach(item => {
    if (item.brand) {
      brands.set(item.brand.id, item.brand);
    }
  });
  return Array.from(brands.values()).sort((a, b) => a.name.localeCompare(b.name));
});

const filteredItems = computed(() => {
  let filtered = items.value;
  
  if (accuracyFilter.value) {
    filtered = filtered.filter(item => item.accuracy === accuracyFilter.value);
  }
  
  if (brandFilter.value) {
    filtered = filtered.filter(item => item.brand?.id === Number(brandFilter.value));
  }
  
  return filtered;
});

const totalPages = computed(() => {
  return Math.ceil(filteredItems.value.length / props.itemsPerPage);
});

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * props.itemsPerPage;
  const end = start + props.itemsPerPage;
  return filteredItems.value.slice(start, end);
});

// Методы
const loadCompatibility = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const result = await trpc.client.crud.partApplicability.findMany.query({
      where: { partId: props.partId },
      include: {
        catalogItem: {
          include: {
            brand: true,
            attributes: {
              include: { template: { include: { unit: true } } }
            }
          }
        }
      }
    });

    if (result && Array.isArray(result)) {
      items.value = result.map((app: any) => ({
        id: app.catalogItem?.id,
        sku: app.catalogItem?.sku,
        description: app.catalogItem?.description,
        brand: app.catalogItem?.brand ? { id: app.catalogItem.brand.id, name: app.catalogItem.brand.name } : undefined,
        accuracy: app.accuracy,
        notes: app.notes ?? undefined,
        attributes: (app.catalogItem?.attributes ?? []).map((a: any) => ({
          id: a.id,
          value: a.value,
          template: { id: a.template.id, title: a.template.title ?? a.template.name, dataType: a.template.dataType, unit: a.template.unit ? { symbol: a.template.unit.symbol } : undefined }
        }))
      }));
    }
  } catch (err) {
    error.value = err as Error;
    console.error('Error loading compatibility:', err);
  } finally {
    loading.value = false;
  }
};

const setViewMode = (mode: 'grid' | 'list') => {
  viewMode.value = mode;
};

const getAccuracyClass = (accuracy: string): string => {
  const level = accuracyLevels.find(l => l.value === accuracy);
  return level ? `accuracy-${level.class}` : 'accuracy-default';
};

const getAccuracyLabel = (accuracy: string): string => {
  const level = accuracyLevels.find(l => l.value === accuracy);
  return level ? level.label : accuracy;
};

const getAccuracyCount = (accuracy: string): number => {
  return items.value.filter(item => item.accuracy === accuracy).length;
};

const formatAttributeValue = (attr: any): string => {
  let value = attr.value;
  
  if (attr.template.dataType === 'NUMBER' && attr.template.unit) {
    value += ` ${attr.template.unit.symbol}`;
  }
  value = String(value ?? '');
  
  return String(value);
};

const viewCatalogItem = (item: CompatibilityItem) => {
  // Переход к детальной странице каталожной позиции
  window.open(`/catalog-items/${item.id}`, '_blank');
};

const editCompatibility = (item: CompatibilityItem) => {
  // Открытие формы редактирования совместимости
  console.log('Edit compatibility:', item);
};

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// Watchers
watch([accuracyFilter, brandFilter], () => {
  currentPage.value = 1;
});

// Lifecycle
onMounted(() => {
  loadCompatibility();
});
</script>

<style scoped>
.part-compatibility-section { display: grid; gap: 1rem; }
.section-header { display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem; }
.section-title { font-size: 1.125rem; font-weight: 600; }
.section-actions { display: flex; align-items: center; gap: 1rem; }
.view-controls { display: inline-flex; border: 1px solid #d1d5db; border-radius: .375rem; }
.view-button { padding: .25rem .75rem; color: #4b5563; }
.view-button.active { background: #3b82f6; color: white; }

.filter-controls { display: inline-flex; gap: .5rem; }
.accuracy-filter, .brand-filter { padding: .25rem .75rem; font-size: .875rem; border: 1px solid #d1d5db; border-radius: .375rem; }
.loading-state, .error-state { display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 2rem 0; color: #4b5563; }
.error-icon { color: #ef4444; margin-bottom: .5rem; }
.retry-button { margin-top: .5rem; padding: .5rem 1rem; background: #3b82f6; color: white; border-radius: .375rem; }

.compatibility-stats { display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem; padding: 1rem; background: #f9fafb; border-radius: .5rem; }
.stats-item { display: inline-flex; align-items: center; gap: .5rem; }
.stats-label { font-size: .875rem; color: #6b7280; }
.stats-value { font-weight: 600; }
.accuracy-legend { display: flex; flex-wrap: wrap; gap: .75rem; }
.legend-item { display: inline-flex; align-items: center; gap: .25rem; font-size: .875rem; }
.legend-indicator { width: .75rem; height: .75rem; border-radius: 9999px; }
.legend-indicator.exact { background: #22c55e; }
.legend-indicator.notes { background: #3b82f6; }
.legend-indicator.partial { background: #eab308; }
.legend-indicator.alternative { background: #f97316; }

.compatibility-grid { display: grid; grid-template-columns: 1fr; gap: 1rem; }
@media (min-width: 768px) { .compatibility-grid { grid-template-columns: 1fr 1fr; } }
@media (min-width: 1024px) { .compatibility-grid { grid-template-columns: 1fr 1fr 1fr; } }

.compatibility-card { background: var(--surface-0); border: 1px solid var(--surface-300); border-radius: .5rem; padding: 1rem; display: grid; gap: .75rem; }
.card-header { display: flex; justify-content: space-between; align-items: flex-start; }
.item-sku { font-weight: 600; }
.item-brand { font-size: .875rem; opacity: .8; }
.accuracy-badge { padding: .125rem .5rem; font-size: .75rem; font-weight: 600; border-radius: 9999px; }
.accuracy-badge.accuracy-exact { background: #dcfce7; color: #166534; }
.accuracy-badge.accuracy-notes { background: #dbeafe; color: #1e40af; }
.accuracy-badge.accuracy-partial { background: #fef9c3; color: #854d0e; }
.item-description { font-size: .875rem; }

.item-notes { display: inline-flex; align-items: center; gap: .25rem; font-size: .75rem; color: #1d4ed8; background: #eff6ff; padding: .5rem; border-radius: .375rem; }
.item-attributes { display: inline-flex; gap: .25rem; flex-wrap: wrap; }
.attribute-chip { font-size: .75rem; background: #f3f4f6; padding: .25rem .5rem; border-radius: .375rem; }
.attr-name { opacity: .8; }
.attr-value { font-weight: 600; }
.more-attributes { font-size: .75rem; color: #6b7280; background: #f3f4f6; padding: .25rem .5rem; border-radius: .375rem; }

.card-actions { display: inline-flex; gap: .5rem; }
.view-button, .edit-button { display: inline-flex; align-items: center; gap: .25rem; padding: .25rem .75rem; font-size: .875rem; border-radius: .375rem; border: 1px solid #d1d5db; }
.view-button { color: #374151; }
.edit-button { color: #1d4ed8; border-color: #93c5fd; }

.compatibility-list { background: var(--surface-0); border: 1px solid var(--surface-300); border-radius: .5rem; overflow: hidden; }
.list-header { display: grid; grid-template-columns: repeat(4, minmax(0,1fr)); gap: 1rem; padding: 1rem; background: #f3f4f6; border-bottom: 1px solid var(--surface-300); }
.header-cell { font-size: .875rem; font-weight: 600; }

.list-row { display: grid; grid-template-columns: repeat(4, minmax(0,1fr)); gap: 1rem; padding: 1rem; border-bottom: 1px solid var(--surface-300); }
.row-cell { display: flex; flex-direction: column; justify-content: center; }
.item-primary { display: grid; gap: .25rem; }
.row-actions { display: inline-flex; gap: .5rem; }
.action-button { padding: .25rem; }

.pagination { display: flex; justify-content: space-between; align-items: center; padding-top: 1rem; border-top: 1px solid var(--surface-300); }
.pagination-button { display: inline-flex; align-items: center; gap: .25rem; padding: .5rem .75rem; border: 1px solid #d1d5db; color: #374151; border-radius: .375rem; }
.pagination-button:disabled { opacity: .5; cursor: not-allowed; }
.pagination-info { font-size: .875rem; color: #6b7280; }
.empty-state { display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 3rem 0; color: #6b7280; }
.empty-icon { margin-bottom: 1rem; }
.empty-title { font-size: 1.125rem; font-weight: 500; margin-bottom: .5rem; }
.empty-description { text-align: center; max-width: 36rem; }
</style>