<template>
  <div class="grid gap-4">
    <div class="flex justify-between items-center flex-wrap gap-4">
      <h3 class="text-lg font-semibold">Совместимые каталожные позиции</h3>
      <div class="flex items-center gap-4">
        <div class="inline-flex border border-gray-300 rounded-md">
          <button
            @click="setViewMode('grid')"
            :class="[
              'px-3 py-1 text-gray-600',
              viewMode === 'grid' ? 'bg-blue-600 text-white' : ''
            ]"
          >
            <Icon name="grid" size="16" />
          </button>
          <button
            @click="setViewMode('list')"
            :class="[
              'px-3 py-1 text-gray-600',
              viewMode === 'list' ? 'bg-blue-600 text-white' : ''
            ]"
          >
            <Icon name="list" size="16" />
          </button>
        </div>

        <div class="inline-flex gap-2">
          <select
            v-model="accuracyFilter"
            class="px-3 py-1 text-sm border border-gray-300 rounded-md"
          >
            <option value="">Все уровни точности</option>
            <option value="EXACT_MATCH">Точное соответствие</option>
            <option value="MATCH_WITH_NOTES">С примечаниями</option>
            <option value="REQUIRES_MODIFICATION">Требует доработки</option>
            <option value="PARTIAL_MATCH">Частичное соответствие</option>
          </select>

          <select
            v-model="brandFilter"
            class="px-3 py-1 text-sm border border-gray-300 rounded-md"
          >
            <option value="">Все бренды</option>
            <option
              v-for="brand in availableBrands"
              :key="brand.id"
              :value="brand.id"
            >
              {{ brand.name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Загрузка -->
    <div v-if="loading" class="flex flex-col items-center justify-center py-8 text-gray-600">
      <Spinner size="md" />
      <span>Загрузка совместимых позиций...</span>
    </div>

    <!-- Ошибка -->
    <div v-else-if="error" class="flex flex-col items-center justify-center py-8 text-gray-600">
      <Icon name="alert-circle" size="24" class="text-red-500 mb-2" />
      <p class="error-text">Ошибка загрузки данных</p>
      <button @click="loadCompatibility" class="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md">
        Повторить
      </button>
    </div>

    <!-- Контент -->
    <div v-else-if="filteredItems.length > 0" class="space-y-4">
      <!-- Статистика -->
      <div class="flex justify-between items-center flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
        <div class="inline-flex items-center gap-2">
          <span class="text-sm text-gray-600">Найдено позиций:</span>
          <span class="font-semibold">{{ filteredItems.length }}</span>
        </div>
        <div class="flex flex-wrap gap-3">
          <div
            v-for="level in accuracyLevels"
            :key="level.value"
            class="inline-flex items-center gap-1 text-sm"
          >
            <div :class="[
              'w-3 h-3 rounded-full',
              level.class === 'exact' ? 'bg-green-500' :
              level.class === 'notes' ? 'bg-blue-500' :
              level.class === 'partial' ? 'bg-yellow-500' :
              'bg-orange-500'
            ]"></div>
            <span class="legend-label">{{ level.label }}</span>
            <span class="legend-count">({{ getAccuracyCount(level.value) }})</span>
          </div>
        </div>
      </div>

      <!-- Сетка -->
      <div v-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="item in paginatedItems"
          :key="item.id"
          class="bg-white border border-gray-300 rounded-lg p-4 grid gap-3"
        >
          <div class="flex justify-between items-start">
            <div class="item-info">
              <h4 class="font-semibold">{{ item.sku }}</h4>
              <p class="text-sm opacity-80">{{ item.brand?.name }}</p>
            </div>
            <div :class="[
              'px-2 py-0.5 text-xs font-semibold rounded-full',
              getAccuracyClass(item.accuracy) === 'accuracy-exact' ? 'bg-green-100 text-green-800' :
              getAccuracyClass(item.accuracy) === 'accuracy-notes' ? 'bg-blue-100 text-blue-800' :
              getAccuracyClass(item.accuracy) === 'accuracy-partial' ? 'bg-yellow-100 text-yellow-800' :
              'bg-orange-100 text-orange-800'
            ]">
              {{ getAccuracyLabel(item.accuracy) }}
            </div>
          </div>

          <div class="card-content">
            <p v-if="item.description" class="text-sm">
              {{ item.description }}
            </p>

            <div v-if="item.notes" class="inline-flex items-center gap-1 text-xs text-blue-800 bg-blue-50 p-2 rounded-md">
              <Icon name="info" size="14" />
              <span>{{ item.notes }}</span>
            </div>

            <div v-if="item.attributes?.length" class="inline-flex gap-1 flex-wrap">
              <div
                v-for="attr in item.attributes.slice(0, 3)"
                :key="attr.id"
                class="text-xs bg-gray-100 px-2 py-1 rounded-md"
              >
                <span class="opacity-80">{{ attr.template.title }}:</span>
                <span class="font-semibold">{{ formatAttributeValue(attr) }}</span>
              </div>
              <div v-if="item.attributes.length > 3" class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                +{{ item.attributes.length - 3 }} еще
              </div>
            </div>
          </div>

          <div class="inline-flex gap-2">
            <button
              @click="viewCatalogItem(item)"
              class="inline-flex items-center gap-1 px-3 py-1 text-sm rounded-md border border-gray-300 text-gray-700"
            >
              <Icon name="eye" size="16" />
              Подробнее
            </button>
            <button
              v-if="canEdit"
              @click="editCompatibility(item)"
              class="inline-flex items-center gap-1 px-3 py-1 text-sm rounded-md text-blue-800 border border-blue-300"
            >
              <Icon name="edit" size="16" />
              Изменить
            </button>
          </div>
        </div>
      </div>

      <!-- Список -->
      <div v-else class="bg-white border border-gray-300 rounded-lg overflow-hidden">
        <div class="grid grid-cols-4 gap-4 p-4 bg-gray-100 border-b border-gray-300">
          <div class="text-sm font-semibold">SKU / Бренд</div>
          <div class="text-sm font-semibold">Описание</div>
          <div class="text-sm font-semibold">Точность</div>
          <div class="text-sm font-semibold">Действия</div>
        </div>

        <div
          v-for="item in paginatedItems"
          :key="item.id"
          class="grid grid-cols-4 gap-4 p-4 border-b border-gray-300"
        >
          <div class="flex flex-col justify-center">
            <div class="grid gap-1">
              <span class="font-semibold">{{ item.sku }}</span>
              <span class="text-sm opacity-80">{{ item.brand?.name }}</span>
            </div>
          </div>

          <div class="flex flex-col justify-center">
            <p class="text-sm">{{ item.description || 'Без описания' }}</p>
            <div v-if="item.notes" class="inline-flex items-center gap-1 text-xs text-blue-800 bg-blue-50 p-1 rounded-md mt-1">
              <Icon name="info" size="12" />
              <span>{{ item.notes }}</span>
            </div>
          </div>

          <div class="flex flex-col justify-center">
            <div :class="[
              'px-2 py-0.5 text-xs font-semibold rounded-full w-fit',
              getAccuracyClass(item.accuracy) === 'accuracy-exact' ? 'bg-green-100 text-green-800' :
              getAccuracyClass(item.accuracy) === 'accuracy-notes' ? 'bg-blue-100 text-blue-800' :
              getAccuracyClass(item.accuracy) === 'accuracy-partial' ? 'bg-yellow-100 text-yellow-800' :
              'bg-orange-100 text-orange-800'
            ]">
              {{ getAccuracyLabel(item.accuracy) }}
            </div>
          </div>

          <div class="flex flex-col justify-center">
            <div class="inline-flex gap-2">
              <button
                @click="viewCatalogItem(item)"
                class="p-1"
              >
                <Icon name="eye" size="14" />
              </button>
              <button
                v-if="canEdit"
                @click="editCompatibility(item)"
                class="p-1"
              >
                <Icon name="edit" size="14" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Пагинация -->
      <div v-if="totalPages > 1" class="flex justify-between items-center pt-4 border-t border-gray-300">
        <button
          @click="previousPage"
          :disabled="currentPage === 1"
          class="inline-flex items-center gap-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Icon name="chevron-left" size="16" />
          Назад
        </button>

        <div class="text-sm text-gray-600">
          Страница {{ currentPage }} из {{ totalPages }}
        </div>

        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="inline-flex items-center gap-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Вперед
          <Icon name="chevron-right" size="16" />
        </button>
      </div>
    </div>

    <!-- Пустое состояние -->
    <div v-else class="flex flex-col items-center justify-center py-12 text-gray-600">
      <Icon name="package" size="48" class="mb-4" />
      <h4 class="text-lg font-medium mb-2">Совместимые позиции не найдены</h4>
      <p class="text-center max-w-lg">
        Для этой запчасти пока не указаны совместимые каталожные позиции
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import { useAuth } from '@/composables/useAuth';
import Icon from '@/components/ui/Icon.vue';
import Spinner from '@/components/ui/Spinner.vue';

export interface CompatibilityItem {
  id: number;
  sku: string;
  description?: string;
  brand?: {
    id: number;
    name: string;
  };
  accuracy: 'EXACT_MATCH' | 'MATCH_WITH_NOTES' | 'REQUIRES_MODIFICATION' | 'PARTIAL_MATCH';
  notes?: string;
  attributes?: Array<{
    id: number;
    value: any;
    template: {
      id: number;
      title: string;
      dataType: string;
      unit?: {
        symbol: string;
      };
    };
  }>;
}

export interface PartCompatibilitySectionProps {
  partId: number;
  initialViewMode?: 'grid' | 'list';
  itemsPerPage?: number;
}

const props = withDefaults(defineProps<PartCompatibilitySectionProps>(), {
  initialViewMode: 'grid',
  itemsPerPage: 12
});

const trpc = useTrpc();
const { user } = useAuth();

// Реактивные данные
const loading = ref(false);
const error = ref<Error | null>(null);
const items = ref<CompatibilityItem[]>([]);
const viewMode = ref(props.initialViewMode);
const accuracyFilter = ref('');
const brandFilter = ref('');
const currentPage = ref(1);

// Константы
const accuracyLevels = [
  { value: 'EXACT_MATCH', label: 'Точное соответствие', class: 'exact' },
  { value: 'MATCH_WITH_NOTES', label: 'С примечаниями', class: 'notes' },
  { value: 'REQUIRES_MODIFICATION', label: 'Требует доработки', class: 'mod' },
  { value: 'PARTIAL_MATCH', label: 'Частичное соответствие', class: 'partial' }
];

// Вычисляемые свойства
const canEdit = computed(() => {
  return user.value?.role === 'ADMIN' || user.value?.role === 'SHOP';
});

const availableBrands = computed(() => {
  const brands = new Map();
  items.value.forEach(item => {
    if (item.brand) {
      brands.set(item.brand.id, item.brand);
    }
  });
  return Array.from(brands.values()).sort((a, b) => a.name.localeCompare(b.name));
});

const filteredItems = computed(() => {
  let filtered = items.value;
  
  if (accuracyFilter.value) {
    filtered = filtered.filter(item => item.accuracy === accuracyFilter.value);
  }
  
  if (brandFilter.value) {
    filtered = filtered.filter(item => item.brand?.id === Number(brandFilter.value));
  }
  
  return filtered;
});

const totalPages = computed(() => {
  return Math.ceil(filteredItems.value.length / props.itemsPerPage);
});

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * props.itemsPerPage;
  const end = start + props.itemsPerPage;
  return filteredItems.value.slice(start, end);
});

// Методы
const loadCompatibility = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const result = await trpc.client.crud.partApplicability.findMany.query({
      where: { partId: props.partId },
      include: {
        catalogItem: {
          include: {
            brand: true,
            attributes: {
              include: { template: { include: { unit: true } } }
            }
          }
        }
      }
    });

    if (result && Array.isArray(result)) {
      items.value = result.map((app: any) => ({
        id: app.catalogItem?.id,
        sku: app.catalogItem?.sku,
        description: app.catalogItem?.description,
        brand: app.catalogItem?.brand ? { id: app.catalogItem.brand.id, name: app.catalogItem.brand.name } : undefined,
        accuracy: app.accuracy,
        notes: app.notes ?? undefined,
        attributes: (app.catalogItem?.attributes ?? []).map((a: any) => ({
          id: a.id,
          value: a.value,
          template: { id: a.template.id, title: a.template.title ?? a.template.name, dataType: a.template.dataType, unit: a.template.unit ? { symbol: a.template.unit.symbol } : undefined }
        }))
      }));
    }
  } catch (err) {
    error.value = err as Error;
    console.error('Error loading compatibility:', err);
  } finally {
    loading.value = false;
  }
};

const setViewMode = (mode: 'grid' | 'list') => {
  viewMode.value = mode;
};

const getAccuracyClass = (accuracy: string): string => {
  const level = accuracyLevels.find(l => l.value === accuracy);
  return level ? `accuracy-${level.class}` : 'accuracy-default';
};

const getAccuracyLabel = (accuracy: string): string => {
  const level = accuracyLevels.find(l => l.value === accuracy);
  return level ? level.label : accuracy;
};

const getAccuracyCount = (accuracy: string): number => {
  return items.value.filter(item => item.accuracy === accuracy).length;
};

const formatAttributeValue = (attr: any): string => {
  let value = attr.value;
  
  if (attr.template.dataType === 'NUMBER' && attr.template.unit) {
    value += ` ${attr.template.unit.symbol}`;
  }
  value = String(value ?? '');
  
  return String(value);
};

const viewCatalogItem = (item: CompatibilityItem) => {
  // Переход к детальной странице каталожной позиции
  window.open(`/catalog-items/${item.id}`, '_blank');
};

const editCompatibility = (item: CompatibilityItem) => {
  // Открытие формы редактирования совместимости
  console.log('Edit compatibility:', item);
};

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// Watchers
watch([accuracyFilter, brandFilter], () => {
  currentPage.value = 1;
});

// Lifecycle
onMounted(() => {
  loadCompatibility();
});
</script>

