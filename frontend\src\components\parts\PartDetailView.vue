<template>
  <div class="part-detail-view">
    <!-- Загрузка -->
    <div v-if="loading" class="loading-state">
      <Spinner size="lg" />
      <span>Загрузка информации о запчасти...</span>
    </div>

    <!-- Ошибка -->
    <ErrorBoundary v-else-if="error" :error="error" @retry="loadPart" />

    <!-- Основной контент -->
    <div v-else-if="part" class="part-content">
      <!-- Заголовок -->
      <div class="part-header">
        <div class="part-title-section">
          <h1 class="part-title">{{ part.name || 'Запчасть без названия' }}</h1>
          <div class="part-meta">
            <span class="part-id">ID: {{ part.id }}</span>
            <span class="part-category">{{ part.partCategory?.name }}</span>
            <span v-if="part.level > 0" class="part-level">Уровень: {{ part.level }}</span>
          </div>
        </div>

        <div class="part-actions">
          <button @click="sharePart" class="share-button">
            <Icon name="share" size="20" />
            Поделиться
          </button>
        </div>
      </div>

      <!-- Основная информация -->
      <div class="part-main">
        <!-- Изображения -->
        <div class="part-images">
          <PartImageGallery
            :images="partImages"
            :main-image="part.image"
            @image-click="onImageClick"
          />
        </div>

        <!-- Информация -->
        <div class="part-info">
          <!-- Базовая информация -->
          <div class="info-section">
            <h3 class="section-title">Основная информация</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">Категория:</span>
                <span class="info-value">{{ part.partCategory?.name || 'Не указана' }}</span>
              </div>
              <div class="info-item" v-if="part.parentId">
                <span class="info-label">Родительская деталь:</span>
                <button type="button" class="info-link" @click="navigate(`/parts/${part.parentId}`)">
                  {{ getParentPartName() }}
                </button>
              </div>
              <div class="info-item">
                <span class="info-label">Путь:</span>
                <span class="info-value">{{ part.path }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Создано:</span>
                <span class="info-value">{{ formatDate(part.createdAt) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Обновлено:</span>
                <span class="info-value">{{ formatDate(part.updatedAt) }}</span>
              </div>
            </div>
          </div>

          <!-- Атрибуты -->
          <PartAttributesSection
            v-if="part.attributes?.length"
            :attributes="part.attributes"
            :show-groups="true"
            class="attributes-section"
          />

          <!-- Совместимые каталожные позиции -->
          <PartCompatibilitySection
            v-if="showCompatibility"
            :part-id="part.id"
            class="compatibility-section"
          />

          <!-- Применимость к оборудованию -->
          <PartEquipmentSection
            v-if="part.equipmentApplicabilities?.length"
            :applicabilities="part.equipmentApplicabilities"
            class="equipment-section"
          />
        </div>
      </div>

      <!-- Связанные детали -->
      <div v-if="showRelated && relatedParts.length" class="related-parts">
        <h3 class="section-title">Связанные детали</h3>
        <div class="related-parts-grid">
          <div
            v-for="relatedPart in relatedParts"
            :key="relatedPart.id"
            class="related-part-card"
            @click="navigateToPart(relatedPart.id)"
          >
            <div class="related-part-image">
              <img
                v-if="relatedPart.image"
                :src="relatedPart.image.url"
                :alt="relatedPart.name"
                class="part-thumbnail"
              />
              <div v-else class="no-image-placeholder">
                <Icon name="image" size="24" />
              </div>
            </div>
            <div class="related-part-info">
              <h4 class="related-part-name">{{ relatedPart.name || 'Без названия' }}</h4>
              <p class="related-part-category">{{ relatedPart.partCategory?.name }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Дочерние детали -->
      <div v-if="childParts.length" class="child-parts">
        <h3 class="section-title">Дочерние детали</h3>
        <div class="child-parts-list">
          <div
            v-for="childPart in childParts"
            :key="childPart.id"
            class="child-part-item"
            @click="navigateToPart(childPart.id)"
          >
            <div class="child-part-image">
              <img
                v-if="childPart.image"
                :src="childPart.image.url"
                :alt="childPart.name"
                class="part-thumbnail-small"
              />
              <div v-else class="no-image-placeholder-small">
                <Icon name="image" size="16" />
              </div>
            </div>
            <div class="child-part-info">
              <h4 class="child-part-name">{{ childPart.name || 'Без названия' }}</h4>
              <p class="child-part-path">{{ childPart.path }}</p>
            </div>
            <Icon name="chevron-right" size="16" class="child-part-arrow" />
          </div>
        </div>
      </div>
    </div>

    <!-- Пустое состояние -->
    <EmptyState
      v-else
      title="Запчасть не найдена"
      description="Запчасть с указанным ID не существует или была удалена"
    >
      <template #actions>
        <Button @click="goToCatalog" variant="primary">
          Перейти к каталогу
        </Button>
      </template>
    </EmptyState>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { navigate } from 'astro:transitions/client';
import { useTrpc } from '@/composables/useTrpc';
import PartImageGallery from './PartImageGallery.vue';
import PartAttributesSection from './PartAttributesSection.vue';
import PartCompatibilitySection from './PartCompatibilitySection.vue';
import PartEquipmentSection from './PartEquipmentSection.vue';
import Icon from '@/components/ui/Icon.vue';
import Button from '@/components/ui/Button.vue';
import Spinner from '@/components/ui/Spinner.vue';
import ErrorBoundary from '@/components/ui/ErrorBoundary.vue';
import EmptyState from '@/components/ui/EmptyState.vue';

export interface PartDetailViewProps {
  partId: number;
  showRelated?: boolean;
  showCompatibility?: boolean;
}

const props = withDefaults(defineProps<PartDetailViewProps>(), {
  showRelated: true,
  showCompatibility: true
});

const trpc = useTrpc();

// Реактивные данные
const loading = ref(false);
const error = ref<Error | null>(null);
const part = ref<any>(null);
const relatedParts = ref<any[]>([]);
const childParts = ref<any[]>([]);

// Вычисляемые свойства
const partImages = computed(() => {
  if (!part.value) return [];

  const images = [] as any[];
  if (part.value.image) {
    images.push(part.value.image);
  }

  return images;
});

// Методы
const loadPart = async () => {
  if (!props.partId) return;

  loading.value = true;
  error.value = null;

  try {
    const result = await trpc.client.crud.part.findUnique.query({
      where: { id: props.partId },
      include: {
        partCategory: true,
        attributes: { include: { template: { include: { unit: true, group: true } } } },
        equipmentApplicabilities: {
          include: {
            equipmentModel: {
              include: { brand: true, attributes: { include: { template: { include: { group: true } } } } }
            }
          }
        },
        image: true
      }
    });

    if (result) {
      part.value = result;
      await loadRelatedData();
    }
  } catch (err) {
    error.value = err as Error;
    console.error('Error loading part:', err);
  } finally {
    loading.value = false;
  }
};

const loadRelatedData = async () => {
  if (!part.value) return;

  try {
    // Загружаем связанные детали (минимальный показ ближайших детей)
    if (props.showRelated) {
      const siblings = await trpc.parts.findMany({
        where: { parentId: part.value.parentId ?? undefined },
        include: { partCategory: true, image: true },
        take: 6
      });
      if (siblings) relatedParts.value = (siblings as any[]).filter(p => p.id !== part.value.id);
    }

    // Загружаем дочерние детали
    const children = await trpc.parts.findMany({
      where: { parentId: part.value.id },
      include: {
        partCategory: true,
        image: true
      },
      orderBy: { path: 'asc' }
    });

    if (children) {
      childParts.value = children as any[];
    }
  } catch (err) {
    console.error('Error loading related data:', err);
  }
};


const sharePart = async () => {
  const url = window.location.href;
  
  if (navigator.share) {
    try {
      await navigator.share({
        title: part.value?.name || 'Запчасть',
        text: `Посмотрите эту запчасть: ${part.value?.name}`,
        url: url
      });
    } catch (err) {
      console.error('Error sharing:', err);
    }
  } else {
    // Fallback - копируем в буфер обмена
    try {
      await navigator.clipboard.writeText(url);
      // Показываем уведомление
      console.log('URL copied to clipboard');
    } catch (err) {
      console.error('Error copying to clipboard:', err);
    }
  }
};

const onImageClick = (image: any, index: number) => {
  // Открываем галерею изображений в полноэкранном режиме
  console.log('Image clicked:', image, index);
};

const navigateToPart = (partId: number) => {
  navigate(`/parts/${partId}`);
};

const goToCatalog = () => navigate('/catalog');

const getParentPartName = (): string => {
  // Здесь можно загрузить название родительской детали
  return `Деталь #${part.value?.parentId}`;
};

const formatDate = (date: Date | string): string => {
  const d = new Date(date);
  return d.toLocaleDateString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Watchers
watch(() => props.partId, () => {
  loadPart();
});

// Lifecycle
onMounted(() => {
  loadPart();
});
</script>

<style scoped>
.part-detail-view { max-width: 80rem; margin: 0 auto; padding: 1.5rem 1rem; }
.loading-state { display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 3rem 0; color: #4b5563; }
.loading-state span { margin-top: 1rem; font-size: 1.125rem; }
.part-content { display: grid; gap: 2rem; }
.part-header { display: flex; justify-content: space-between; align-items: flex-start; padding-bottom: 1.5rem; border-bottom: 1px solid var(--surface-300); }
.part-title-section { flex: 1; }
.part-title { font-size: 1.875rem; font-weight: 800; margin-bottom: .5rem; }
.part-meta { display: inline-flex; align-items: center; gap: 1rem; font-size: .875rem; color: #6b7280; }
.part-id { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace; background: #f3f4f6; padding: .125rem .5rem; border-radius: .25rem; }
.part-category { background: #dbeafe; color: #1e40af; padding: .125rem .5rem; border-radius: .25rem; }
.part-level { background: #dcfce7; color: #166534; padding: .125rem .5rem; border-radius: .25rem; }
.part-actions { display: inline-flex; gap: .75rem; }
.share-button { display: inline-flex; align-items: center; gap: .5rem; padding: .5rem 1rem; border: 1px solid #d1d5db; border-radius: .5rem; color: #374151; }
.part-main { display: grid; grid-template-columns: 1fr; gap: 2rem; }
@media (min-width: 1024px) { .part-main { grid-template-columns: 1fr 1fr; } }
.part-images { position: sticky; top: 1.5rem; }
.part-info { display: grid; gap: 1.5rem; }
.info-section { background: var(--surface-0); border: 1px solid var(--surface-300); border-radius: .5rem; padding: 1.5rem; }
.section-title { font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; }
.info-grid { display: grid; grid-template-columns: 1fr; gap: 1rem; }
@media (min-width: 768px) { .info-grid { grid-template-columns: 1fr 1fr; } }
.info-item { display: flex; flex-direction: column; gap: .25rem; }
.info-label { font-size: .875rem; font-weight: 600; color: #6b7280; }
.info-value { font-size: .875rem; }
.info-link { font-size: .875rem; color: #2563eb; background: transparent; border: 0; padding: 0; cursor: pointer; }
.attributes-section, .compatibility-section, .equipment-section, .related-parts { background: var(--surface-0); border: 1px solid var(--surface-300); border-radius: .5rem; padding: 1.5rem; }
.related-parts-grid { display: grid; grid-template-columns: 1fr; gap: 1rem; }
@media (min-width: 640px) { .related-parts-grid { grid-template-columns: 1fr 1fr; } }
@media (min-width: 1024px) { .related-parts-grid { grid-template-columns: 1fr 1fr 1fr; } }
.related-part-card { display: flex; align-items: center; gap: .75rem; padding: .75rem; border: 1px solid var(--surface-300); border-radius: .5rem; cursor: pointer; }
.related-part-image { flex-shrink: 0; }
.part-thumbnail { width: 3rem; height: 3rem; object-fit: cover; border-radius: .375rem; }
.no-image-placeholder { width: 3rem; height: 3rem; display: inline-flex; align-items: center; justify-content: center; border-radius: .375rem; color: #9ca3af; background: #f3f4f6; }
.related-part-info { flex: 1; min-width: 0; }
.related-part-name { font-size: .875rem; font-weight: 600; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.related-part-category { font-size: .75rem; color: #6b7280; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.child-parts { background: var(--surface-0); border: 1px solid var(--surface-300); border-radius: .5rem; padding: 1.5rem; }
.child-parts-list { display: grid; gap: .5rem; }
.child-part-item { display: flex; align-items: center; gap: .75rem; padding: .75rem; border: 1px solid var(--surface-300); border-radius: .5rem; cursor: pointer; }
.child-part-image { flex-shrink: 0; }
.part-thumbnail-small { width: 2rem; height: 2rem; object-fit: cover; border-radius: .375rem; }
.no-image-placeholder-small { width: 2rem; height: 2rem; display: inline-flex; align-items: center; justify-content: center; border-radius: .375rem; color: #9ca3af; background: #f3f4f6; }
.child-part-info { flex: 1; min-width: 0; }
.child-part-name { font-size: .875rem; font-weight: 600; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.child-part-path { font-size: .75rem; color: #6b7280; font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace; }
.child-part-arrow { color: #9ca3af; }
</style>