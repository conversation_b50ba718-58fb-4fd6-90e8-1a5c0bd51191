<template>
  <div class="space-y-4">
    <div class="flex justify-between items-center">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Применимость к оборудованию</h3>
      <div class="flex items-center space-x-2">
        <button
          @click="toggleExpanded"
          class="inline-flex items-center gap-1 px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-md transition-colors duration-200"
        >
          <Icon :name="expanded ? 'chevron-up' : 'chevron-down'" size="16" />
          {{ expanded ? 'Свернуть' : 'Развернуть' }}
        </button>
      </div>
    </div>

    <div v-show="expanded" class="grid gap-6">
      <!-- Группировка по брендам -->
      <div class="grid gap-4">
        <div
          v-for="brandGroup in equipmentByBrand"
          :key="brandGroup.brand.id"
          class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
        >
          <div class="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
            <div class="flex items-center space-x-3">
              <h4 class="text-base font-medium text-gray-900 dark:text-white">{{ brandGroup.brand.name }}</h4>
              <span class="text-sm text-gray-600 dark:text-gray-400 bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">{{ brandGroup.models.length }} моделей</span>
            </div>
            <button
              @click="toggleBrandExpanded(brandGroup.brand.id)"
              class="p-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors duration-200"
            >
              <Icon
                :name="brandGroup.expanded ? 'chevron-up' : 'chevron-down'"
                size="16"
              />
            </button>
          </div>

          <div v-show="brandGroup.expanded" class="p-4 space-y-4">
            <div
              v-for="applicability in brandGroup.models"
              :key="applicability.id"
              class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 space-y-3"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <h5 class="text-base font-medium">
                    <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-200" @click="navigate(`/equipment/${applicability.equipmentModel.id}`)">
                      {{ applicability.equipmentModel.model }}
                    </button>
                  </h5>

                  <div class="flex items-center space-x-2 mt-1 text-sm text-gray-600 dark:text-gray-400">
                    <span v-if="applicability.equipmentModel.year" class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                      {{ applicability.equipmentModel.year }}
                    </span>
                    <span v-if="applicability.equipmentModel.category" class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                      {{ applicability.equipmentModel.category.name }}
                    </span>
                  </div>
                </div>

                <div class="flex-shrink-0">
                  <div :class="getStatusClass(applicability.status)">
                    {{ getStatusLabel(applicability.status) }}
                  </div>
                </div>
              </div>

              <!-- Дополнительная информация -->
              <div v-if="applicability.notes || applicability.conditions" class="equipment-details">
                <div v-if="applicability.notes" class="applicability-notes">
                  <Icon name="info" size="14" />
                  <span>{{ applicability.notes }}</span>
                </div>
                
                <div v-if="applicability.conditions" class="applicability-conditions">
                  <Icon name="alert-triangle" size="14" />
                  <span>{{ applicability.conditions }}</span>
                </div>
              </div>

              <!-- Атрибуты оборудования -->
              <div v-if="showEquipmentAttributes && applicability.equipmentModel.attributes?.length" class="equipment-attributes">
                <div class="attributes-header">
                  <span class="attributes-title">Характеристики:</span>
                  <button
                    @click="toggleEquipmentAttributes(applicability.id)"
                    class="attributes-toggle"
                  >
                    <Icon
                      :name="expandedAttributes.has(applicability.id) ? 'chevron-up' : 'chevron-down'"
                      size="12"
                    />
                  </button>
                </div>
                
                <div v-show="expandedAttributes.has(applicability.id)" class="attributes-list">
                  <div
                    v-for="attr in applicability.equipmentModel.attributes.slice(0, 5)"
                    :key="attr.id"
                    class="attribute-item"
                  >
                    <span class="attr-name">{{ attr.template.title }}:</span>
                    <span class="attr-value">{{ formatAttributeValue(attr) }}</span>
                  </div>
                  <div v-if="applicability.equipmentModel.attributes.length > 5" class="more-attributes">
                    +{{ applicability.equipmentModel.attributes.length - 5 }} еще
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Статистика -->
      <div v-if="showStats" class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center">
            <span class="block text-sm text-gray-600 dark:text-gray-400">Всего моделей:</span>
            <span class="block text-lg font-semibold text-gray-900 dark:text-white">{{ totalModels }}</span>
          </div>
          <div class="text-center">
            <span class="block text-sm text-gray-600 dark:text-gray-400">Брендов:</span>
            <span class="block text-lg font-semibold text-gray-900 dark:text-white">{{ totalBrands }}</span>
          </div>
          <div class="text-center">
            <span class="block text-sm text-gray-600 dark:text-gray-400">Совместимо:</span>
            <span class="block text-lg font-semibold text-gray-900 dark:text-white">{{ compatibleCount }}</span>
          </div>
          <div class="text-center">
            <span class="block text-sm text-gray-600 dark:text-gray-400">С ограничениями:</span>
            <span class="block text-lg font-semibold text-gray-900 dark:text-white">{{ conditionalCount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Пустое состояние -->
    <div v-if="applicabilities.length === 0" class="empty-state">
      <Icon name="settings" size="48" class="empty-icon" />
      <h4 class="empty-title">Применимость не указана</h4>
      <p class="empty-description">
        Для этой запчасти пока не указана применимость к конкретным моделям оборудования
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { navigate } from 'astro:transitions/client';
import Icon from '@/components/ui/Icon.vue';

export interface EquipmentApplicability {
  id: number;
  status: 'COMPATIBLE' | 'CONDITIONAL' | 'NOT_COMPATIBLE';
  notes?: string;
  conditions?: string;
  equipmentModel: {
    id: string;
    model: string;
    year?: number;
    brand: {
      id: number;
      name: string;
    };
    category?: {
      id: number;
      name: string;
    };
    attributes?: Array<{
      id: number;
      value: any;
      template: {
        id: number;
        title: string;
        dataType: string;
        unit?: {
          symbol: string;
        };
      };
    }>;
  };
}

export interface PartEquipmentSectionProps {
  applicabilities: EquipmentApplicability[];
  showEquipmentAttributes?: boolean;
  showStats?: boolean;
  initialExpanded?: boolean;
}

const props = withDefaults(defineProps<PartEquipmentSectionProps>(), {
  showEquipmentAttributes: true,
  showStats: true,
  initialExpanded: true
});

// Реактивные данные
const expanded = ref(props.initialExpanded);
const expandedBrands = ref<Set<number>>(new Set());
const expandedAttributes = ref<Set<number>>(new Set());

// Вычисляемые свойства
const equipmentByBrand = computed(() => {
  const brandMap = new Map();
  
  props.applicabilities.forEach(applicability => {
    const brand = applicability.equipmentModel.brand;
    
    if (!brandMap.has(brand.id)) {
      brandMap.set(brand.id, {
        brand,
        models: [],
        expanded: expandedBrands.value.has(brand.id)
      });
    }
    
    brandMap.get(brand.id).models.push(applicability);
  });
  
  // Сортируем бренды по названию
  const brands = Array.from(brandMap.values()).sort((a, b) => 
    a.brand.name.localeCompare(b.brand.name)
  );
  
  // Сортируем модели внутри каждого бренда
  brands.forEach(brandGroup => {
    brandGroup.models.sort((a: EquipmentApplicability, b: EquipmentApplicability) => {
      // Сначала по статусу (совместимые первыми)
      const orderMap: Record<'COMPATIBLE'|'CONDITIONAL'|'NOT_COMPATIBLE', number> = { COMPATIBLE: 0, CONDITIONAL: 1, NOT_COMPATIBLE: 2 };
      if (a.status !== b.status) {
        return orderMap[a.status] - orderMap[b.status];
      }
      // Потом по модели
      return a.equipmentModel.model.localeCompare(b.equipmentModel.model);
    });
  });
  
  return brands;
});

const totalModels = computed(() => props.applicabilities.length);

const totalBrands = computed(() => {
  const brands = new Set();
  props.applicabilities.forEach(app => brands.add(app.equipmentModel.brand.id));
  return brands.size;
});

const compatibleCount = computed(() => {
  return props.applicabilities.filter(app => app.status === 'COMPATIBLE').length;
});

const conditionalCount = computed(() => {
  return props.applicabilities.filter(app => app.status === 'CONDITIONAL').length;
});

// Методы
const toggleExpanded = () => {
  expanded.value = !expanded.value;
};

const toggleBrandExpanded = (brandId: number) => {
  if (expandedBrands.value.has(brandId)) {
    expandedBrands.value.delete(brandId);
  } else {
    expandedBrands.value.add(brandId);
  }
};

const toggleEquipmentAttributes = (applicabilityId: number) => {
  if (expandedAttributes.value.has(applicabilityId)) {
    expandedAttributes.value.delete(applicabilityId);
  } else {
    expandedAttributes.value.add(applicabilityId);
  }
};

const getStatusClass = (status: string): string => {
  const base = 'px-3 py-1 text-sm font-medium rounded-full';
  switch (status) {
    case 'COMPATIBLE':
      return `${base} bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200`;
    case 'CONDITIONAL':
      return `${base} bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200`;
    case 'NOT_COMPATIBLE':
      return `${base} bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200`;
    default:
      return `${base} bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200`;
  }
};

const getStatusLabel = (status: string): string => {
  switch (status) {
    case 'COMPATIBLE':
      return 'Совместимо';
    case 'CONDITIONAL':
      return 'С ограничениями';
    case 'NOT_COMPATIBLE':
      return 'Не совместимо';
    default:
      return 'Неизвестно';
  }
};

const formatAttributeValue = (attr: any): string => {
  let value = attr.value;
  
  if (attr.template.dataType === 'NUMBER' && attr.template.unit) {
    value += ` ${attr.template.unit.symbol}`;
  }
  
  return String(value);
};
</script>

<style scoped>
/* оставляем только анимации и минимальные вспомогательные правила */
.equipment-content { animation: slideDown .2s ease-out; }
.brand-models { animation: slideDown .15s ease-out; }
.attributes-list { animation: slideDown .15s ease-out; }
@keyframes slideDown { from { opacity: 0; transform: translateY(-8px);} to { opacity: 1; transform: translateY(0);} }
</style>