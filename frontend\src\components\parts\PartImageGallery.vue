<template>
  <div class="w-full">
    <!-- Основное изображение -->
    <div class="relative mb-4">
      <div class="relative bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden aspect-square">
        <img
          v-if="currentImage"
          :src="currentImage.url"
          :alt="currentImage.alt || 'Изображение запчасти'"
          class="w-full h-full object-cover cursor-pointer transition-transform duration-200 hover:scale-105"
          @click="openFullscreen"
          @load="onImageLoad"
          @error="onImageError"
        />
        <div v-else class="w-full h-full flex flex-col items-center justify-center text-gray-400 dark:text-gray-500">
          <Icon name="image" size="64" />
          <span class="mt-2 text-sm">Изображение отсутствует</span>
        </div>

        <!-- Индикатор загрузки -->
        <div v-if="imageLoading" class="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-800 bg-opacity-75">
          <Spinner size="md" />
        </div>

        <!-- Кнопки навигации -->
        <div v-if="images.length > 1" class="absolute inset-y-0 left-0 right-0 flex items-center justify-between px-2">
          <button
            @click="previousImage"
            :disabled="currentIndex === 0"
            class="p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-75 transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed"
          >
            <Icon name="chevron-left" size="24" />
          </button>
          <button
            @click="nextImage"
            :disabled="currentIndex === images.length - 1"
            class="p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-75 transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed"
          >
            <Icon name="chevron-right" size="24" />
          </button>
        </div>

        <!-- Индикатор количества изображений -->
        <div v-if="images.length > 1" class="absolute bottom-4 right-4 px-2 py-1 rounded bg-black bg-opacity-50 text-white text-sm">
          {{ currentIndex + 1 }} / {{ images.length }}
        </div>

        <!-- Кнопка полноэкранного режима -->
        <button
          v-if="currentImage"
          @click="openFullscreen"
          class="absolute top-4 right-4 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-75 transition-all duration-200"
        >
          <Icon name="maximize" size="20" />
        </button>
      </div>
    </div>

    <!-- Миниатюры -->
    <div v-if="images.length > 1 && showThumbnails" class="mt-4">
      <div class="flex space-x-2 overflow-x-auto pb-2 scrollbar-thin scrollbar-track-gray-100 dark:scrollbar-track-gray-700 scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-gray-500 scrollbar-thumb-rounded-full">
        <div
          v-for="(image, index) in images"
          :key="index"
          :class="[
            'flex-shrink-0 cursor-pointer border-2 rounded-lg overflow-hidden transition-all duration-200',
            index === currentIndex
              ? 'border-blue-500'
              : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600'
          ]"
          @click="setCurrentImage(index)"
        >
          <img
            :src="image.thumbnailUrl || image.url"
            :alt="image.alt || `Изображение ${index + 1}`"
            class="w-16 h-16 object-cover"
            @error="onThumbnailError"
          />
        </div>
      </div>
    </div>

    <!-- Полноэкранная галерея -->
    <div
      v-if="fullscreenMode"
      class="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center"
      @click="closeFullscreen"
    >
      <div class="w-full h-full max-w-7xl mx-auto flex flex-col" @click.stop>
        <div class="flex justify-between items-center p-4 text-white">
          <div class="text-lg font-medium">
            {{ currentImage?.alt || `Изображение ${currentIndex + 1}` }}
          </div>
          <button @click="closeFullscreen" class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors duration-200">
            <Icon name="x" size="24" />
          </button>
        </div>

        <div class="flex-1 relative flex items-center justify-center p-4">
          <img
            v-if="currentImage"
            :src="currentImage.fullUrl || currentImage.url"
            :alt="currentImage.alt"
            class="max-w-full max-h-full object-contain"
          />

          <!-- Навигация в полноэкранном режиме -->
          <div v-if="images.length > 1" class="absolute inset-y-0 left-0 right-0 flex items-center justify-between px-4">
            <button
              @click="previousImage"
              :disabled="currentIndex === 0"
              class="p-3 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-75 transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed"
            >
              <Icon name="chevron-left" size="32" />
            </button>
            <button
              @click="nextImage"
              :disabled="currentIndex === images.length - 1"
              class="p-3 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-75 transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed"
            >
              <Icon name="chevron-right" size="32" />
            </button>
          </div>
        </div>

        <!-- Миниатюры в полноэкранном режиме -->
        <div v-if="images.length > 1" class="flex justify-center space-x-2 p-4 overflow-x-auto scrollbar-thin scrollbar-track-gray-100 dark:scrollbar-track-gray-700 scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-gray-500 scrollbar-thumb-rounded-full">
          <div
            v-for="(image, index) in images"
            :key="index"
            :class="[
              'flex-shrink-0 cursor-pointer border-2 rounded overflow-hidden transition-all duration-200',
              index === currentIndex
                ? 'border-white'
                : 'border-transparent hover:border-gray-400'
            ]"
            @click="setCurrentImage(index)"
          >
            <img
              :src="image.thumbnailUrl || image.url"
              :alt="image.alt || `Изображение ${index + 1}`"
              class="w-12 h-12 object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import Icon from '@/components/ui/Icon.vue';
import Spinner from '@/components/ui/Spinner.vue';

export interface ImageItem {
  id?: number;
  url: string;
  thumbnailUrl?: string;
  fullUrl?: string;
  alt?: string;
  title?: string;
}

export interface PartImageGalleryProps {
  images: ImageItem[];
  mainImage?: ImageItem;
  showThumbnails?: boolean;
  allowFullscreen?: boolean;
}

const props = withDefaults(defineProps<PartImageGalleryProps>(), {
  showThumbnails: true,
  allowFullscreen: true
});

const emit = defineEmits<{
  'image-click': [image: ImageItem, index: number];
  'image-change': [image: ImageItem, index: number];
}>();

// Реактивные данные
const currentIndex = ref(0);
const fullscreenMode = ref(false);
const imageLoading = ref(false);

// Вычисляемые свойства
const currentImage = computed(() => {
  if (props.images.length === 0) return null;
  return props.images[currentIndex.value];
});

// Методы
const setCurrentImage = (index: number) => {
  if (index >= 0 && index < props.images.length) {
    currentIndex.value = index;
    emit('image-change', currentImage.value!, index);
  }
};

const previousImage = () => {
  if (currentIndex.value > 0) {
    setCurrentImage(currentIndex.value - 1);
  }
};

const nextImage = () => {
  if (currentIndex.value < props.images.length - 1) {
    setCurrentImage(currentIndex.value + 1);
  }
};

const openFullscreen = () => {
  if (!props.allowFullscreen || !currentImage.value) return;
  
  fullscreenMode.value = true;
  document.body.style.overflow = 'hidden';
  emit('image-click', currentImage.value, currentIndex.value);
};

const closeFullscreen = () => {
  fullscreenMode.value = false;
  document.body.style.overflow = '';
};

const onImageLoad = () => {
  imageLoading.value = false;
};

const onImageError = (event: Event) => {
  imageLoading.value = false;
  console.error('Error loading image:', event);
};

const onThumbnailError = (event: Event) => {
  console.error('Error loading thumbnail:', event);
};

// Обработка клавиатуры
const handleKeydown = (event: KeyboardEvent) => {
  if (!fullscreenMode.value) return;
  
  switch (event.key) {
    case 'Escape':
      closeFullscreen();
      break;
    case 'ArrowLeft':
      previousImage();
      break;
    case 'ArrowRight':
      nextImage();
      break;
  }
};

// Lifecycle
onMounted(() => {
  // Устанавливаем главное изображение если указано
  if (props.mainImage && props.images.length > 0) {
    const mainIndex = props.images.findIndex(img => img.id === props.mainImage?.id);
    if (mainIndex >= 0) {
      currentIndex.value = mainIndex;
    }
  }
  
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
  document.body.style.overflow = '';
});
</script>

