<template>
  <div class="part-image-gallery">
    <!-- Основное изображение -->
    <div class="main-image-container">
      <div class="main-image-wrapper">
        <img
          v-if="currentImage"
          :src="currentImage.url"
          :alt="currentImage.alt || 'Изображение запчасти'"
          class="main-image"
          @click="openFullscreen"
          @load="onImageLoad"
          @error="onImageError"
        />
        <div v-else class="no-image-placeholder">
          <Icon name="image" size="64" />
          <span>Изображение отсутствует</span>
        </div>
        
        <!-- Индикатор загрузки -->
        <div v-if="imageLoading" class="image-loading">
          <Spinner size="md" />
        </div>
        
        <!-- Кнопки навигации -->
        <div v-if="images.length > 1" class="navigation-buttons">
          <button
            @click="previousImage"
            :disabled="currentIndex === 0"
            class="nav-button nav-button-prev"
          >
            <Icon name="chevron-left" size="24" />
          </button>
          <button
            @click="nextImage"
            :disabled="currentIndex === images.length - 1"
            class="nav-button nav-button-next"
          >
            <Icon name="chevron-right" size="24" />
          </button>
        </div>
        
        <!-- Индикатор количества изображений -->
        <div v-if="images.length > 1" class="image-counter">
          {{ currentIndex + 1 }} / {{ images.length }}
        </div>
        
        <!-- Кнопка полноэкранного режима -->
        <button
          v-if="currentImage"
          @click="openFullscreen"
          class="fullscreen-button"
        >
          <Icon name="maximize" size="20" />
        </button>
      </div>
    </div>
    
    <!-- Миниатюры -->
    <div v-if="images.length > 1 && showThumbnails" class="thumbnails-container">
      <div class="thumbnails-scroll">
        <div
          v-for="(image, index) in images"
          :key="index"
          :class="[
            'thumbnail-wrapper',
            { active: index === currentIndex }
          ]"
          @click="setCurrentImage(index)"
        >
          <img
            :src="image.thumbnailUrl || image.url"
            :alt="image.alt || `Изображение ${index + 1}`"
            class="thumbnail-image"
            @error="onThumbnailError"
          />
        </div>
      </div>
    </div>
    
    <!-- Полноэкранная галерея -->
    <div
      v-if="fullscreenMode"
      class="fullscreen-gallery"
      @click="closeFullscreen"
    >
      <div class="fullscreen-content" @click.stop>
        <div class="fullscreen-header">
          <div class="fullscreen-title">
            {{ currentImage?.alt || `Изображение ${currentIndex + 1}` }}
          </div>
          <button @click="closeFullscreen" class="close-button">
            <Icon name="x" size="24" />
          </button>
        </div>
        
        <div class="fullscreen-image-container">
          <img
            v-if="currentImage"
            :src="currentImage.fullUrl || currentImage.url"
            :alt="currentImage.alt"
            class="fullscreen-image"
          />
          
          <!-- Навигация в полноэкранном режиме -->
          <div v-if="images.length > 1" class="fullscreen-navigation">
            <button
              @click="previousImage"
              :disabled="currentIndex === 0"
              class="fullscreen-nav-button fullscreen-nav-prev"
            >
              <Icon name="chevron-left" size="32" />
            </button>
            <button
              @click="nextImage"
              :disabled="currentIndex === images.length - 1"
              class="fullscreen-nav-button fullscreen-nav-next"
            >
              <Icon name="chevron-right" size="32" />
            </button>
          </div>
        </div>
        
        <!-- Миниатюры в полноэкранном режиме -->
        <div v-if="images.length > 1" class="fullscreen-thumbnails">
          <div
            v-for="(image, index) in images"
            :key="index"
            :class="[
              'fullscreen-thumbnail',
              { active: index === currentIndex }
            ]"
            @click="setCurrentImage(index)"
          >
            <img
              :src="image.thumbnailUrl || image.url"
              :alt="image.alt || `Изображение ${index + 1}`"
              class="fullscreen-thumbnail-image"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import Icon from '@/components/ui/Icon.vue';
import Spinner from '@/components/ui/Spinner.vue';

export interface ImageItem {
  id?: number;
  url: string;
  thumbnailUrl?: string;
  fullUrl?: string;
  alt?: string;
  title?: string;
}

export interface PartImageGalleryProps {
  images: ImageItem[];
  mainImage?: ImageItem;
  showThumbnails?: boolean;
  allowFullscreen?: boolean;
}

const props = withDefaults(defineProps<PartImageGalleryProps>(), {
  showThumbnails: true,
  allowFullscreen: true
});

const emit = defineEmits<{
  'image-click': [image: ImageItem, index: number];
  'image-change': [image: ImageItem, index: number];
}>();

// Реактивные данные
const currentIndex = ref(0);
const fullscreenMode = ref(false);
const imageLoading = ref(false);

// Вычисляемые свойства
const currentImage = computed(() => {
  if (props.images.length === 0) return null;
  return props.images[currentIndex.value];
});

// Методы
const setCurrentImage = (index: number) => {
  if (index >= 0 && index < props.images.length) {
    currentIndex.value = index;
    emit('image-change', currentImage.value!, index);
  }
};

const previousImage = () => {
  if (currentIndex.value > 0) {
    setCurrentImage(currentIndex.value - 1);
  }
};

const nextImage = () => {
  if (currentIndex.value < props.images.length - 1) {
    setCurrentImage(currentIndex.value + 1);
  }
};

const openFullscreen = () => {
  if (!props.allowFullscreen || !currentImage.value) return;
  
  fullscreenMode.value = true;
  document.body.style.overflow = 'hidden';
  emit('image-click', currentImage.value, currentIndex.value);
};

const closeFullscreen = () => {
  fullscreenMode.value = false;
  document.body.style.overflow = '';
};

const onImageLoad = () => {
  imageLoading.value = false;
};

const onImageError = (event: Event) => {
  imageLoading.value = false;
  console.error('Error loading image:', event);
};

const onThumbnailError = (event: Event) => {
  console.error('Error loading thumbnail:', event);
};

// Обработка клавиатуры
const handleKeydown = (event: KeyboardEvent) => {
  if (!fullscreenMode.value) return;
  
  switch (event.key) {
    case 'Escape':
      closeFullscreen();
      break;
    case 'ArrowLeft':
      previousImage();
      break;
    case 'ArrowRight':
      nextImage();
      break;
  }
};

// Lifecycle
onMounted(() => {
  // Устанавливаем главное изображение если указано
  if (props.mainImage && props.images.length > 0) {
    const mainIndex = props.images.findIndex(img => img.id === props.mainImage?.id);
    if (mainIndex >= 0) {
      currentIndex.value = mainIndex;
    }
  }
  
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
  document.body.style.overflow = '';
});
</script>

<style scoped>
.part-image-gallery {
  @apply w-full;
}

.main-image-container {
  @apply relative mb-4;
}

.main-image-wrapper {
  @apply relative bg-gray-100 dark:bg-gray-800;
  @apply rounded-lg overflow-hidden;
  @apply aspect-square;
}

.main-image {
  @apply w-full h-full object-cover cursor-pointer;
  @apply transition-transform duration-200;
}

.main-image:hover {
  @apply scale-105;
}

.no-image-placeholder {
  @apply w-full h-full;
  @apply flex flex-col items-center justify-center;
  @apply text-gray-400 dark:text-gray-500;
}

.no-image-placeholder span {
  @apply mt-2 text-sm;
}

.image-loading {
  @apply absolute inset-0;
  @apply flex items-center justify-center;
  @apply bg-white dark:bg-gray-800 bg-opacity-75;
}

.navigation-buttons {
  @apply absolute inset-y-0 left-0 right-0;
  @apply flex items-center justify-between;
  @apply px-2;
}

.nav-button {
  @apply p-2 rounded-full;
  @apply bg-black bg-opacity-50 text-white;
  @apply hover:bg-opacity-75;
  @apply transition-all duration-200;
  @apply disabled:opacity-30 disabled:cursor-not-allowed;
}

.image-counter {
  @apply absolute bottom-4 right-4;
  @apply px-2 py-1 rounded;
  @apply bg-black bg-opacity-50 text-white text-sm;
}

.fullscreen-button {
  @apply absolute top-4 right-4;
  @apply p-2 rounded-full;
  @apply bg-black bg-opacity-50 text-white;
  @apply hover:bg-opacity-75;
  @apply transition-all duration-200;
}

.thumbnails-container {
  @apply mt-4;
}

.thumbnails-scroll {
  @apply flex space-x-2 overflow-x-auto pb-2;
}

.thumbnail-wrapper {
  @apply flex-shrink-0 cursor-pointer;
  @apply border-2 border-transparent rounded-lg overflow-hidden;
  @apply transition-all duration-200;
}

.thumbnail-wrapper.active {
  @apply border-blue-500;
}

.thumbnail-wrapper:hover {
  @apply border-gray-300 dark:border-gray-600;
}

.thumbnail-image {
  @apply w-16 h-16 object-cover;
}

/* Полноэкранная галерея */
.fullscreen-gallery {
  @apply fixed inset-0 z-50;
  @apply bg-black bg-opacity-90;
  @apply flex items-center justify-center;
}

.fullscreen-content {
  @apply w-full h-full max-w-7xl mx-auto;
  @apply flex flex-col;
}

.fullscreen-header {
  @apply flex justify-between items-center;
  @apply p-4 text-white;
}

.fullscreen-title {
  @apply text-lg font-medium;
}

.close-button {
  @apply p-2 rounded-full;
  @apply hover:bg-white hover:bg-opacity-20;
  @apply transition-colors duration-200;
}

.fullscreen-image-container {
  @apply flex-1 relative;
  @apply flex items-center justify-center;
  @apply p-4;
}

.fullscreen-image {
  @apply max-w-full max-h-full object-contain;
}

.fullscreen-navigation {
  @apply absolute inset-y-0 left-0 right-0;
  @apply flex items-center justify-between;
  @apply px-4;
}

.fullscreen-nav-button {
  @apply p-3 rounded-full;
  @apply bg-black bg-opacity-50 text-white;
  @apply hover:bg-opacity-75;
  @apply transition-all duration-200;
  @apply disabled:opacity-30 disabled:cursor-not-allowed;
}

.fullscreen-thumbnails {
  @apply flex justify-center space-x-2;
  @apply p-4 overflow-x-auto;
}

.fullscreen-thumbnail {
  @apply flex-shrink-0 cursor-pointer;
  @apply border-2 border-transparent rounded overflow-hidden;
  @apply transition-all duration-200;
}

.fullscreen-thumbnail.active {
  @apply border-white;
}

.fullscreen-thumbnail:hover {
  @apply border-gray-400;
}

.fullscreen-thumbnail-image {
  @apply w-12 h-12 object-cover;
}

/* Скроллбар для миниатюр */
.thumbnails-scroll::-webkit-scrollbar,
.fullscreen-thumbnails::-webkit-scrollbar {
  height: 4px;
}

.thumbnails-scroll::-webkit-scrollbar-track,
.fullscreen-thumbnails::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-700;
}

.thumbnails-scroll::-webkit-scrollbar-thumb,
.fullscreen-thumbnails::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600;
  @apply rounded-full;
}

.thumbnails-scroll::-webkit-scrollbar-thumb:hover,
.fullscreen-thumbnails::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}
</style>