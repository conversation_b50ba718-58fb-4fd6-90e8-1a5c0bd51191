<template>
    <div class="search-filters">
        <div class="filters-header">
            <h3 class="filters-title">Фильтры</h3>
            <div class="filters-actions">
                <button @click="resetFilters" class="reset-button" :disabled="!hasActiveFilters">
                    <Icon name="x" size="14" />
                    Сбросить
                </button>
                <button @click="toggleCollapsed" class="collapse-button">
                    <Icon :name="collapsed ? 'chevron-down' : 'chevron-up'" size="14" />
                </button>
            </div>
        </div>

        <div v-show="!collapsed" class="filters-content">
            <!-- Категории -->
            <div class="filter-group">
                <label class="filter-label">Категории</label>
                <div class="filter-options">
                    <div v-for="category in categories" :key="category.id" class="filter-option">
                        <input :id="`category-${category.id}`" v-model="localFilters.categoryIds" :value="category.id"
                            type="checkbox" class="filter-checkbox" />
                        <label :for="`category-${category.id}`" class="filter-option-label">
                            {{ category.name }}
                        </label>
                    </div>
                </div>
            </div>

            <!-- Бренды -->
            <div class="filter-group">
                <label class="filter-label">Бренды</label>
                <div class="filter-search">
                    <input v-model="brandSearch" type="text" placeholder="Поиск брендов..."
                        class="filter-search-input" />
                </div>
                <div class="filter-options scrollable">
                    <div v-for="brand in filteredBrands" :key="brand.id" class="filter-option">
                        <input :id="`brand-${brand.id}`" v-model="localFilters.brandIds" :value="brand.id"
                            type="checkbox" class="filter-checkbox" />
                        <label :for="`brand-${brand.id}`" class="filter-option-label">
                            {{ brand.name }}
                        </label>
                    </div>
                </div>
            </div>

            <!-- Диапазон цен -->
            <div class="filter-group">
                <label class="filter-label">Диапазон цен</label>
                <div class="price-range">
                    <input v-model.number="localFilters.priceRange[0]" type="number" placeholder="От"
                        class="price-input" min="0" />
                    <span class="price-separator">—</span>
                    <input v-model.number="localFilters.priceRange[1]" type="number" placeholder="До"
                        class="price-input" min="0" />
                </div>
            </div>

            <!-- Атрибуты -->
            <div class="filter-group">
                <label class="filter-label">Атрибуты</label>
                <div class="attributes-filters">
                    <div v-for="template in attributeTemplates" :key="template.id" class="attribute-filter">
                        <label class="attribute-label">{{ template.title }}</label>

                        <!-- Строковые атрибуты -->
                        <div v-if="template.dataType === 'STRING'" class="attribute-input">
                            <input v-model="getAttributeFilter(template.id).value" type="text"
                                :placeholder="`Поиск по ${template.title.toLowerCase()}`"
                                class="attribute-text-input" />
                        </div>

                        <!-- Числовые атрибуты -->
                        <div v-else-if="template.dataType === 'NUMBER'" class="attribute-range">
                            <input v-model.number="getAttributeFilter(template.id).minValue" type="number"
                                :placeholder="`Мин. ${template.unit?.symbol || ''}`" class="attribute-number-input"
                                :min="template.minValue" :max="template.maxValue" />
                            <span class="range-separator">—</span>
                            <input v-model.number="getAttributeFilter(template.id).maxValue" type="number"
                                :placeholder="`Макс. ${template.unit?.symbol || ''}`" class="attribute-number-input"
                                :min="template.minValue" :max="template.maxValue" />
                        </div>

                        <!-- Булевы атрибуты -->
                        <div v-else-if="template.dataType === 'BOOLEAN'" class="attribute-boolean">
                            <label class="boolean-option">
                                <input v-model="getAttributeFilter(template.id).value" type="radio" :value="true"
                                    :name="`attr-${template.id}`" class="boolean-radio" />
                                Да
                            </label>
                            <label class="boolean-option">
                                <input v-model="getAttributeFilter(template.id).value" type="radio" :value="false"
                                    :name="`attr-${template.id}`" class="boolean-radio" />
                                Нет
                            </label>
                            <label class="boolean-option">
                                <input v-model="getAttributeFilter(template.id).value" type="radio" :value="null"
                                    :name="`attr-${template.id}`" class="boolean-radio" />
                                Любой
                            </label>
                        </div>

                        <!-- Выбор из списка -->
                        <div v-else-if="template.allowedValues?.length" class="attribute-select">
                            <select v-model="getAttributeFilter(template.id).value" class="attribute-select-input">
                                <option value="">Любое значение</option>
                                <option v-for="value in template.allowedValues" :key="value" :value="value">
                                    {{ value }}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Совместимость с оборудованием -->
            <div class="filter-group">
                <label class="filter-label">Совместимость с оборудованием</label>
                <div class="equipment-search">
                    <input v-model="equipmentSearch" type="text" placeholder="Поиск моделей оборудования..."
                        class="filter-search-input" @input="searchEquipment" />
                </div>
                <div v-if="equipmentSuggestions.length" class="equipment-suggestions">
                    <div v-for="equipment in equipmentSuggestions" :key="equipment.id" class="equipment-suggestion"
                        @click="addEquipmentFilter(equipment)">
                        <span class="equipment-brand">{{ equipment.brand }}</span>
                        <span class="equipment-model">{{ equipment.model }}</span>
                    </div>
                </div>
                <div v-if="localFilters.equipmentModelIds?.length" class="selected-equipment">
                    <div v-for="equipmentId in localFilters.equipmentModelIds" :key="equipmentId"
                        class="selected-equipment-item">
                        <span>{{ getEquipmentName(equipmentId) }}</span>
                        <button @click="removeEquipmentFilter(equipmentId)" class="remove-equipment">
                            <Icon name="x" size="12" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useDebounce } from '@/composables/useDebounce';
import { useTrpc } from '@/composables/useTrpc';
import type { CatalogFilters, AttributeFilter } from '@/utils/filters';
import Icon from '@/components/ui/Icon.vue';

export interface SearchFiltersProps {
    filters: CatalogFilters;
    brands: Array<{ id: number; name: string }>;
    categories: Array<{ id: number; name: string }>;
    attributeTemplates: Array<{
        id: number;
        name: string;
        title: string;
        dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
        unit?: { symbol: string };
        minValue?: number;
        maxValue?: number;
        allowedValues?: string[];
    }>;
    collapsed?: boolean;
}

const props = withDefaults(defineProps<SearchFiltersProps>(), {
    collapsed: false
});

const emit = defineEmits<{
    'update:filters': [filters: CatalogFilters];
}>();

// Реактивные данные
const collapsed = ref(props.collapsed);
const brandSearch = ref('');
const equipmentSearch = ref('');
const equipmentSuggestions = ref<Array<{ id: string; brand: string; model: string }>>([]);
const equipmentCache = ref<Map<string, { brand: string; model: string }>>(new Map());

// Локальные фильтры для двустороннего связывания
const localFilters = ref<CatalogFilters>({
    categoryIds: [],
    brandIds: [],
    priceRange: [null, null],
    attributes: [],
    equipmentModelIds: [],
    ...props.filters
});

// tRPC клиент
const trpc = useTrpc();

// Дебаунс для поиска оборудования
const debouncedEquipmentSearch = useDebounce(() => {
    if (equipmentSearch.value.length >= 2) {
        performEquipmentSearch();
    } else {
        equipmentSuggestions.value = [];
    }
}, 300);

// Вычисляемые свойства
const hasActiveFilters = computed(() => {
    return !!(
        localFilters.value.categoryIds?.length ||
        localFilters.value.brandIds?.length ||
        (localFilters.value.priceRange?.[0] !== null || localFilters.value.priceRange?.[1] !== null) ||
        localFilters.value.attributes?.length ||
        localFilters.value.equipmentModelIds?.length
    );
});

const filteredBrands = computed(() => {
    if (!brandSearch.value) return props.brands;

    const search = brandSearch.value.toLowerCase();
    return props.brands.filter(brand =>
        brand.name.toLowerCase().includes(search)
    );
});

// Методы
const resetFilters = () => {
    localFilters.value = {
        categoryIds: [],
        brandIds: [],
        priceRange: [null, null],
        attributes: [],
        equipmentModelIds: []
    };
    brandSearch.value = '';
    equipmentSearch.value = '';
    equipmentSuggestions.value = [];
    emitFilters();
};

const toggleCollapsed = () => {
    collapsed.value = !collapsed.value;
};

const getAttributeFilter = (templateId: number): AttributeFilter => {
    let filter = localFilters.value.attributes?.find(attr => attr.templateId === templateId);

    if (!filter) {
        filter = {
            templateId,
            value: null,
            minValue: null,
            maxValue: null
        };

        if (!localFilters.value.attributes) {
            localFilters.value.attributes = [];
        }

        localFilters.value.attributes.push(filter);
    }

    return filter;
};

const searchEquipment = () => {
    debouncedEquipmentSearch();
};

const performEquipmentSearch = async () => {
    try {
        const results = await trpc.client.equipmentModels.search.query({
            query: equipmentSearch.value,
            limit: 10
        });

        if (results) {
            equipmentSuggestions.value = results.map(item => ({
                id: item.id,
                brand: item.brand?.name || 'Неизвестный бренд',
                model: item.model || 'Неизвестная модель'
            }));

            // Кэшируем результаты
            results.forEach(item => {
                equipmentCache.value.set(item.id, {
                    brand: item.brand?.name || 'Неизвестный бренд',
                    model: item.model || 'Неизвестная модель'
                });
            });
        }
    } catch (error) {
        console.error('Equipment search error:', error);
        equipmentSuggestions.value = [];
    }
};

const addEquipmentFilter = (equipment: { id: string; brand: string; model: string }) => {
    if (!localFilters.value.equipmentModelIds) {
        localFilters.value.equipmentModelIds = [];
    }

    if (!localFilters.value.equipmentModelIds.includes(equipment.id)) {
        localFilters.value.equipmentModelIds.push(equipment.id);
        equipmentCache.value.set(equipment.id, {
            brand: equipment.brand,
            model: equipment.model
        });
    }

    equipmentSearch.value = '';
    equipmentSuggestions.value = [];
    emitFilters();
};

const removeEquipmentFilter = (equipmentId: string) => {
    if (localFilters.value.equipmentModelIds) {
        const index = localFilters.value.equipmentModelIds.indexOf(equipmentId);
        if (index >= 0) {
            localFilters.value.equipmentModelIds.splice(index, 1);
        }
    }
    emitFilters();
};

const getEquipmentName = (equipmentId: string): string => {
    const cached = equipmentCache.value.get(equipmentId);
    if (cached) {
        return `${cached.brand} ${cached.model}`;
    }
    return equipmentId;
};

const emitFilters = () => {
    // Очищаем пустые атрибуты
    const cleanedFilters = { ...localFilters.value };

    if (cleanedFilters.attributes) {
        cleanedFilters.attributes = cleanedFilters.attributes.filter(attr =>
            attr.value !== null && attr.value !== '' ||
            attr.minValue !== null ||
            attr.maxValue !== null
        );
    }

    // Очищаем пустые массивы
    if (cleanedFilters.categoryIds?.length === 0) {
        delete cleanedFilters.categoryIds;
    }
    if (cleanedFilters.brandIds?.length === 0) {
        delete cleanedFilters.brandIds;
    }
    if (cleanedFilters.equipmentModelIds?.length === 0) {
        delete cleanedFilters.equipmentModelIds;
    }

    // Очищаем пустой диапазон цен
    if (cleanedFilters.priceRange?.[0] === null && cleanedFilters.priceRange?.[1] === null) {
        delete cleanedFilters.priceRange;
    }

    emit('update:filters', cleanedFilters);
};

// Watchers
watch(() => props.filters, (newFilters) => {
    localFilters.value = {
        categoryIds: [],
        brandIds: [],
        priceRange: [null, null],
        attributes: [],
        equipmentModelIds: [],
        ...newFilters
    };
}, { deep: true });

watch(localFilters, () => {
    emitFilters();
}, { deep: true });

// Lifecycle
onMounted(() => {
    // Загружаем кэшированные данные оборудования если есть
    if (localFilters.value.equipmentModelIds?.length) {
        loadEquipmentNames();
    }
});

const loadEquipmentNames = async () => {
    if (!localFilters.value.equipmentModelIds?.length) return;

    try {
        const results = await trpc.client.equipmentModels.findMany.query({
            where: {
                id: { in: localFilters.value.equipmentModelIds }
            }
        });

        if (results) {
            results.forEach(item => {
                equipmentCache.value.set(item.id, {
                    brand: item.brand?.name || 'Неизвестный бренд',
                    model: item.model || 'Неизвестная модель'
                });
            });
        }
    } catch (error) {
        console.error('Error loading equipment names:', error);
    }
};
</script>

<style scoped>
.search-filters {
    @apply bg-white dark:bg-gray-800;
    @apply border border-gray-200 dark:border-gray-700;
    @apply rounded-lg p-4;
}

.filters-header {
    @apply flex justify-between items-center mb-4;
}

.filters-title {
    @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.filters-actions {
    @apply flex space-x-2;
}

.reset-button {
    @apply flex items-center space-x-1 px-3 py-1;
    @apply text-sm text-gray-600 dark:text-gray-400;
    @apply hover:text-gray-800 dark:hover:text-gray-200;
    @apply transition-colors duration-200;
}

.reset-button:disabled {
    @apply opacity-50 cursor-not-allowed;
}

.collapse-button {
    @apply p-1 text-gray-400 hover:text-gray-600;
    @apply dark:text-gray-500 dark:hover:text-gray-300;
    @apply transition-colors duration-200;
}

.filters-content {
    @apply space-y-6;
}

.filter-group {
    @apply space-y-3;
}

.filter-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.filter-options {
    @apply space-y-2;
}

.filter-options.scrollable {
    @apply max-h-40 overflow-y-auto;
}

.filter-option {
    @apply flex items-center;
}

.filter-checkbox {
    @apply mr-2 rounded border-gray-300 dark:border-gray-600;
    @apply text-blue-600 focus:ring-blue-500;
    @apply dark:bg-gray-700 dark:checked:bg-blue-600;
}

.filter-option-label {
    @apply text-sm text-gray-700 dark:text-gray-300 cursor-pointer;
}

.filter-search {
    @apply mb-3;
}

.filter-search-input {
    @apply w-full px-3 py-2 text-sm;
    @apply border border-gray-300 dark:border-gray-600;
    @apply rounded-md;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500;
    @apply dark:bg-gray-700 dark:text-white;
}

.price-range {
    @apply flex items-center space-x-2;
}

.price-input {
    @apply flex-1 px-3 py-2 text-sm;
    @apply border border-gray-300 dark:border-gray-600;
    @apply rounded-md;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500;
    @apply dark:bg-gray-700 dark:text-white;
}

.price-separator {
    @apply text-gray-500 dark:text-gray-400;
}

.attributes-filters {
    @apply space-y-4;
}

.attribute-filter {
    @apply space-y-2;
}

.attribute-label {
    @apply block text-sm font-medium text-gray-600 dark:text-gray-400;
}

.attribute-text-input {
    @apply w-full px-3 py-2 text-sm;
    @apply border border-gray-300 dark:border-gray-600;
    @apply rounded-md;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500;
    @apply dark:bg-gray-700 dark:text-white;
}

.attribute-range {
    @apply flex items-center space-x-2;
}

.attribute-number-input {
    @apply flex-1 px-3 py-2 text-sm;
    @apply border border-gray-300 dark:border-gray-600;
    @apply rounded-md;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500;
    @apply dark:bg-gray-700 dark:text-white;
}

.range-separator {
    @apply text-gray-500 dark:text-gray-400;
}

.attribute-boolean {
    @apply flex space-x-4;
}

.boolean-option {
    @apply flex items-center space-x-1;
    @apply text-sm text-gray-700 dark:text-gray-300;
    @apply cursor-pointer;
}

.boolean-radio {
    @apply text-blue-600 focus:ring-blue-500;
    @apply dark:bg-gray-700 dark:checked:bg-blue-600;
}

.attribute-select-input {
    @apply w-full px-3 py-2 text-sm;
    @apply border border-gray-300 dark:border-gray-600;
    @apply rounded-md;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500;
    @apply dark:bg-gray-700 dark:text-white;
}

.equipment-search {
    @apply mb-3;
}

.equipment-suggestions {
    @apply max-h-40 overflow-y-auto;
    @apply border border-gray-200 dark:border-gray-600;
    @apply rounded-md;
}

.equipment-suggestion {
    @apply px-3 py-2 cursor-pointer;
    @apply hover:bg-gray-50 dark:hover:bg-gray-700;
    @apply border-b border-gray-100 dark:border-gray-600 last:border-b-0;
}

.equipment-brand {
    @apply text-sm font-medium text-gray-900 dark:text-white;
}

.equipment-model {
    @apply text-sm text-gray-600 dark:text-gray-400 ml-2;
}

.selected-equipment {
    @apply flex flex-wrap gap-2 mt-3;
}

.selected-equipment-item {
    @apply flex items-center space-x-2;
    @apply px-3 py-1 bg-blue-100 dark:bg-blue-900;
    @apply text-sm text-blue-800 dark:text-blue-200;
    @apply rounded-full;
}

.remove-equipment {
    @apply text-blue-600 dark:text-blue-400;
    @apply hover:text-blue-800 dark:hover:text-blue-200;
    @apply transition-colors duration-200;
}
</style>