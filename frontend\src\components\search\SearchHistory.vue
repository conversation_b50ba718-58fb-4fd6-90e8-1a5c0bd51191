<template>
  <div class="search-history">
    <div class="history-header">
      <h4 class="history-title">История поиска</h4>
      <button type="button" class="clear-btn" @click="$emit('clear')">Очистить</button>
    </div>
    <ul class="history-list">
      <li v-for="(item, idx) in history" :key="idx" class="history-item" @click="$emit('select', item)">
        <span class="query">{{ item.query }}</span>
        <span class="time">{{ formatTime(item.timestamp) }}</span>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
interface HistoryItem { query: string; filters?: any; timestamp: Date | string }

const props = defineProps<{ history: HistoryItem[] }>()
</script>

<style scoped>
.search-history { display: grid; gap: .5rem; }
.history-header { display: flex; justify-content: space-between; align-items: center; }
.history-title { font-weight: 600; }
.clear-btn { font-size: .875rem; color: #2563eb; }
.history-list { list-style: none; padding: 0; margin: 0; display: grid; gap: .25rem; }
.history-item { display: flex; justify-content: space-between; padding: .5rem .75rem; border: 1px solid #e5e7eb; border-radius: .5rem; cursor: pointer; }
.history-item:hover { background: #f9fafb; }
.query { font-weight: 500; }
.time { font-size: .75rem; opacity: .7; }
</style>

<script lang="ts">
export default {
  emits: ['select', 'clear'],
  methods: {
    formatTime(ts: Date | string) {
      const d = new Date(ts)
      return d.toLocaleString('ru-RU', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit' })
    }
  }
}
</script>
