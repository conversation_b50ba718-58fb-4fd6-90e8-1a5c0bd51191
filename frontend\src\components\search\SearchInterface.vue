<template>
  <div class="search-interface">
    <!-- Основной поисковый интерфейс -->
    <div class="search-header">
      <div class="search-input-container">
        <div class="relative">
          <input
            ref="searchInput"
            v-model="searchQuery"
            type="text"
            :placeholder="placeholder"
            class="search-input"
            @input="onSearchInput"
            @focus="showSuggestions = true"
            @keydown="handleKeydown"
            autocomplete="off"
          />
          <button
            v-if="searchQuery"
            @click="clearSearch"
            class="clear-button"
            type="button"
          >
            <Icon name="x" size="16" />
          </button>
          <button
            @click="toggleMode"
            class="mode-toggle"
            type="button"
            :title="mode === 'simple' ? 'Расширенный поиск' : 'Простой поиск'"
          >
            <Icon :name="mode === 'simple' ? 'sliders' : 'search'" size="16" />
          </button>
        </div>
        
        <!-- Автодополнение -->
        <SearchSuggestions
          v-if="showSuggestions && suggestions.length > 0"
          :suggestions="suggestions"
          :selected-index="selectedSuggestionIndex"
          @select="selectSuggestion"
          @close="showSuggestions = false"
        />
      </div>
      
      <!-- Переключатель режимов -->
      <div class="search-modes">
        <button
          @click="setMode('simple')"
          :class="['mode-button', { active: mode === 'simple' }]"
        >
          Простой поиск
        </button>
        <button
          @click="setMode('advanced')"
          :class="['mode-button', { active: mode === 'advanced' }]"
        >
          Расширенный поиск
        </button>
      </div>
    </div>

    <!-- Расширенные фильтры -->
    <SearchFilters
      v-if="mode === 'advanced'"
      :filters="filters"
      :brands="brands"
      :categories="categories"
      :attribute-templates="attributeTemplates"
      @update:filters="updateFilters"
    />

    <!-- История поиска -->
    <SearchHistory
      v-if="showHistory && searchHistory.length > 0"
      :history="searchHistory"
      @select="selectFromHistory"
      @clear="clearHistory"
    />

    <!-- Результаты поиска -->
    <div v-if="hasResults" class="search-results">
      <div class="results-header">
        <span class="results-count">
          Найдено: {{ totalResults }} {{ pluralize(totalResults, 'результат', 'результата', 'результатов') }}
        </span>
        <div class="results-actions">
          <button
            @click="saveSearch"
            class="save-search-button"
            :disabled="!searchQuery"
          >
            <Icon name="bookmark" size="16" />
            Сохранить поиск
          </button>
        </div>
      </div>
      
      <slot name="results" :results="results" :loading="loading" />
    </div>

    <!-- Состояние загрузки -->
    <div v-if="loading" class="search-loading">
      <Spinner size="md" />
      <span>Поиск...</span>
    </div>

    <!-- Пустое состояние -->
    <EmptyState
      v-if="!loading && !hasResults && (searchQuery || hasActiveFilters)"
      title="Ничего не найдено"
      description="Попробуйте изменить параметры поиска или использовать другие ключевые слова"
    >
      <template #actions>
        <Button @click="clearAll" variant="outline">
          Очистить фильтры
        </Button>
      </template>
    </EmptyState>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { useDebounce } from '@/composables/useDebounce';
import { useTrpc } from '@/composables/useTrpc';
import type { CatalogFilters } from '@/utils/filters';
import SearchSuggestions from './SearchSuggestions.vue';
import SearchFilters from './SearchFilters.vue';
import SearchHistory from './SearchHistory.vue';
import Icon from '@/components/ui/Icon.vue';
import Button from '@/components/ui/Button.vue';
import Spinner from '@/components/ui/Spinner.vue';
import EmptyState from '@/components/ui/EmptyState.vue';

export interface SearchInterfaceProps {
  mode?: 'simple' | 'advanced';
  placeholder?: string;
  showSuggestions?: boolean;
  showHistory?: boolean;
  initialQuery?: string;
  initialFilters?: CatalogFilters;
}

export interface SearchResult {
  id: number;
  type: 'part' | 'catalogItem';
  name: string;
  description?: string;
  brand?: string;
  category?: string;
  imageUrl?: string;
  attributes?: Array<{ name: string; value: string; unit?: string }>;
}

const props = withDefaults(defineProps<SearchInterfaceProps>(), {
  mode: 'simple',
  placeholder: 'Поиск запчастей...',
  showSuggestions: true,
  showHistory: true,
  initialQuery: '',
  initialFilters: () => ({})
});

const emit = defineEmits<{
  search: [query: string, filters: CatalogFilters];
  'update:mode': [mode: 'simple' | 'advanced'];
  'update:query': [query: string];
  'update:filters': [filters: CatalogFilters];
}>();

// Реактивные данные
const searchInput = ref<HTMLInputElement>();
const searchQuery = ref(props.initialQuery);
const mode = ref(props.mode);
const filters = ref<CatalogFilters>({ ...props.initialFilters });
const showSuggestions = ref(false);
const selectedSuggestionIndex = ref(-1);
const loading = ref(false);
const results = ref<SearchResult[]>([]);
const totalResults = ref(0);
const suggestions = ref<string[]>([]);
const searchHistory = ref<Array<{ query: string; filters: CatalogFilters; timestamp: Date }>>([]);

// Данные для фильтров
const brands = ref<Array<{ id: number; name: string }>>([]);
const categories = ref<Array<{ id: number; name: string }>>([]);
const attributeTemplates = ref<Array<any>>([]);

// tRPC клиент
const trpc = useTrpc();

// Дебаунс для поиска
const debouncedSearch = useDebounce(() => {
  performSearch();
}, 300);

// Вычисляемые свойства
const hasResults = computed(() => results.value.length > 0);
const hasActiveFilters = computed(() => {
  return !!(
    filters.value.categoryIds?.length ||
    filters.value.brandIds?.length ||
    filters.value.equipmentModelIds?.length ||
    filters.value.priceRange ||
    filters.value.attributes?.length
  );
});

const showHistory = computed(() => {
  return props.showHistory && !searchQuery.value && !showSuggestions.value;
});

// Методы
const onSearchInput = () => {
  emit('update:query', searchQuery.value);
  
  if (props.showSuggestions) {
    loadSuggestions();
  }
  
  debouncedSearch();
};

const performSearch = async () => {
  if (!searchQuery.value && !hasActiveFilters.value) {
    results.value = [];
    totalResults.value = 0;
    return;
  }

  loading.value = true;
  
  try {
    // Сохраняем поиск в историю
    if (searchQuery.value) {
      saveToHistory();
    }

    // Выполняем поиск через tRPC
    const searchResults = await trpc.client.search.findParts.query({
      query: searchQuery.value,
      filters: filters.value,
      limit: 50
    });

    if (searchResults) {
      results.value = searchResults.items || [];
      totalResults.value = searchResults.total || 0;
    }

    emit('search', searchQuery.value, filters.value);
  } catch (error) {
    console.error('Search error:', error);
    results.value = [];
    totalResults.value = 0;
  } finally {
    loading.value = false;
  }
};

const loadSuggestions = async () => {
  if (!searchQuery.value || searchQuery.value.length < 2) {
    suggestions.value = [];
    return;
  }

  try {
    const suggestionResults = await trpc.client.search.getSuggestions.query({
      query: searchQuery.value,
      limit: 10
    });

    if (suggestionResults) {
      suggestions.value = suggestionResults;
    }
  } catch (error) {
    console.error('Suggestions error:', error);
    suggestions.value = [];
  }
};

const selectSuggestion = (suggestion: string) => {
  searchQuery.value = suggestion;
  showSuggestions.value = false;
  selectedSuggestionIndex.value = -1;
  emit('update:query', searchQuery.value);
  performSearch();
};

const handleKeydown = (event: KeyboardEvent) => {
  if (!showSuggestions.value || suggestions.value.length === 0) return;

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      selectedSuggestionIndex.value = Math.min(
        selectedSuggestionIndex.value + 1,
        suggestions.value.length - 1
      );
      break;
    case 'ArrowUp':
      event.preventDefault();
      selectedSuggestionIndex.value = Math.max(selectedSuggestionIndex.value - 1, -1);
      break;
    case 'Enter':
      event.preventDefault();
      if (selectedSuggestionIndex.value >= 0) {
        selectSuggestion(suggestions.value[selectedSuggestionIndex.value]);
      } else {
        showSuggestions.value = false;
        performSearch();
      }
      break;
    case 'Escape':
      showSuggestions.value = false;
      selectedSuggestionIndex.value = -1;
      break;
  }
};

const clearSearch = () => {
  searchQuery.value = '';
  emit('update:query', '');
  results.value = [];
  totalResults.value = 0;
  suggestions.value = [];
  showSuggestions.value = false;
};

const toggleMode = () => {
  const newMode = mode.value === 'simple' ? 'advanced' : 'simple';
  setMode(newMode);
};

const setMode = (newMode: 'simple' | 'advanced') => {
  mode.value = newMode;
  emit('update:mode', newMode);
};

const updateFilters = (newFilters: CatalogFilters) => {
  filters.value = { ...newFilters };
  emit('update:filters', filters.value);
  debouncedSearch();
};

const clearAll = () => {
  searchQuery.value = '';
  filters.value = {};
  results.value = [];
  totalResults.value = 0;
  emit('update:query', '');
  emit('update:filters', {});
};

const saveSearch = () => {
  if (!searchQuery.value) return;
  
  const searchData = {
    query: searchQuery.value,
    filters: { ...filters.value },
    timestamp: new Date()
  };
  
  // Сохраняем в localStorage
  const saved = JSON.parse(localStorage.getItem('savedSearches') || '[]');
  saved.unshift(searchData);
  
  // Ограничиваем количество сохраненных поисков
  if (saved.length > 20) {
    saved.splice(20);
  }
  
  localStorage.setItem('savedSearches', JSON.stringify(saved));
};

const saveToHistory = () => {
  const historyItem = {
    query: searchQuery.value,
    filters: { ...filters.value },
    timestamp: new Date()
  };
  
  // Удаляем дубликаты
  const existingIndex = searchHistory.value.findIndex(
    item => item.query === historyItem.query
  );
  
  if (existingIndex >= 0) {
    searchHistory.value.splice(existingIndex, 1);
  }
  
  searchHistory.value.unshift(historyItem);
  
  // Ограничиваем историю
  if (searchHistory.value.length > 10) {
    searchHistory.value.splice(10);
  }
  
  // Сохраняем в localStorage
  localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value));
};

const selectFromHistory = (historyItem: { query: string; filters: CatalogFilters }) => {
  searchQuery.value = historyItem.query;
  filters.value = { ...historyItem.filters };
  emit('update:query', searchQuery.value);
  emit('update:filters', filters.value);
  performSearch();
};

const clearHistory = () => {
  searchHistory.value = [];
  localStorage.removeItem('searchHistory');
};

const pluralize = (count: number, one: string, few: string, many: string): string => {
  const mod10 = count % 10;
  const mod100 = count % 100;
  
  if (mod100 >= 11 && mod100 <= 19) {
    return many;
  }
  
  if (mod10 === 1) {
    return one;
  }
  
  if (mod10 >= 2 && mod10 <= 4) {
    return few;
  }
  
  return many;
};

// Загрузка данных для фильтров
const loadFilterData = async () => {
  try {
    const [brandsResult, categoriesResult, templatesResult] = await Promise.all([
      trpc.brands.findMany(),
      trpc.partCategories.findMany(),
      trpc.attributeTemplates.findMany({ limit: 100 })
    ]);

    if (brandsResult) brands.value = brandsResult;
    if (categoriesResult) categories.value = categoriesResult;
    if (templatesResult) attributeTemplates.value = templatesResult;
  } catch (error) {
    console.error('Error loading filter data:', error);
  }
};

// Загрузка истории поиска
const loadSearchHistory = () => {
  try {
    const saved = localStorage.getItem('searchHistory');
    if (saved) {
      searchHistory.value = JSON.parse(saved).map((item: any) => ({
        ...item,
        timestamp: new Date(item.timestamp)
      }));
    }
  } catch (error) {
    console.error('Error loading search history:', error);
  }
};

// Watchers
watch(() => props.mode, (newMode) => {
  mode.value = newMode;
});

watch(() => props.initialQuery, (newQuery) => {
  searchQuery.value = newQuery;
});

watch(() => props.initialFilters, (newFilters) => {
  filters.value = { ...newFilters };
}, { deep: true });

// Lifecycle
onMounted(async () => {
  await loadFilterData();
  loadSearchHistory();
  
  // Выполняем поиск если есть начальные параметры
  if (searchQuery.value || hasActiveFilters.value) {
    await nextTick();
    performSearch();
  }
});

// Закрытие автодополнения при клике вне компонента
onMounted(() => {
  const handleClickOutside = (event: Event) => {
    if (searchInput.value && !searchInput.value.contains(event.target as Node)) {
      showSuggestions.value = false;
    }
  };
  
  document.addEventListener('click', handleClickOutside);
  
  return () => {
    document.removeEventListener('click', handleClickOutside);
  };
});
</script>

<style scoped>
.search-interface {
  @apply w-full;
}

.search-header {
  @apply mb-6;
}

.search-input-container {
  @apply relative mb-4;
}

.search-input {
  @apply w-full px-4 py-3 pr-20 text-base border border-gray-300 rounded-lg;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply dark:bg-gray-800 dark:border-gray-600 dark:text-white;
  @apply placeholder-gray-500 dark:placeholder-gray-400;
}

.clear-button {
  @apply absolute right-12 top-1/2 transform -translate-y-1/2;
  @apply p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300;
  @apply transition-colors duration-200;
}

.mode-toggle {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2;
  @apply p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300;
  @apply transition-colors duration-200;
}

.search-modes {
  @apply flex space-x-2;
}

.mode-button {
  @apply px-4 py-2 text-sm font-medium rounded-md;
  @apply border border-gray-300 dark:border-gray-600;
  @apply text-gray-700 dark:text-gray-300;
  @apply hover:bg-gray-50 dark:hover:bg-gray-700;
  @apply transition-colors duration-200;
}

.mode-button.active {
  @apply bg-blue-500 text-white border-blue-500;
  @apply hover:bg-blue-600;
}

.search-results {
  @apply mt-6;
}

.results-header {
  @apply flex justify-between items-center mb-4;
}

.results-count {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.results-actions {
  @apply flex space-x-2;
}

.save-search-button {
  @apply flex items-center space-x-2 px-3 py-1 text-sm;
  @apply text-gray-600 dark:text-gray-400;
  @apply hover:text-gray-800 dark:hover:text-gray-200;
  @apply transition-colors duration-200;
}

.save-search-button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.search-loading {
  @apply flex items-center justify-center space-x-3 py-8;
  @apply text-gray-600 dark:text-gray-400;
}
</style>