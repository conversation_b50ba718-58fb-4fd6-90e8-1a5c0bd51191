<template>
  <div class="search-suggestions">
    <div class="suggestions-list">
      <div
        v-for="(suggestion, index) in suggestions"
        :key="index"
        :class="[
          'suggestion-item',
          { 'selected': index === selectedIndex }
        ]"
        @click="selectSuggestion(suggestion)"
        @mouseenter="$emit('hover', index)"
      >
        <Icon name="search" size="14" class="suggestion-icon" />
        <span class="suggestion-text" v-html="highlightMatch(suggestion)"></span>
        <button
          @click.stop="removeSuggestion(index)"
          class="remove-suggestion"
          v-if="suggestion.type === 'history'"
        >
          <Icon name="x" size="12" />
        </button>
      </div>
    </div>
    
    <div v-if="showFooter" class="suggestions-footer">
      <div class="footer-hint">
        <Icon name="corner-down-left" size="12" />
        <span>Нажмите Enter для поиска</span>
      </div>
      <div class="footer-hint">
        <Icon name="arrow-up" size="12" />
        <Icon name="arrow-down" size="12" />
        <span>для навигации</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import Icon from '@/components/ui/Icon.vue';

export interface SuggestionItem {
  text: string;
  type: 'search' | 'history' | 'synonym';
  category?: string;
  count?: number;
}

export interface SearchSuggestionsProps {
  suggestions: (string | SuggestionItem)[];
  selectedIndex?: number;
  showFooter?: boolean;
  maxItems?: number;
  highlightQuery?: string;
}

const props = withDefaults(defineProps<SearchSuggestionsProps>(), {
  selectedIndex: -1,
  showFooter: true,
  maxItems: 10,
  highlightQuery: ''
});

const emit = defineEmits<{
  select: [suggestion: string | SuggestionItem];
  hover: [index: number];
  remove: [index: number];
  close: [];
}>();

// Нормализуем предложения к единому формату
const normalizedSuggestions = computed(() => {
  return props.suggestions.slice(0, props.maxItems).map(suggestion => {
    if (typeof suggestion === 'string') {
      return {
        text: suggestion,
        type: 'search' as const
      };
    }
    return suggestion;
  });
});

// Методы
const selectSuggestion = (suggestion: SuggestionItem) => {
  emit('select', suggestion);
};

const removeSuggestion = (index: number) => {
  emit('remove', index);
};

const highlightMatch = (suggestion: SuggestionItem): string => {
  if (!props.highlightQuery || !suggestion.text) {
    return suggestion.text;
  }
  
  const query = props.highlightQuery.toLowerCase();
  const text = suggestion.text;
  const lowerText = text.toLowerCase();
  
  const index = lowerText.indexOf(query);
  if (index === -1) {
    return text;
  }
  
  const before = text.substring(0, index);
  const match = text.substring(index, index + query.length);
  const after = text.substring(index + query.length);
  
  return `${before}<mark class="highlight">${match}</mark>${after}`;
};

const getSuggestionIcon = (suggestion: SuggestionItem): string => {
  switch (suggestion.type) {
    case 'history':
      return 'clock';
    case 'synonym':
      return 'link';
    default:
      return 'search';
  }
};

const getSuggestionLabel = (suggestion: SuggestionItem): string => {
  switch (suggestion.type) {
    case 'history':
      return 'Из истории';
    case 'synonym':
      return 'Синоним';
    default:
      return '';
  }
};
</script>

<style scoped>
.search-suggestions {
  @apply absolute top-full left-0 right-0 z-50;
  @apply bg-white dark:bg-gray-800;
  @apply border border-gray-200 dark:border-gray-600;
  @apply rounded-lg shadow-lg;
  @apply mt-1;
  max-height: 400px;
  overflow-y: auto;
}

.suggestions-list {
  @apply py-2;
}

.suggestion-item {
  @apply flex items-center px-4 py-2;
  @apply text-sm text-gray-700 dark:text-gray-300;
  @apply hover:bg-gray-50 dark:hover:bg-gray-700;
  @apply cursor-pointer;
  @apply transition-colors duration-150;
}

.suggestion-item.selected {
  @apply bg-blue-50 dark:bg-blue-900/20;
  @apply text-blue-700 dark:text-blue-300;
}

.suggestion-icon {
  @apply mr-3 text-gray-400 dark:text-gray-500;
  @apply flex-shrink-0;
}

.suggestion-item.selected .suggestion-icon {
  @apply text-blue-500 dark:text-blue-400;
}

.suggestion-text {
  @apply flex-1 truncate;
}

.suggestion-text :deep(.highlight) {
  @apply bg-yellow-200 dark:bg-yellow-800;
  @apply text-gray-900 dark:text-gray-100;
  @apply font-medium;
  @apply px-0.5 rounded;
}

.suggestion-category {
  @apply ml-2 px-2 py-0.5;
  @apply text-xs text-gray-500 dark:text-gray-400;
  @apply bg-gray-100 dark:bg-gray-700;
  @apply rounded;
}

.suggestion-count {
  @apply ml-2 text-xs text-gray-400 dark:text-gray-500;
}

.remove-suggestion {
  @apply ml-2 p-1;
  @apply text-gray-400 hover:text-gray-600;
  @apply dark:text-gray-500 dark:hover:text-gray-300;
  @apply rounded;
  @apply transition-colors duration-150;
  @apply flex-shrink-0;
}

.remove-suggestion:hover {
  @apply bg-gray-100 dark:bg-gray-600;
}

.suggestions-footer {
  @apply flex justify-between items-center;
  @apply px-4 py-2;
  @apply border-t border-gray-200 dark:border-gray-600;
  @apply bg-gray-50 dark:bg-gray-750;
}

.footer-hint {
  @apply flex items-center space-x-1;
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.footer-hint svg {
  @apply text-gray-400 dark:text-gray-500;
}

/* Анимации */
.search-suggestions {
  animation: slideDown 0.15s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Скроллбар */
.search-suggestions::-webkit-scrollbar {
  width: 6px;
}

.search-suggestions::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-700;
}

.search-suggestions::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600;
  @apply rounded-full;
}

.search-suggestions::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}
</style>