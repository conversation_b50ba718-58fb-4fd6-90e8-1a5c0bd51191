<template>
  <span :class="badgeClasses">
    <slot />
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info'
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md'
})

const badgeClasses = computed(() => {
  const base = 'inline-flex items-center font-medium rounded-full border-0'
  
  const variants = {
    default: 'bg-[--p-content-hover-background] text-[--color-foreground]',
    success: 'bg-[color-mix(in_srgb,var(--p-success-color),transparent_80%)] text-[--p-success-color] border border-[color-mix(in_srgb,var(--p-success-color),transparent_70%)]',
    warning: 'bg-[color-mix(in_srgb,var(--p-warning-color),transparent_80%)] text-[--p-warning-color] border border-[color-mix(in_srgb,var(--p-warning-color),transparent_70%)]',
    error: 'bg-[color-mix(in_srgb,var(--p-danger-color),transparent_80%)] text-[--p-danger-color] border border-[color-mix(in_srgb,var(--p-danger-color),transparent_70%)]',
    info: 'bg-[color-mix(in_srgb,var(--p-primary-color),transparent_80%)] text-[--p-primary-color] border border-[color-mix(in_srgb,var(--p-primary-color),transparent_70%)]'
  }
  
  const sizes = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base'
  }
  
  return `${base} ${variants[props.variant]} ${sizes[props.size]}`
})
</script>