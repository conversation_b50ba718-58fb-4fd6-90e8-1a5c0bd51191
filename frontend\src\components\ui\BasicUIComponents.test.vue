<template>
  <div class="p-8 space-y-8">
    <h1 class="text-2xl font-bold">Basic UI Components Test</h1>
    
    <!-- LoadingSpinner Tests -->
    <section>
      <h2 class="text-xl font-semibold mb-4">LoadingSpinner</h2>
      <div class="flex items-center gap-4 mb-4">
        <Spinner size="xs" />
        <Spinner size="sm" />
        <Spinner size="md" />
        <Spinner size="lg" />
        <Spinner size="xl" />
      </div>
      <div class="flex items-center gap-4 mb-4">
        <Spinner variant="primary" label="Загрузка..." />
        <Spinner variant="success" label="Успешно" />
        <Spinner variant="warning" label="Внимание" />
        <Spinner variant="danger" label="Ошибка" />
      </div>
      <Spinner centered label="Центрированный спиннер" />
    </section>
    
    <!-- ErrorBoundary Tests -->
    <section>
      <h2 class="text-xl font-semibold mb-4">ErrorBoundary</h2>
      <div class="space-y-4">
        <ErrorBoundary variant="default">
          <div>Нормальный контент без ошибок</div>
        </ErrorBoundary>
        
        <ErrorBoundary 
          variant="minimal"
          title="Минимальная ошибка"
          message="Что-то пошло не так"
          :show-actions="false"
        >
          <div>Контент с ошибкой</div>
        </ErrorBoundary>
      </div>
    </section>
    
    <!-- EmptyState Tests -->
    <section>
      <h2 class="text-xl font-semibold mb-4">EmptyState</h2>
      <div class="space-y-4">
        <EmptyState
          title="Нет результатов поиска"
          description="Попробуйте изменить критерии поиска или очистить фильтры"
          icon="search"
          :primary-action="{ label: 'Очистить фильтры' }"
          :secondary-action="{ label: 'Новый поиск' }"
          @primary-action="console.log('Primary action')"
          @secondary-action="console.log('Secondary action')"
        />
        
        <EmptyState
          title="Каталог пуст"
          description="Пока что здесь нет запчастей"
          icon="package"
          variant="compact"
          :primary-action="{ label: 'Добавить запчасть' }"
        />
      </div>
    </section>
    
    <!-- ConfirmationDialog Tests -->
    <section>
      <h2 class="text-xl font-semibold mb-4">ConfirmationDialog</h2>
      <div class="flex gap-4">
        <button 
          @click="showDefaultDialog = true"
          class="px-4 py-2 bg-blue-600 text-white rounded"
        >
          Обычный диалог
        </button>
        
        <button 
          @click="showDangerDialog = true"
          class="px-4 py-2 bg-red-600 text-white rounded"
        >
          Диалог удаления
        </button>
        
        <button 
          @click="showWarningDialog = true"
          class="px-4 py-2 bg-yellow-600 text-white rounded"
        >
          Диалог предупреждения
        </button>
      </div>
      
      <!-- Dialogs -->
      <ConfirmationDialog
        :is-open="showDefaultDialog"
        title="Подтвердите действие"
        description="Вы уверены, что хотите выполнить это действие?"
        @confirm="handleConfirm('default')"
        @close="showDefaultDialog = false"
      />
      
      <ConfirmationDialog
        :is-open="showDangerDialog"
        title="Удалить запчасть"
        description="Это действие нельзя отменить. Запчасть будет удалена навсегда."
        variant="danger"
        confirm-label="Удалить"
        @confirm="handleConfirm('danger')"
        @close="showDangerDialog = false"
      />
      
      <ConfirmationDialog
        :is-open="showWarningDialog"
        title="Несохраненные изменения"
        description="У вас есть несохраненные изменения. Вы действительно хотите покинуть страницу?"
        variant="warning"
        confirm-label="Покинуть"
        cancel-label="Остаться"
        @confirm="handleConfirm('warning')"
        @close="showWarningDialog = false"
      />
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Spinner, ErrorBoundary, EmptyState, ConfirmationDialog } from './index'

const showDefaultDialog = ref(false)
const showDangerDialog = ref(false)
const showWarningDialog = ref(false)

const handleConfirm = (type: string) => {
  console.log(`Confirmed: ${type}`)
  showDefaultDialog.value = false
  showDangerDialog.value = false
  showWarningDialog.value = false
}
</script>