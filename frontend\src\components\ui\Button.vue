<template>
  <Motion
    :whileHover="{ scale: 1.05 }"
    :whileTap="{ scale: 0.95 }"
    :transition="{ type: 'spring', stiffness: 400, damping: 17 }"
  >
    <button
      :class="buttonClasses"
      :disabled="disabled"
      v-bind="$attrs"
      @click="$emit('click', $event)"
    >
      <span v-if="loading" class="mr-2">
        <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </span>
      <slot />
    </button>
  </Motion>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Motion } from 'motion-v'

interface Props {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false
})

defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => {
  const base = 'inline-flex items-center justify-center font-medium transition-all duration-300 rounded-[--radius-md] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[--color-ring] focus-visible:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'

  const variants = {
    primary:
      'bg-[--color-primary] hover:bg-[--color-primary-hover] active:bg-[--color-primary-active] text-[--color-primary-foreground] [box-shadow:var(--shadow-md)] hover:[box-shadow:var(--shadow-lg)]',
    secondary:
      'bg-[--color-card] text-[--color-foreground] border border-[--color-border] hover:bg-[--p-content-hover-background]',
    outline:
      'bg-transparent border border-[--color-border] text-[--color-foreground] hover:bg-[--p-content-hover-background]',
    ghost:
      'bg-transparent text-[--color-foreground] hover:bg-[--p-content-hover-background]'
  }

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  }

  return `${base} ${variants[props.variant]} ${sizes[props.size]}`
})
</script>