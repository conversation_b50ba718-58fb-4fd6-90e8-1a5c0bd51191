<template>
  <Motion
    :whileHover="{ y: -5, scale: 1.02 }"
    :transition="{ type: 'spring', stiffness: 300, damping: 20 }"
    :class="cardClasses"
  >
    <div v-if="$slots.header" class="p-6 border-b border-[--color-border]">
      <slot name="header" />
    </div>
    
    <div class="p-6 text-[--color-foreground]">
      <slot />
    </div>
    
    <div v-if="$slots.footer" class="p-6 border-t border-[--color-border]">
      <slot name="footer" />
    </div>
  </Motion>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Motion } from 'motion-v'

interface Props {
  variant?: 'default' | 'elevated' | 'outlined'
  hover?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  hover: true
})

const cardClasses = computed(() => {
  const base = 'bg-[--color-card] border border-[--color-border] rounded-[--radius-lg] overflow-hidden transition-all duration-300 [box-shadow:var(--shadow-md)]'

  const variants = {
    default: '',
    elevated: '[box-shadow:var(--shadow-lg)]',
    outlined: 'border-2'
  }

  const hoverEffect = props.hover ? 'hover:bg-[--p-content-hover-background] hover:[box-shadow:var(--shadow-xl)]' : ''

  return `${base} ${variants[props.variant]} ${hoverEffect}`
})
</script>