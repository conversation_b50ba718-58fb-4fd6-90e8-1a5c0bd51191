<template>
  <Teleport to="body">
    <Motion
      v-if="isOpen"
      :initial="{ opacity: 0 }"
      :animate="{ opacity: 1 }"
      :exit="{ opacity: 0 }"
      :transition="{ duration: 0.3 }"
      class="fixed inset-0 z-50 flex items-center justify-center p-4"
      @click="closeOnBackdrop && close()"
    >
      <!-- Backdrop -->
      <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" />
      
      <!-- Modal -->
      <Motion
        :initial="{ opacity: 0, scale: 0.8, y: 20 }"
        :animate="{ opacity: 1, scale: 1, y: 0 }"
        :exit="{ opacity: 0, scale: 0.8, y: 20 }"
        :transition="{ duration: 0.3, type: 'spring', stiffness: 300, damping: 30 }"
        :class="modalClasses"
        @click.stop
      >
        <!-- Header -->
        <div v-if="$slots.header || title" class="flex items-center justify-between p-6 border-b border-[--color-border]">
          <div>
            <slot name="header">
              <h2 class="text-xl font-semibold text-[--color-foreground]">{{ title }}</h2>
            </slot>
          </div>
          <button
            v-if="showClose"
            @click="close"
            class="text-[--color-muted] hover:text-[--color-foreground] transition-colors"
          >
            <X class="w-5 h-5" />
          </button>
        </div>
        
        <!-- Content -->
        <div class="p-6 text-[--color-foreground]">
          <slot />
        </div>
        
        <!-- Footer -->
        <div v-if="$slots.footer" class="flex items-center justify-end gap-3 p-6 border-t border-[--color-border]">
          <slot name="footer" />
        </div>
      </Motion>
    </Motion>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Motion } from 'motion-v'
import { X } from 'lucide-vue-next'

interface Props {
  isOpen: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  closeOnBackdrop?: boolean
  showClose?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closeOnBackdrop: true,
  showClose: true
})

const emit = defineEmits<{
  close: []
}>()

const modalClasses = computed(() => {
  const base = 'relative bg-zinc-900 backdrop-blur-xl border border-zinc-700 rounded-2xl shadow-2xl max-h-[90vh] overflow-auto'
  
  const sizes = {
    sm: 'max-w-md w-full',
    md: 'max-w-lg w-full',
    lg: 'max-w-2xl w-full',
    xl: 'max-w-4xl w-full'
  }
  
  return `${base} ${sizes[props.size]}`
})

const close = () => {
  emit('close')
}
</script>