# Basic UI Components

This directory contains the basic UI components implemented for the PartTec3 client interface. These components follow the design system and provide consistent user experience across the application.

## Components

### LoadingSpinner

Enhanced loading spinner component with multiple sizes, variants, and optional labels.

**Props:**
- `size`: 'xs' | 'sm' | 'md' | 'lg' | 'xl' (default: 'md')
- `variant`: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' (default: 'default')
- `label`: string (optional) - Text to display below the spinner
- `centered`: boolean (default: false) - Whether to center the spinner

**Usage:**
```vue
<template>
  <!-- Basic spinner -->
  <Spinner />
  
  <!-- Large primary spinner with label -->
  <Spinner size="lg" variant="primary" label="Загрузка данных..." />
  
  <!-- Centered spinner -->
  <Spinner centered label="Обработка запроса..." />
</template>
```

### ErrorBoundary

Component for catching and displaying errors from child components with recovery options.

**Props:**
- `title`: string (optional) - Custom error title
- `message`: string (optional) - Custom error message
- `variant`: 'default' | 'minimal' | 'detailed' (default: 'default')
- `showActions`: boolean (default: true) - Whether to show action buttons
- `showReload`: boolean (default: true) - Whether to show reload button
- `showDetails`: boolean (default: DEV mode) - Whether to show error details
- `onRetry`: function (optional) - Custom retry handler

**Events:**
- `error`: Emitted when an error is caught
- `retry`: Emitted when retry button is clicked

**Usage:**
```vue
<template>
  <!-- Wrap components that might error -->
  <ErrorBoundary @error="handleError" @retry="handleRetry">
    <SomeComponentThatMightError />
  </ErrorBoundary>
  
  <!-- Minimal error display -->
  <ErrorBoundary variant="minimal" :show-actions="false">
    <AnotherComponent />
  </ErrorBoundary>
</template>
```

### EmptyState

Component for displaying empty states with customizable icons, messages, and actions.

**Props:**
- `title`: string (required) - Main title text
- `description`: string (optional) - Description text
- `icon`: 'search' | 'package' | 'file' | 'database' | 'inbox' | 'alert' | 'settings' | 'users' | 'cart' | 'filter' | 'custom' (default: 'inbox')
- `variant`: 'default' | 'compact' | 'detailed' (default: 'default')
- `primaryAction`: ActionButton (optional) - Primary action button
- `secondaryAction`: ActionButton (optional) - Secondary action button

**Events:**
- `primaryAction`: Emitted when primary action is clicked
- `secondaryAction`: Emitted when secondary action is clicked

**Slots:**
- `icon`: Custom icon content
- `title`: Custom title content
- `description`: Custom description content
- `actions`: Custom actions content

**Usage:**
```vue
<template>
  <!-- Basic empty state -->
  <EmptyState
    title="Нет результатов поиска"
    description="Попробуйте изменить критерии поиска"
    icon="search"
    :primary-action="{ label: 'Очистить фильтры' }"
    @primary-action="clearFilters"
  />
  
  <!-- Custom icon and actions -->
  <EmptyState title="Каталог пуст" variant="detailed">
    <template #icon>
      <CustomIcon />
    </template>
    <template #actions>
      <Button @click="addItem">Добавить запчасть</Button>
    </template>
  </EmptyState>
</template>
```

### ConfirmationDialog

Modal dialog for user confirmations with different variants and customizable actions.

**Props:**
- `isOpen`: boolean (required) - Whether dialog is open
- `title`: string (required) - Dialog title
- `description`: string (optional) - Dialog description
- `variant`: 'default' | 'danger' | 'warning' | 'success' | 'info' (default: 'default')
- `confirmLabel`: string (default: 'Подтвердить') - Confirm button text
- `cancelLabel`: string (default: 'Отмена') - Cancel button text
- `confirmIcon`: Component (optional) - Icon for confirm button
- `cancelIcon`: Component (optional) - Icon for cancel button
- `showCancel`: boolean (default: true) - Whether to show cancel button
- `showCloseButton`: boolean (default: true) - Whether to show close button
- `closeOnBackdrop`: boolean (default: true) - Whether clicking backdrop closes dialog
- `loading`: boolean (default: false) - Whether to show loading state
- `closeLabel`: string (default: 'Закрыть диалог') - Aria label for close button

**Events:**
- `confirm`: Emitted when confirm button is clicked
- `cancel`: Emitted when cancel button is clicked
- `close`: Emitted when dialog should be closed

**Slots:**
- `default`: Custom content in dialog body

**Usage:**
```vue
<template>
  <!-- Basic confirmation -->
  <ConfirmationDialog
    :is-open="showDialog"
    title="Подтвердите действие"
    description="Вы уверены, что хотите выполнить это действие?"
    @confirm="handleConfirm"
    @close="showDialog = false"
  />
  
  <!-- Danger confirmation -->
  <ConfirmationDialog
    :is-open="showDeleteDialog"
    title="Удалить запчасть"
    description="Это действие нельзя отменить."
    variant="danger"
    confirm-label="Удалить"
    :loading="isDeleting"
    @confirm="deleteItem"
    @close="showDeleteDialog = false"
  />
  
  <!-- Custom content -->
  <ConfirmationDialog
    :is-open="showCustomDialog"
    title="Дополнительная информация"
    @confirm="handleConfirm"
    @close="showCustomDialog = false"
  >
    <div class="space-y-2">
      <p>Дополнительный контент...</p>
      <ul class="list-disc list-inside">
        <li>Пункт 1</li>
        <li>Пункт 2</li>
      </ul>
    </div>
  </ConfirmationDialog>
</template>
```

## Composables

### useErrorBoundary

Composable for managing error boundary state and providing error handling utilities.

**Usage:**
```typescript
import { useErrorBoundary } from '@/composables/useErrorBoundary'

const { 
  state, 
  captureError, 
  retry, 
  reset, 
  shouldShowRetry, 
  getErrorMessage, 
  getErrorTitle 
} = useErrorBoundary()

// Capture an error
try {
  // Some operation that might fail
} catch (error) {
  captureError(error)
}

// Check if retry should be shown
if (shouldShowRetry()) {
  // Show retry button
}

// Get user-friendly error message
const message = getErrorMessage(error)
const title = getErrorTitle(error)
```

## Design System Integration

All components follow the established design system:

- Use CSS custom properties for theming (`--color-primary`, `--color-foreground`, etc.)
- Support both light and dark themes automatically
- Follow accessibility guidelines (WCAG 2.1 AA)
- Use consistent spacing, typography, and interaction patterns
- Support keyboard navigation and screen readers
- Include proper ARIA attributes and semantic HTML

## Requirements Fulfilled

These components fulfill the following requirements from the specification:

- **Requirement 6.4**: Error handling and loading states
- **Requirement 8.4**: Performance optimization with proper loading indicators

The components provide:
- Consistent loading states across the application
- Robust error handling with recovery options
- User-friendly empty states with actionable guidance
- Accessible confirmation dialogs with proper focus management
- Full theme support and responsive design
- TypeScript support with proper type definitions