<template>
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <Motion
      :animate="{
        x: [0, 100, 0],
        y: [0, 50, 0],
      }"
      :transition="{
        duration: 20,
        repeat: Infinity,
        ease: 'linear'
      }"
      class="absolute"
      :style="{
        left: `${x}%`,
        top: `${y}%`,
        transform: 'translate(-50%, -50%)'
      }"
    >
      <div
        class="spotlight"
        :style="{
          width: `${size}px`,
          height: `${size}px`,
          background: `radial-gradient(circle, ${color}40 0%, ${color}20 25%, transparent 70%)`,
          filter: 'blur(40px)'
        }"
      />
    </Motion>
  </div>
</template>

<script setup lang="ts">
import { Motion } from 'motion-v'

interface Props {
  x?: number
  y?: number
  size?: number
  color?: string
}

const props = withDefaults(defineProps<Props>(), {
  x: 50,
  y: 50,
  size: 600,
  color: 'rgba(59, 130, 246'
})
</script>

<style scoped>
.spotlight {
  border-radius: 50%;
  animation: spotlight-pulse 4s ease-in-out infinite;
}

@keyframes spotlight-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}
</style>