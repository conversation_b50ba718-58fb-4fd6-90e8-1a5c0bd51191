<template>
  <div class="w-full">
    <!-- Tab Headers -->
    <div class="flex space-x-1 bg-[--color-card] p-1 rounded-[--radius-md] mb-6 border border-[--color-border]">
      <Motion
        v-for="(tab, index) in tabs"
        :key="tab.id"
        :whileHover="{ scale: 1.02 }"
        :whileTap="{ scale: 0.98 }"
        @click="activeTab = tab.id"
        :class="`relative flex-1 px-4 py-2 text-sm font-medium rounded-[--radius-sm] cursor-pointer transition-all duration-200 ${
          activeTab === tab.id
            ? 'text-[--color-foreground]'
            : 'text-[--color-muted] hover:text-[--color-foreground]'
        }`"
      >
        {{ tab.label }}
        
        <Motion
          v-if="activeTab === tab.id"
          layoutId="activeTab"
          class="absolute inset-0 bg-[--p-content-hover-background] border border-[--color-border] rounded-[--radius-sm] -z-10"
          :transition="{ type: 'spring', stiffness: 300, damping: 30 }"
        />
      </Motion>
    </div>
    
    <!-- Tab Content -->
    <div class="relative">
      <Motion
        v-for="tab in tabs"
        :key="tab.id"
        v-show="activeTab === tab.id"
        :initial="{ opacity: 0, y: 20 }"
        :animate="{ opacity: 1, y: 0 }"
        :exit="{ opacity: 0, y: -20 }"
        :transition="{ duration: 0.3 }"
      >
        <slot :name="tab.id" />
      </Motion>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Motion } from 'motion-v'

interface Tab {
  id: string
  label: string
}

interface Props {
  tabs: Tab[]
  defaultTab?: string
}

const props = defineProps<Props>()

const activeTab = ref(props.defaultTab || props.tabs[0]?.id)
</script>