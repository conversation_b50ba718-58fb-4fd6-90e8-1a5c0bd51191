<template>
  <div class="theme-toggle">
    <!-- Простая кнопка переключения -->
      <button
      v-if="mode === 'toggle'"
      @click="toggleTheme"
      :class="buttonClass"
      class="p-2 rounded-[--radius-md] text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors"
      :title="`Переключить тему (текущая: ${themeName})`"
    >
      <component :is="themeIconComponent" :size="20" />
      <span v-if="showLabel" class="ml-2">{{ themeName }}</span>
    </button>

    <!-- Выпадающее меню с выбором темы -->
    <div v-else-if="mode === 'menu'" class="relative">
      <button
        @click="toggleMenu"
        :class="buttonClass"
        class="p-2 rounded-[--radius-md] text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors flex items-center"
        :title="`Выбрать тему (текущая: ${themeName})`"
      >
        <component :is="themeIconComponent" :size="20" />
        <span v-if="showLabel" class="ml-2">{{ themeName }}</span>
      </button>
      
      <!-- Выпадающее меню -->
      <div
        v-if="showMenu"
        class="absolute right-0 mt-2 w-48 bg-[--color-card] rounded-[--radius-md] [box-shadow:var(--shadow-lg)] border border-[--color-border] z-50"
        @click.stop
      >
        <div class="py-1">
          <button
            v-for="theme in themes"
            :key="theme.value"
            @click="selectTheme(theme.value)"
            class="flex items-center w-full px-4 py-2 text-sm text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors"
            :class="{
              'bg-[--p-highlight-background] text-[--p-primary-color]': currentTheme === theme.value
            }"
          >
            <component :is="theme.icon" :size="16" class="mr-3" />
            <span>{{ theme.label }}</span>
            <Check
              v-if="currentTheme === theme.value"
              :size="16"
              class="ml-auto text-[--p-primary-color]"
            />
          </button>
        </div>
      </div>
    </div>

    <!-- Группа кнопок -->
    <div v-else-if="mode === 'buttons'" class="flex rounded-[--radius-md] border border-[--color-border] overflow-hidden">
      <button
        v-for="theme in themes"
        :key="theme.value"
        @click="selectTheme(theme.value)"
        class="flex items-center px-3 py-2 text-sm transition-colors border-r border-[--color-border] last:border-r-0"
        :class="{
          'bg-[--color-primary] text-[--color-primary-foreground]': currentTheme === theme.value,
          'bg-[--color-card] text-[--color-foreground] hover:bg-[--p-content-hover-background]': currentTheme !== theme.value
        }"
        :title="`Выбрать ${theme.label.toLowerCase()} тему`"
      >
        <component :is="theme.icon" :size="16" />
        <span v-if="showLabel" class="ml-2">{{ theme.label }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTheme, type ThemeType } from '@/composables/useTheme'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import { Sun, Moon, Monitor, Check } from 'lucide-vue-next'

interface Props {
  mode?: 'toggle' | 'menu' | 'buttons'
  showLabel?: boolean
  buttonClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'toggle',
  showLabel: false,
  buttonClass: ''
})

// Используем composable темы
const {
  currentTheme,
  activeTheme,
  isDark,
  isLight,
  isSystem,
  themeIcon,
  themeName,
  systemPrefersDark,
  setTheme,
  toggleTheme
} = useTheme()

// Локальное состояние для меню
const showMenu = ref(false)

// Компонент иконки для текущей темы - ИСПРАВЛЕНО
const themeIconComponent = computed(() => {
  switch (currentTheme.value) {
    case 'light':
      return Moon // Показываем луну, так как следующая тема будет темная
    case 'dark':
      return Sun // Показываем солнце, так как следующая тема будет светлая
    case 'system':
      return systemPrefersDark.value ? Sun : Moon // Показываем противоположную системной
    default:
      return Monitor
  }
})

// Список доступных тем с Lucide иконками
const themes = [
  { value: 'light' as ThemeType, label: 'Светлая', icon: Sun },
  { value: 'dark' as ThemeType, label: 'Темная', icon: Moon },
  { value: 'system' as ThemeType, label: 'Системная', icon: Monitor }
]

// Методы
const toggleMenu = () => {
  showMenu.value = !showMenu.value
}

const selectTheme = (theme: ThemeType) => {
  setTheme(theme)
  showMenu.value = false
}

// Закрытие меню при клике вне его
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.theme-toggle')) {
    showMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.theme-toggle {
  position: relative;
}

/* Анимация для плавного появления меню */
.theme-toggle .absolute {
  animation: fadeIn 0.15s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
