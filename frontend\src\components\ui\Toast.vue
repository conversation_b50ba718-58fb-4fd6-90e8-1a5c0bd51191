<template>
  <Teleport to="body">
    <div class="fixed top-4 right-4 z-50 space-y-2">
      <Motion
        v-for="toast in toasts"
        :key="toast.id"
        :initial="{ opacity: 0, x: 100, scale: 0.8 }"
        :animate="{ opacity: 1, x: 0, scale: 1 }"
        :exit="{ opacity: 0, x: 100, scale: 0.8 }"
        :transition="{ duration: 0.3 }"
        :class="toastClasses(toast.type)"
      >
        <div class="flex items-center gap-3">
          <component :is="getIcon(toast.type)" class="w-5 h-5 flex-shrink-0" />
          <div class="flex-1">
            <div v-if="toast.title" class="font-medium text-[--color-foreground]">{{ toast.title }}</div>
            <div class="text-sm text-[--color-muted]">{{ toast.message }}</div>
          </div>
          <button @click="removeToast(toast.id)" class="text-[--color-muted] hover:text-[--color-foreground]">
            <X class="w-4 h-4" />
          </button>
        </div>
      </Motion>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Motion } from 'motion-v'
import { CheckCircle, AlertTriangle, XCircle, Info, X } from 'lucide-vue-next'

interface Toast {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
}

const toasts = ref<Toast[]>([])

const toastClasses = (type: Toast['type']) => {
  const base = 'bg-[--color-card] text-[--color-foreground] backdrop-blur-xl border border-[--color-border] rounded-[--radius-md] p-4 [box-shadow:var(--shadow-lg)] max-w-sm'
  
  const variants = {
    success: 'text-[--p-success-color] border-[color-mix(in_srgb,var(--p-success-color),transparent_60%)]',
    error: 'text-[--p-danger-color] border-[color-mix(in_srgb,var(--p-danger-color),transparent_60%)]',
    warning: 'text-[--p-warning-color] border-[color-mix(in_srgb,var(--p-warning-color),transparent_60%)]',
    info: 'text-[--p-primary-color] border-[color-mix(in_srgb,var(--p-primary-color),transparent_60%)]'
  }
  
  return `${base} ${variants[type]}`
}

const getIcon = (type: Toast['type']) => {
  const icons = {
    success: CheckCircle,
    error: XCircle,
    warning: AlertTriangle,
    info: Info
  }
  
  return icons[type]
}

const addToast = (toast: Omit<Toast, 'id'>) => {
  const id = Math.random().toString(36).substr(2, 9)
  const newToast = { ...toast, id }
  
  toasts.value.push(newToast)
  
  if (toast.duration !== 0) {
    setTimeout(() => {
      removeToast(id)
    }, toast.duration || 5000)
  }
}

const removeToast = (id: string) => {
  const index = toasts.value.findIndex(t => t.id === id)
  if (index > -1) {
    toasts.value.splice(index, 1)
  }
}

defineExpose({
  addToast,
  removeToast
})
</script>