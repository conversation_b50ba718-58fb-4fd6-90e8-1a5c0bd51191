// UI Components Index
export { default as <PERSON><PERSON> } from './Button.vue'
export { default as Card } from './Card.vue'
export { default as Input } from './Input.vue'
export { default as Badge } from './Badge.vue'
export { default as Progress } from './Progress.vue'
export { default as Spinner } from './Spinner.vue'
export { default as Tooltip } from './Tooltip.vue'
export { default as Tabs } from './Tabs.vue'
export { default as Toast } from './Toast.vue'
export { default as Modal } from './Modal.vue'
export { default as AnimatedCounter } from './AnimatedCounter.vue'
export { default as Spotlight } from './Spotlight.vue'

// New basic UI components
export { default as ErrorBoundary } from './ErrorBoundary.vue'
export { default as EmptyState } from './EmptyState.vue'
export { default as ConfirmationDialog } from './ConfirmationDialog.vue'