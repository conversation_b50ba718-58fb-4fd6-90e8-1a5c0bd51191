import { ref, computed, readonly, type Ref } from 'vue';
import type {
  EquipmentModelAttributeWithTemplate,
  AttributeTemplate,
  AttributeFormData,
  EditAttributeFormData,
  AttributeManagementState,
  AttributeTemplateFilter,
  GroupedAttributes
} from '@/types/attributes';
import {
  formatAttributeValue,
  groupAttributes,
  validateAttributeValue,
  getAvailableTemplates,
  getAttributeCountSummary
} from '@/utils/attributes';
import { useTrpc } from '@/composables/useTrpc';
import { useToast } from '@/composables/useToast';

/**
 * Composable for managing equipment model attributes
 */
export function useAttributes(equipmentId: Ref<string>) {
  const { $trpc } = useTrpc();
  const toast = useToast();

  // State
  const state = ref<AttributeManagementState>({
    attributes: [],
    loading: false,
    error: null,
    selectedAttribute: null,
    showAddDialog: false,
    showEditDialog: false
  });

  // Available templates cache
  const availableTemplates = ref<AttributeTemplate[]>([]);
  const attributeGroups = ref<any[]>([]);
  const templateGroups = ref<any[]>([]);
  
  // Quick add state
  const quickAddState = ref({
    selectedTemplateGroup: null as any,
    selectedTemplate: null as any,
    groupSuggestions: [] as any[],
    templateSuggestions: [] as any[],
    loadingTemplates: false
  });

  // Computed properties
  const groupedAttributes = computed<GroupedAttributes>(() => {
    return groupAttributes(state.value.attributes);
  });

  const attributeCount = computed(() => {
    return getAttributeCountSummary(state.value.attributes);
  });

  const hasAttributes = computed(() => {
    return state.value.attributes.length > 0;
  });

  // Methods
  async function loadAttributes() {
    if (!equipmentId.value) return;

    state.value.loading = true;
    state.value.error = null;

    try {
      // Note: This assumes the tRPC route exists - will be implemented in task 8
      const attributes = await $trpc.equipmentModelAttribute.findByEquipmentId.query({
        equipmentId: equipmentId.value
      });
      
      state.value.attributes = attributes as EquipmentModelAttributeWithTemplate[];
    } catch (error: any) {
      state.value.error = error.message || 'Ошибка загрузки атрибутов';
      toast.error('Не удалось загрузить атрибуты');
    } finally {
      state.value.loading = false;
    }
  }

  async function loadAvailableTemplates(filter?: AttributeTemplateFilter) {
    try {
      const templates = await $trpc.crud.attributeTemplate.findMany.query({
        include: {
          group: true
        }
      });
      
      availableTemplates.value = getAvailableTemplates(
        templates as AttributeTemplate[],
        state.value.attributes,
        filter
      );
    } catch (error: any) {
      toast.error('Не удалось загрузить шаблоны атрибутов');
    }
  }

  async function loadAttributeGroups() {
    try {
      const groups = await $trpc.crud.attributeGroup.findMany.query();
      attributeGroups.value = groups;
    } catch (error: any) {
      toast.error('Не удалось загрузить группы атрибутов');
    }
  }

  async function addAttribute(data: AttributeFormData) {
    if (!equipmentId.value) return;

    try {
      // Note: This assumes the tRPC route exists - will be implemented in task 8
      const newAttribute = await $trpc.equipmentModelAttribute.create.mutate({
        equipmentModelId: equipmentId.value,
        templateId: data.templateId,
        value: String(data.value)
      });

      // Reload attributes to get the full data with template
      await loadAttributes();
      
      toast.success('Атрибут успешно добавлен');
      state.value.showAddDialog = false;
    } catch (error: any) {
      toast.error(error.message || 'Не удалось добавить атрибут');
      throw error;
    }
  }

  async function updateAttribute(data: EditAttributeFormData) {
    try {
      // Validate the value first
      const errors = validateAttributeValue(data.value, data.template);
      if (errors.length > 0) {
        toast.error(errors[0].message);
        return;
      }

      // Note: This assumes the tRPC route exists - will be implemented in task 8
      await $trpc.equipmentModelAttribute.update.mutate({
        where: { id: data.id },
        data: { value: String(data.value) }
      });

      // Update local state
      const index = state.value.attributes.findIndex(attr => attr.id === data.id);
      if (index !== -1) {
        state.value.attributes[index].value = String(data.value);
      }

      toast.success('Атрибут успешно обновлен');
      state.value.showEditDialog = false;
      state.value.selectedAttribute = null;
    } catch (error: any) {
      toast.error(error.message || 'Не удалось обновить атрибут');
      throw error;
    }
  }

  async function deleteAttribute(attributeId: number) {
    const attribute = state.value.attributes.find(attr => attr.id === attributeId);
    if (!attribute) return;

    // Check if attribute is required
    if (attribute.template.isRequired) {
      toast.warning('Нельзя удалить обязательный атрибут');
      return;
    }

    try {
      // Note: This assumes the tRPC route exists - will be implemented in task 8
      await $trpc.equipmentModelAttribute.delete.mutate({
        where: { id: attributeId }
      });

      // Remove from local state
      state.value.attributes = state.value.attributes.filter(attr => attr.id !== attributeId);
      
      toast.success('Атрибут успешно удален');
    } catch (error: any) {
      toast.error(error.message || 'Не удалось удалить атрибут');
      throw error;
    }
  }

  function openAddDialog() {
    loadAvailableTemplates();
    state.value.showAddDialog = true;
  }

  function openEditDialog(attribute: EquipmentModelAttributeWithTemplate) {
    state.value.selectedAttribute = attribute;
    state.value.showEditDialog = true;
  }

  function closeDialogs() {
    state.value.showAddDialog = false;
    state.value.showEditDialog = false;
    state.value.selectedAttribute = null;
  }

  function formatAttribute(attribute: EquipmentModelAttributeWithTemplate) {
    return formatAttributeValue(attribute);
  }

  // Initialize
  // Quick add methods
  async function filterGroups(query: string) {
    try {
      const groups = await $trpc.crud.attributeGroup.findMany.query({
        where: {
          name: {
            contains: query,
            mode: 'insensitive'
          }
        },
        take: 10
      });
      quickAddState.value.groupSuggestions = groups || [];
    } catch (error) {
      console.error('Error filtering groups:', error);
      quickAddState.value.groupSuggestions = [];
    }
  }

  async function filterTemplates(query: string) {
    try {
      const templates = await $trpc.crud.attributeTemplate.findMany.query({
        where: {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { name: { contains: query, mode: 'insensitive' } }
          ]
        },
        include: {
          group: true
        },
        take: 10
      });
      quickAddState.value.templateSuggestions = templates || [];
    } catch (error) {
      console.error('Error filtering templates:', error);
      quickAddState.value.templateSuggestions = [];
    }
  }

  async function loadSelectedGroupTemplates() {
    if (!quickAddState.value.selectedTemplateGroup) return;
    
    quickAddState.value.loadingTemplates = true;
    try {
      const groupId = quickAddState.value.selectedTemplateGroup.id || quickAddState.value.selectedTemplateGroup;
      const templates = await $trpc.crud.attributeTemplate.findMany.query({
        where: { groupId },
        include: { group: true }
      });
      
      if (templates) {
        // Add templates as new attributes
        for (const template of templates) {
          const attributeData: AttributeFormData = {
            templateId: template.id,
            value: getDefaultValueForType(template.dataType),
            template: template
          };
          await addAttribute(attributeData);
        }
      }
      
      quickAddState.value.selectedTemplateGroup = null;
    } catch (error) {
      console.error('Error loading group templates:', error);
      toast.error('Не удалось загрузить шаблоны группы');
    } finally {
      quickAddState.value.loadingTemplates = false;
    }
  }

  async function addSingleTemplate() {
    if (!quickAddState.value.selectedTemplate) return;
    
    const template = quickAddState.value.selectedTemplate;
    const attributeData: AttributeFormData = {
      templateId: template.id,
      value: getDefaultValueForType(template.dataType),
      template: template
    };
    
    await addAttribute(attributeData);
    quickAddState.value.selectedTemplate = null;
  }

  async function loadTemplatesByGroupId(groupId: number) {
    quickAddState.value.loadingTemplates = true;
    try {
      const templates = await $trpc.crud.attributeTemplate.findMany.query({
        where: { groupId },
        include: { group: true }
      });
      
      if (templates) {
        for (const template of templates) {
          const attributeData: AttributeFormData = {
            templateId: template.id,
            value: getDefaultValueForType(template.dataType),
            template: template
          };
          await addAttribute(attributeData);
        }
      }
    } catch (error) {
      console.error('Error loading templates by group:', error);
      toast.error('Не удалось загрузить шаблоны группы');
    } finally {
      quickAddState.value.loadingTemplates = false;
    }
  }

  function getDefaultValueForType(dataType: string): any {
    switch (dataType) {
      case 'STRING': return '';
      case 'NUMBER': return 0;
      case 'BOOLEAN': return false;
      case 'DATE': return new Date();
      case 'JSON': return '';
      default: return '';
    }
  }

  async function loadTemplateGroups() {
    try {
      const groups = await $trpc.crud.attributeGroup.findMany.query({
        include: {
          _count: {
            select: { templates: true }
          }
        },
        orderBy: { name: 'asc' }
      });
      templateGroups.value = groups || [];
    } catch (error) {
      console.error('Error loading template groups:', error);
    }
  }

  function initialize() {
    if (equipmentId.value) {
      loadAttributes();
      loadAttributeGroups();
      loadTemplateGroups();
    }
  }

  return {
    // State
    state: readonly(state),
    availableTemplates: readonly(availableTemplates),
    attributeGroups: readonly(attributeGroups),
    templateGroups: readonly(templateGroups),
    quickAddState: readonly(quickAddState),
    
    // Computed
    groupedAttributes,
    attributeCount,
    hasAttributes,
    
    // Methods
    loadAttributes,
    loadAvailableTemplates,
    loadAttributeGroups,
    loadTemplateGroups,
    addAttribute,
    updateAttribute,
    deleteAttribute,
    openAddDialog,
    openEditDialog,
    closeDialogs,
    formatAttribute,
    
    // Quick add methods
    filterGroups,
    filterTemplates,
    loadSelectedGroupTemplates,
    addSingleTemplate,
    loadTemplatesByGroupId,
    
    initialize
  };
}