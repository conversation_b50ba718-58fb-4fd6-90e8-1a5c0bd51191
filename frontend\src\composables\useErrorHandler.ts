/**
 * Composable для централизованной обработки ошибок
 * Предоставляет единую систему обработки различных типов ошибок
 */

import { ref, computed } from 'vue'
import { useToast } from './useToast'

export interface AppError {
  code: string
  message: string
  details?: any
  field?: string
  timestamp: Date
  id: string
}

export interface ValidationError extends AppError {
  field: string
  value: any
  constraint: string
}

export interface NetworkError extends AppError {
  status: number
  retryable: boolean
  url?: string
}

export interface TRPCError extends AppError {
  trpcCode: string
  zodErrors?: Record<string, string[]>
}

// Глобальное хранилище ошибок
const globalErrors = ref<AppError[]>([])
const maxErrors = 50 // Максимальное количество ошибок в истории

export const useErrorHandler = () => {
  const toast = useToast()

  // Генерация уникального ID для ошибки
  const generateErrorId = (): string => {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Базовая функция для создания ошибки
  const createError = (
    code: string,
    message: string,
    details?: any,
    field?: string
  ): AppError => {
    return {
      id: generateErrorId(),
      code,
      message,
      details,
      field,
      timestamp: new Date()
    }
  }

  // Добавление ошибки в глобальное хранилище
  const addToGlobalErrors = (error: AppError) => {
    globalErrors.value.unshift(error)
    
    // Ограничиваем количество ошибок
    if (globalErrors.value.length > maxErrors) {
      globalErrors.value = globalErrors.value.slice(0, maxErrors)
    }
  }

  // Обработка tRPC ошибок
  const handleTRPCError = (err: any, showToast = true): TRPCError => {
    console.error('tRPC Error:', err)

    const code = err?.data?.code || err?.shape?.code || err?.name || 'UNKNOWN_ERROR'
    const zodErrors = err?.data?.zodError?.fieldErrors || err?.zodError?.fieldErrors
    
    let message = err?.message as string | undefined

    // Определяем сообщение по коду ошибки
    if (!message) {
      switch (code) {
        case 'UNAUTHORIZED':
          message = 'Требуется авторизация'
          break
        case 'FORBIDDEN':
          message = 'Недостаточно прав для выполнения операции'
          break
        case 'NOT_FOUND':
          message = 'Ресурс не найден'
          break
        case 'BAD_REQUEST':
          message = 'Некорректный запрос'
          break
        case 'CONFLICT':
          message = 'Конфликт данных'
          break
        case 'PRECONDITION_FAILED':
          message = 'Нарушены условия выполнения операции'
          break
        case 'INTERNAL_SERVER_ERROR':
          message = 'Внутренняя ошибка сервера'
          break
        case 'TIMEOUT':
          message = 'Превышено время ожидания'
          break
        default:
          message = 'Произошла ошибка при выполнении запроса'
      }
    }

    // Обработка Zod ошибок валидации
    if (zodErrors && typeof zodErrors === 'object') {
      const validationDetails = Object.entries(zodErrors)
        .flatMap(([field, issues]) =>
          (Array.isArray(issues) ? issues : [issues])
            .filter(Boolean)
            .map((issue) => `${field}: ${issue}`)
        )
        .slice(0, 5)
        .join('\n')
      
      if (validationDetails) {
        message = `Ошибка валидации:\n${validationDetails}`
      }
    }

    const error: TRPCError = {
      ...createError(code, message, err),
      trpcCode: code,
      zodErrors
    }

    addToGlobalErrors(error)

    if (showToast) {
      toast.error('Ошибка', message)
    }

    return error
  }

  // Обработка сетевых ошибок
  const handleNetworkError = (
    err: any,
    url?: string,
    showToast = true
  ): NetworkError => {
    console.error('Network Error:', err)

    const status = err?.status || err?.response?.status || 0
    const message = err?.message || 'Ошибка сети'
    
    // Определяем, можно ли повторить запрос
    const retryable = status >= 500 || status === 0 || status === 408 || status === 429

    const error: NetworkError = {
      ...createError('NETWORK_ERROR', message, err),
      status,
      retryable,
      url
    }

    addToGlobalErrors(error)

    if (showToast) {
      if (status === 0) {
        toast.error('Ошибка сети', 'Проверьте подключение к интернету')
      } else if (status >= 500) {
        toast.error('Ошибка сервера', 'Попробуйте повторить запрос позже')
      } else {
        toast.error('Ошибка сети', message)
      }
    }

    return error
  }

  // Обработка ошибок валидации
  const handleValidationError = (
    field: string,
    value: any,
    constraint: string,
    showToast = true
  ): ValidationError => {
    const message = `Поле "${field}": ${constraint}`
    
    const error: ValidationError = {
      ...createError('VALIDATION_ERROR', message, { value, constraint }, field),
      field,
      value,
      constraint
    }

    addToGlobalErrors(error)

    if (showToast) {
      toast.warn('Ошибка валидации', message)
    }

    return error
  }

  // Обработка общих ошибок
  const handleGenericError = (
    err: any,
    context?: string,
    showToast = true
  ): AppError => {
    console.error('Generic Error:', err)

    const message = err?.message || 'Произошла неизвестная ошибка'
    const code = err?.code || err?.name || 'GENERIC_ERROR'
    
    const error = createError(
      code,
      context ? `${context}: ${message}` : message,
      err
    )

    addToGlobalErrors(error)

    if (showToast) {
      toast.error('Ошибка', error.message)
    }

    return error
  }

  // Универсальная функция обработки ошибок
  const handleError = (
    err: any,
    options: {
      context?: string
      showToast?: boolean
      url?: string
    } = {}
  ): AppError => {
    const { context, showToast = true, url } = options

    // Определяем тип ошибки и обрабатываем соответственно
    if (err?.data?.code || err?.shape?.code) {
      return handleTRPCError(err, showToast)
    }

    if (err?.status || err?.response?.status) {
      return handleNetworkError(err, url, showToast)
    }

    return handleGenericError(err, context, showToast)
  }

  // Очистка ошибок
  const clearErrors = () => {
    globalErrors.value = []
  }

  // Удаление конкретной ошибки
  const removeError = (errorId: string) => {
    const index = globalErrors.value.findIndex(err => err.id === errorId)
    if (index !== -1) {
      globalErrors.value.splice(index, 1)
    }
  }

  // Получение ошибок по типу
  const getErrorsByType = (code: string) => {
    return globalErrors.value.filter(err => err.code === code)
  }

  // Получение последних ошибок
  const getRecentErrors = (count = 10) => {
    return globalErrors.value.slice(0, count)
  }

  // Проверка наличия критических ошибок
  const hasCriticalErrors = computed(() => {
    const criticalCodes = ['INTERNAL_SERVER_ERROR', 'UNAUTHORIZED', 'FORBIDDEN']
    return globalErrors.value.some(err => criticalCodes.includes(err.code))
  })

  // Статистика ошибок
  const errorStats = computed(() => {
    const stats: Record<string, number> = {}
    globalErrors.value.forEach(err => {
      stats[err.code] = (stats[err.code] || 0) + 1
    })
    return stats
  })

  // Функции для быстрого показа типичных ошибок
  const showSaveError = (entityName = 'Запись', error?: any) => {
    if (error) {
      handleError(error, { context: `Сохранение ${entityName.toLowerCase()}` })
    } else {
      toast.error('Ошибка сохранения', `Не удалось сохранить ${entityName.toLowerCase()}`)
    }
  }

  const showDeleteError = (entityName = 'Запись', error?: any) => {
    if (error) {
      handleError(error, { context: `Удаление ${entityName.toLowerCase()}` })
    } else {
      toast.error('Ошибка удаления', `Не удалось удалить ${entityName.toLowerCase()}`)
    }
  }

  const showLoadError = (entityName = 'Данные', error?: any) => {
    if (error) {
      handleError(error, { context: `Загрузка ${entityName.toLowerCase()}` })
    } else {
      toast.error('Ошибка загрузки', `Не удалось загрузить ${entityName.toLowerCase()}`)
    }
  }

  return {
    // Состояние
    errors: computed(() => globalErrors.value),
    hasCriticalErrors,
    errorStats,

    // Основные методы обработки
    handleError,
    handleTRPCError,
    handleNetworkError,
    handleValidationError,
    handleGenericError,

    // Управление ошибками
    clearErrors,
    removeError,
    getErrorsByType,
    getRecentErrors,

    // Удобные методы
    showSaveError,
    showDeleteError,
    showLoadError
  }
}

// Глобальная функция для обработки необработанных ошибок
export const setupGlobalErrorHandler = () => {
  if (typeof window === 'undefined') return

  const { handleError } = useErrorHandler()

  // Обработка необработанных промисов
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    handleError(event.reason, { context: 'Необработанная ошибка промиса' })
  })

  // Обработка JavaScript ошибок
  window.addEventListener('error', (event) => {
    console.error('JavaScript error:', event.error)
    handleError(event.error, { context: 'JavaScript ошибка' })
  })
}