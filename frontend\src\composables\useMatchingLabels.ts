export const useMatchingLabels = () => {
  const getAccuracyLabel = (accuracy: string): string => ({
    EXACT_MATCH: 'Точное совпадение',
    MATCH_WITH_NOTES: 'С примечаниями',
    REQUIRES_MODIFICATION: 'Требует доработки',
    PARTIAL_MATCH: 'Частичное совпадение',
  }[accuracy] || accuracy)

  const getAccuracySeverity = (accuracy: string): string => ({
    EXACT_MATCH: 'success',
    MATCH_WITH_NOTES: 'info',
    REQUIRES_MODIFICATION: 'warning',
    PARTIAL_MATCH: 'secondary',
  }[accuracy] || 'secondary')

  const getDetailSeverity = (kind: string): string => {
    if (kind.includes('EXACT')) return 'success'
    if (kind.includes('WITHIN_TOLERANCE') || kind.includes('NEAR')) return 'info'
    if (kind.includes('LEGACY')) return 'warning'
    return 'secondary'
  }

  const getKindLabel = (kind: string): string => ({
    NUMBER_EXACT: 'Число: точное',
    NUMBER_WITHIN_TOLERANCE: 'Число: в допуске',
    STRING_EXACT: 'Строка: точное',
    STRING_SYNONYM_EXACT: 'Строка: группа EXACT',
    STRING_SYNONYM_NEAR: 'Строка: группа NEAR',
    STRING_SYNONYM_LEGACY: 'Строка: группа LEGACY',
    EXACT_STRING: 'Точное совпадение',
  }[kind] || kind)

  return { getAccuracyLabel, getAccuracySeverity, getDetailSeverity, getKindLabel }
}


