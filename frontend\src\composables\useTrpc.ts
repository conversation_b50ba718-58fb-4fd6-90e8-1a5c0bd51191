import { ref, computed } from 'vue';
import { trpc } from '@/lib/trpc';
import { useErrorHandler } from './useErrorHandler';
// Типы не импортируем из сервера, чтобы избежать несовместимости.TRPC используется как any в клиенте
// Глобальные тосты транслируются через window-события и ловятся GlobalToastBus

// Типы для удобства
type TRPCClient = typeof trpc;

// Composable для работы с tRPC
export function useTrpc() {
  const loading = ref(false);
  const error = ref<string | null>(null);
  const errorHandler = useErrorHandler();
  
  const emitToast = (payload: { severity: 'success' | 'info' | 'warn' | 'error'; summary?: string; detail?: string; life?: number }) => {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('app:toast', { detail: payload }));
    }
  };

  // Функция для обработки ошибок (используем централизованную систему)
  const handleError = (err: any) => {
    const trpcError = errorHandler.handleTRPCError(err, false); // не показываем toast здесь
    error.value = trpcError.message;
    
    // Глобальный тост по ошибке
    emitToast({ severity: 'error', summary: 'Ошибка', detail: trpcError.message });
  };

  // Функция для очистки ошибок
  const clearError = () => {
    error.value = null;
  };

  // Wrapper для выполнения запросов с обработкой состояния и опциональными success-тостами
  const execute = async <T>(operation: () => Promise<T>, opts?: { success?: { title?: string; message?: string } }): Promise<T | null> => {
    try {
      loading.value = true;
      clearError();
      const result = await operation();
      if (opts?.success) {
        emitToast({ severity: 'success', summary: opts.success.title ?? 'Успешно', detail: opts.success.message });
      }
      return result;
    } catch (err) {
      handleError(err);
      return null;
    } finally {
      loading.value = false;
    }
  };

  return {
    // Состояние
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    
    // Методы
    clearError,
    execute,
    
    // tRPC клиент
    client: trpc,
    
    // Админ — вызывать напрямую через trpc в компонентах (см. AdminDashboard)
    
    // Удобные методы для работы с основными сущностями
    parts: {
      // Получить все части
      findMany: (input?: any) => execute(() => trpc.crud.part.findMany.query(input)),
      
      // Получить часть по ID
      findUnique: (input: { where: { id: number } }) => 
        execute(() => trpc.crud.part.findUnique.query(input)),
      
      // Создать новую часть
      create: (input: any) => execute(() => trpc.crud.part.create.mutate(input), { success: { title: 'Сохранено', message: 'Запчасть создана' } }),
      
      // Обновить часть
      update: (input: any) => execute(() => trpc.crud.part.update.mutate(input), { success: { title: 'Сохранено', message: 'Запчасть обновлена' } }),
      
      // Удалить часть
      delete: (input: { where: { id: number } }) => 
        execute(() => trpc.crud.part.delete.mutate(input), { success: { title: 'Удалено', message: 'Запчасть удалена' } }),
    },
    
    catalogItems: {
      // Получить все каталожные позиции
      findMany: (input?: any) => execute(() => trpc.crud.catalogItem.findMany.query(input)),
      
      // Получить каталожную позицию по ID
      findUnique: (input: { where: { id: number } }) => 
        execute(() => trpc.crud.catalogItem.findUnique.query(input)),
      
      // Создать новую каталожную позицию
      create: (input: any) => execute(() => trpc.crud.catalogItem.create.mutate(input), { success: { title: 'Сохранено', message: 'Позиция создана' } }),
      
      // Обновить каталожную позицию
      update: (input: any) => execute(() => trpc.crud.catalogItem.update.mutate(input), { success: { title: 'Сохранено', message: 'Позиция обновлена' } }),
      
      // Удалить каталожную позицию
      delete: (input: { where: { id: number } }) => 
        execute(() => trpc.crud.catalogItem.delete.mutate(input), { success: { title: 'Удалено', message: 'Позиция удалена' } }),
    },

    matching: {
      findMatchingParts: (input: { catalogItemId: number }) =>
        execute(() => trpc.matching.findMatchingParts.query(input)),
      findMatchingCatalogItems: (input: { partId: number }) =>
        execute(() => trpc.matching.findMatchingCatalogItems.query(input)),
      proposeLink: (input: { catalogItemId: number; partId: number; accuracySuggestion?: 'EXACT_MATCH'|'MATCH_WITH_NOTES'|'REQUIRES_MODIFICATION'|'PARTIAL_MATCH'; notesSuggestion?: string; details?: any }) =>
        execute(() => trpc.matching.proposeLink.mutate(input)),
      listProposals: (input: { status?: 'PENDING'|'APPROVED'|'REJECTED'|'INVALIDATED'; take?: number; skip?: number; catalogItemId?: number; partId?: number }) =>
        execute(() => trpc.matching.listProposals.query(input as any)),
      approveProposal: (input: { id: number; override?: { accuracy?: 'EXACT_MATCH'|'MATCH_WITH_NOTES'|'REQUIRES_MODIFICATION'|'PARTIAL_MATCH'; notes?: string } }) =>
        execute(() => trpc.matching.approveProposal.mutate(input as any)),
      rejectProposal: (input: { id: number; reason?: string }) =>
        execute(() => trpc.matching.rejectProposal.mutate(input as any)),
      generateProposals: (input: { catalogItemIds?: number[]; brandId?: number; take?: number; skip?: number }) =>
        execute(() => trpc.matching.generateProposals.mutate(input as any)),
      createPartFromItems: (input: { name?: string; partCategoryId: number; itemIds: number[]; accuracy?: 'EXACT_MATCH'|'MATCH_WITH_NOTES'|'REQUIRES_MODIFICATION'|'PARTIAL_MATCH'; notes?: string }) =>
        execute(() => trpc.matching.createPartFromItems.mutate(input as any)),
    },
    
    brands: {
      // Получить все бренды
      findMany: (input?: any) => execute(() => trpc.crud.brand.findMany.query(input)),
      
      // Создать новый бренд
      create: (input: any) => execute(() => trpc.crud.brand.create.mutate(input), { success: { title: 'Сохранено', message: 'Бренд создан' } }),

      // Обновить бренд
      update: (input: any) => execute(() => trpc.crud.brand.update.mutate(input), { success: { title: 'Сохранено', message: 'Бренд обновлён' } }),

      // Удалить бренд
      delete: (input: any) => execute(() => trpc.crud.brand.delete.mutate(input), { success: { title: 'Удалено', message: 'Бренд удалён' } }),
    },
    
    partCategories: {
      // Получить все категории
      findMany: (input?: any) => execute(() => trpc.crud.partCategory.findMany.query(input)),

      // Создать новую категорию
      create: (input: any) => execute(() => trpc.crud.partCategory.create.mutate(input), { success: { title: 'Сохранено', message: 'Категория создана' } }),

      // Обновить категорию
      update: (input: any) => execute(() => trpc.crud.partCategory.update.mutate(input), { success: { title: 'Сохранено', message: 'Категория обновлена' } }),

      // Удалить категорию
      delete: (input: any) => execute(() => trpc.crud.partCategory.delete.mutate(input), { success: { title: 'Удалено', message: 'Категория удалена' } }),
    },

    media: {
      uploadPartImage: (input: { partId: number; fileName: string; fileData: string; mimeType: string }) =>
        execute(() => trpc.upload.uploadPartImage.mutate(input), { success: { title: 'Загружено', message: 'Изображение запчасти обновлено' } }),
      deletePartImage: (input: { partId: number }) =>
        execute(() => trpc.upload.deletePartImage.mutate(input), { success: { title: 'Удалено', message: 'Изображение запчасти удалено' } }),

      uploadPartCategoryImage: (input: { partCategoryId: number; fileName: string; fileData: string; mimeType: string }) =>
        execute(() => trpc.upload.uploadPartCategoryImage.mutate(input), { success: { title: 'Загружено', message: 'Изображение категории обновлено' } }),
      deletePartCategoryImage: (input: { partCategoryId: number }) =>
        execute(() => trpc.upload.deletePartCategoryImage.mutate(input), { success: { title: 'Удалено', message: 'Изображение категории удалено' } }),
    },

    equipmentModels: {
      // Получить все модели техники
      findMany: (input?: any) => execute(() => trpc.crud.equipmentModel.findMany.query(input)),

      // Получить модель техники по ID
      findUnique: (input: { where: { id: string } }) =>
        execute(() => trpc.crud.equipmentModel.findUnique.query(input)),

      // Создать новую модель техники
      create: (input: any) => execute(() => trpc.crud.equipmentModel.create.mutate(input)),

      // Обновить модель техники
      update: (input: any) => execute(() => trpc.crud.equipmentModel.update.mutate(input)),

      // Удалить модель техники
      delete: (input: { where: { id: string } }) =>
        execute(() => trpc.crud.equipmentModel.delete.mutate(input)),
    },

    // Новые методы для работы с атрибутами на основе шаблонов
    partAttributes: {
      // Получить атрибуты запчасти
      findByPartId: (input: { partId: number }) =>
        execute(() => trpc.partAttributes.findByPartId.query(input)),

      // Создать атрибут запчасти
      create: (input: { partId: number; templateId: number; value: string }) =>
        execute(() => trpc.partAttributes.create.mutate(input), { success: { title: 'Сохранено', message: 'Атрибут добавлен' } }),

      // Обновить атрибут запчасти
      update: (input: { id: number; value: string }) =>
        execute(() => trpc.partAttributes.update.mutate(input), { success: { title: 'Сохранено', message: 'Атрибут обновлён' } }),

      // Удалить атрибут запчасти
      delete: (input: { id: number }) =>
        execute(() => trpc.partAttributes.delete.mutate(input), { success: { title: 'Удалено', message: 'Атрибут удалён' } }),

      // Массовое создание атрибутов
      bulkCreate: (input: { partId: number; attributes: Array<{ templateId: number; value: string }> }) =>
        execute(() => trpc.partAttributes.bulkCreate.mutate(input)),
    },

    // Методы для работы с шаблонами атрибутов
    attributeTemplates: {
      // Получить все шаблоны
      findMany: (input?: {
        groupId?: number;
        search?: string;
        dataType?: string;
        limit?: number;
        offset?: number
      }) => execute(() => trpc.attributeTemplates.findMany.query(input)),

      // Получить шаблон по ID
      findById: (input: { id: number }) =>
        execute(() => trpc.attributeTemplates.findById.query(input)),

      // Создать шаблон
      create: (input: any) =>
        execute(() => trpc.attributeTemplates.create.mutate(input), { success: { title: 'Сохранено', message: 'Шаблон создан' } }),

      // Обновить шаблон
      update: (input: any) =>
        execute(() => trpc.attributeTemplates.update.mutate(input), { success: { title: 'Сохранено', message: 'Шаблон обновлён' } }),

      // Удалить шаблон
      delete: (input: { id: number }) =>
        execute(() => trpc.attributeTemplates.delete.mutate(input), { success: { title: 'Удалено', message: 'Шаблон удалён' } }),

      // Получить все группы
      findAllGroups: () =>
        execute(() => trpc.attributeTemplates.findAllGroups.query({} as any)),

      // Создать группу
        createGroup: (input: { name: string; description?: string }) =>
        execute(() => trpc.attributeTemplates.createGroup.mutate(input), { success: { title: 'Сохранено', message: 'Группа создана' } }),

      // Обновить группу
        updateGroup: (input: { id: number; name?: string; description?: string }) =>
        execute(() => trpc.attributeTemplates.updateGroup.mutate(input), { success: { title: 'Сохранено', message: 'Группа обновлена' } }),

      // Удалить группу
        deleteGroup: (input: { id: number }) =>
        execute(() => trpc.attributeTemplates.deleteGroup.mutate(input), { success: { title: 'Удалено', message: 'Группа удалена' } }),
    },

    // Методы для работы с группами синонимов атрибутов
    attributeSynonyms: {
      groups: {
        findMany: (input: { templateId: number; search?: string; limit?: number; offset?: number }) =>
          execute(() => trpc.attributeSynonyms.groups.findMany.query(input)),
        create: (input: { templateId: number; name: string; description?: string | null; compatibilityLevel: 'EXACT' | 'NEAR' | 'LEGACY'; notes?: string | null }) =>
          execute(() => trpc.attributeSynonyms.groups.create.mutate(input), { success: { title: 'Сохранено', message: 'Группа синонимов создана' } }),
        update: (input: { id: number; name?: string; description?: string | null; compatibilityLevel?: 'EXACT' | 'NEAR' | 'LEGACY'; notes?: string | null }) =>
          execute(() => trpc.attributeSynonyms.groups.update.mutate(input), { success: { title: 'Сохранено', message: 'Группа синонимов обновлена' } }),
        delete: (input: { id: number }) =>
          execute(() => trpc.attributeSynonyms.groups.delete.mutate(input), { success: { title: 'Удалено', message: 'Группа синонимов удалена' } }),
      },
      synonyms: {
        findMany: (input: { groupId: number }) =>
          execute(() => trpc.attributeSynonyms.synonyms.findMany.query(input)),
        create: (input: { groupId: number; value: string }) =>
          execute(() => trpc.attributeSynonyms.synonyms.create.mutate(input), { success: { title: 'Сохранено', message: 'Синоним добавлен' } }),
        delete: (input: { id: number }) =>
          execute(() => trpc.attributeSynonyms.synonyms.delete.mutate(input), { success: { title: 'Удалено', message: 'Синоним удалён' } }),
      },
      utils: {
        findGroupByValue: (input: { templateId: number; value: string }) =>
          execute(() => trpc.attributeSynonyms.utils.findGroupByValue.query(input)),
      }
    },

    // NOTE: removed duplicate short matching section; full API is provided above

    partApplicability: {
      upsert: (input: any) => 
        execute(() => trpc.crud.partApplicability.upsert.mutate(input), { success: { title: 'Сохранено', message: 'Применимость обновлена' } }),
      findMany: (input?: any) => 
        execute(() => trpc.crud.partApplicability.findMany.query(input)),
      create: (input: any) =>
        execute(() => trpc.crud.partApplicability.create.mutate(input)),
      update: (input: any) =>
        execute(() => trpc.crud.partApplicability.update.mutate(input)),
      findFirst: (input: any) =>
        execute(() => trpc.crud.partApplicability.findFirst.query(input)),
      delete: (input: { where: { id: number } }) =>
        execute(() => trpc.crud.partApplicability.delete.mutate(input), { success: { title: 'Удалено', message: 'Связь с группой удалена' } }),
    },

    equipmentApplicability: {
      upsert: (input: any) =>
        execute(() => trpc.crud.equipmentApplicability.upsert.mutate(input)),
      findMany: (input?: any) =>
        execute(() => trpc.crud.equipmentApplicability.findMany.query(input)),
      create: (input: any) =>
        execute(() => trpc.crud.equipmentApplicability.create.mutate(input), { success: { title: 'Сохранено', message: 'Применимость техники добавлена' } }),
      update: (input: any) =>
        execute(() => trpc.crud.equipmentApplicability.update.mutate(input), { success: { title: 'Сохранено', message: 'Применимость техники обновлена' } }),
      findFirst: (input: any) =>
        execute(() => trpc.crud.equipmentApplicability.findFirst.query(input)),
    }
  };
}

// Экспортируем типы
// export type { AppRouter };
