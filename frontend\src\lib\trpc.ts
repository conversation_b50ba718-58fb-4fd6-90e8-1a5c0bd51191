import { createTRPCClient, httpBatchLink, loggerLink } from '@trpc/client';
import superjson from 'superjson';

// Получаем базовый URL API из переменных окружения или используем дефолтный
export const getBaseUrl = () => {
  if (typeof window !== 'undefined') {
    // В браузере используем localhost:3000
    return 'http://localhost';
  }
  // На сервере используем полный URL
  return process.env.API_URL || 'http://localhost';
};

// Создаем tRPC клиент с правильной типизацией
export const trpc: any = createTRPCClient<any>({
  links: [
    loggerLink({ enabled: () => import.meta.env.DEV }),
    httpBatchLink({
      url: `${getBaseUrl()}:3000/trpc`,
      transformer: superjson,
      // Включаем передачу cookies для авторизации
      fetch: (url, options) => {
        return fetch(url, {
          ...options,
          credentials: 'include', // Важно для передачи cookies
        });
      },
    }),
  ],
});

// Типы сервера намеренно не импортируются в фронтенд, чтобы избежать несовместимости версий
