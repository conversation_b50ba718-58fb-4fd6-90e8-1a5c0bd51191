import { twMerge } from 'tailwind-merge'
import { clsx, type ClassValue } from 'clsx'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Конвертация файла в base64 data URL
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(String(reader.result))
    reader.onerror = (e) => reject(e)
    reader.readAsDataURL(file)
  })
}

/**
 * Преобразует произвольную строку в URL-friendly slug.
 * Выполняет транслитерацию кириллицы → латиница, приводит к нижнему регистру,
 * заменяет любые небезопасные символы на дефисы и схлопывает их.
 */
export function slugify(source: string): string {
  if (!source) return ''

  const map: Record<string, string> = {
    а: 'a', б: 'b', в: 'v', г: 'g', д: 'd', е: 'e', ё: 'e', ж: 'zh', з: 'z', и: 'i', й: 'y',
    к: 'k', л: 'l', м: 'm', н: 'n', о: 'o', п: 'p', р: 'r', с: 's', т: 't', у: 'u', ф: 'f',
    х: 'h', ц: 'ts', ч: 'ch', ш: 'sh', щ: 'sch', ъ: '', ы: 'y', ь: '', э: 'e', ю: 'yu', я: 'ya',
  }

  const lower = source.trim().toLowerCase()

  let transliterated = ''
  for (const char of lower) {
    const code = char.charCodeAt(0)
    // латиница и цифры — оставляем как есть
    if ((code >= 97 && code <= 122) || (code >= 48 && code <= 57)) {
      transliterated += char
      continue
    }
    // кириллица
    if (map[char] !== undefined) {
      transliterated += map[char]
      continue
    }
    // пробелы и разделители → дефис
    if (/\s|[_]+/.test(char)) {
      transliterated += '-'
      continue
    }
    // любые другие символы игнорируем
  }

  return transliterated
    .replace(/[^a-z0-9-]/g, '-')
    .replace(/-{2,}/g, '-')
    .replace(/^-+|-+$/g, '')
}

/**
 * Преобразует относительный URL медиа (например, "/api/uploads/...")
 * в абсолютный URL к API (например, "http://localhost:3000/api/uploads/...").
 * Если URL уже абсолютный, возвращает его без изменений.
 */
export function resolveMediaUrl(url?: string | null): string | undefined {
  if (!url) return undefined
  if (/^https?:\/\//i.test(url)) return url
  // Совпадает с конфигом в lib/trpc.ts, где API доступен на :3000
  const apiBase = typeof window !== 'undefined'
    ? 'http://localhost:3000'
    : (process.env.API_URL ? String(process.env.API_URL).replace(/\/$/, '') : 'http://localhost:3000')
  if (url.startsWith('/')) return `${apiBase}${url}`
  return `${apiBase}/${url}`
}