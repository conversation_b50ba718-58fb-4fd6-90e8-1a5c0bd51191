/**
 * Middleware для проверки авторизации
 * Используется для защиты админских роутов
 */

import type { APIRoute } from 'astro'
import { auth } from '../../../../api/auth'

/**
 * Проверяет авторизацию пользователя на сервере
 */
export async function checkAuth(request: Request) {
  try {
    // Получаем сессию из cookies используя серверный auth instance
    const session = await auth.api.getSession({
      headers: request.headers
    })

    if (!session?.user) {
      return {
        isAuthenticated: false,
        user: null,
        redirectTo: '/admin/login'
      }
    }

    return {
      isAuthenticated: true,
      user: session.user,
      redirectTo: null
    }
  } catch (error) {
    console.error('Auth check error:', error)
    return {
      isAuthenticated: false,
      user: null,
      redirectTo: '/admin/login'
    }
  }
}

/**
 * Проверяет, имеет ли пользователь доступ к админ панели
 */
export function canAccessAdmin(user: any): boolean {
  if (!user) return false

  // Доступ имеют ADMIN и SHOP (владельцы магазинов)
  return user.role === 'ADMIN' || user.role === 'SHOP'
}

/**
 * Middleware для защищенных админских роутов
 */
export const authMiddleware: APIRoute = async ({ request, redirect }) => {
  const authResult = await checkAuth(request)
  
  if (!authResult.isAuthenticated) {
    return redirect('/admin/login')
  }
  
  if (!canAccessAdmin(authResult.user)) {
    return redirect('/admin/forbidden')
  }
  
  // Пользователь авторизован и имеет доступ
  return new Response(null, { status: 200 })
}

/**
 * Middleware для страниц, доступных только неавторизованным пользователям
 */
export const guestMiddleware: APIRoute = async ({ request, redirect }) => {
  const authResult = await checkAuth(request)
  
  if (authResult.isAuthenticated && canAccessAdmin(authResult.user)) {
    return redirect('/admin')
  }
  
  return new Response(null, { status: 200 })
}

/**
 * Хелпер для проверки роли пользователя
 */
export function hasRole(user: any, role: string): boolean {
  return user?.role === role
}

/**
 * Хелпер для проверки, является ли пользователь админом
 */
export function isAdmin(user: any): boolean {
  return hasRole(user, 'ADMIN')
}

/**
 * Хелпер для проверки, является ли пользователь владельцем магазина
 */
export function isShopOwner(user: any): boolean {
  return hasRole(user, 'SHOP')
}

/**
 * Создает защищенный роут
 */
export function createProtectedRoute(handler: APIRoute): APIRoute {
  return async (context) => {
    const authResult = await checkAuth(context.request)
    
    if (!authResult.isAuthenticated) {
      return context.redirect('/admin/login')
    }
    
    if (!canAccessAdmin(authResult.user)) {
      return context.redirect('/admin/forbidden')
    }
    
    // Добавляем пользователя в контекст
    ;(context as any).user = authResult.user
    
    return handler(context)
  }
}

/**
 * Создает роут только для админов
 */
export function createAdminOnlyRoute(handler: APIRoute): APIRoute {
  return async (context) => {
    const authResult = await checkAuth(context.request)
    
    if (!authResult.isAuthenticated) {
      return context.redirect('/admin/login')
    }
    
    if (!isAdmin(authResult.user)) {
      return context.redirect('/admin/forbidden')
    }
    
    ;(context as any).user = authResult.user
    
    return handler(context)
  }
}

/**
 * Создает роут только для владельцев магазинов
 */
export function createShopOwnerRoute(handler: APIRoute): APIRoute {
  return async (context) => {
    const authResult = await checkAuth(context.request)
    
    if (!authResult.isAuthenticated) {
      return context.redirect('/admin/login')
    }
    
    if (!isShopOwner(authResult.user)) {
      return context.redirect('/admin/forbidden')
    }
    
    ;(context as any).user = authResult.user
    
    return handler(context)
  }
}
