---
import CategoryListBoundary from "@/components/admin/categories/CategoryListBoundary.vue";
import AdminLayout from "../../../layouts/AdminLayout.astro";
import { trpc } from "@/lib/trpc";

const page = Astro.url.searchParams.get('page') || '1';
const pageSize = Astro.url.searchParams.get('pageSize') || '100';

const categories = await trpc.crud.partCategory.findMany.query({
    take: Number(pageSize),
    skip: (Number(page) - 1) * Number(pageSize),
    where: {
        level: 0  // Загружаем только корневые категории
    },
    include: {
        image: true,
        _count: {
            select: {
                parts: true,
                children: true,
            }
        }
    },
    orderBy: {
        name: 'asc'
    }
})
---

<AdminLayout>
  <h1>Categories</h1>
  <CategoryListBoundary client:load initialData={categories} />
</AdminLayout>