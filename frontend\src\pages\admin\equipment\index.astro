---
import EquipmentList from "@/components/admin/equipment/EquipmentList.vue";
import AdminLayout from "../../../layouts/AdminLayout.astro";
import { trpc } from "@/lib/trpc";

const page = Astro.url.searchParams.get('page') || '1';
const pageSize = Astro.url.searchParams.get('pageSize') || '100';

const equipment = await trpc.crud.equipmentModel.findMany.query({
    take: Number(pageSize),
    skip: (Number(page) - 1) * Number(pageSize),
    include: {
        brand: {
            select: {
                name: true
            }
        },
        _count: {
            select: {
                partApplicabilities: true,
                attributes: true,
            }
        }
    },
    orderBy: [
        { name: 'asc' }
    ]
})
---

<AdminLayout>
  <h1>Equipment Models</h1>
  <EquipmentList client:load initialData={equipment} />
</AdminLayout>