---
import AdminLayout from '../../layouts/AdminLayout.astro';
import Icon from '@/components/ui/Icon.vue';
import VCard from '../../volt/Card.vue';
import VButton from '../../volt/Button.vue';
import AdminDashboard from '@/components/admin/AdminDashboard.vue';

// Получаем информацию о пользователе из middleware
const user = Astro.locals.user;
const userRole = user?.role || 'UNKNOWN';
const userName = user?.name || user?.email || 'Пользователь';
---

<AdminLayout title="Дашборд - PartTec Admin">
  <div class="space-y-6">
    <!-- Заголовок -->
    <div>
      <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0">
        Добро пожаловать, {userName}!
      </h1>
      <p class="text-surface-600 dark:text-surface-400 mt-1">
        Обзор системы управления каталогом запчастей
      </p>
      <div class="mt-2 flex items-center space-x-2">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
          {userRole === 'ADMIN' ? 'Администратор' : userRole === 'SHOP' ? 'Магазин' : userRole}
        </span>
        <span class="text-surface-500 text-sm">•</span>
        <span class="text-surface-500 text-sm">{user?.email}</span>
      </div>
    </div>

    <!-- Основной дашборд -->
    <AdminDashboard client:load />

    <!-- Быстрые действия -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Создать запчасть -->
      <VCard client:load>
        <template #content>
          <div class="p-6 text-center">
            <div class="w-12 h-12 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex items-center justify-center">
              <Icon name="pi pi-plus" class="text-primary w-5 h-5" />
            </div>
            <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-2">
              Создать запчасть
            </h3>
            <p class="text-surface-600 dark:text-surface-400 text-sm mb-4">
              Добавить новую запчасть в каталог
            </p>
            <a href="/admin/parts/create">
              <VButton client:load class="w-full">
                Создать
              </VButton>
            </a>
          </div>
        </template>
      </VCard>

      <!-- Управление брендами -->
      <VCard client:load>
        <template #content>
          <div class="p-6 text-center">
            <div class="w-12 h-12 mx-auto mb-4 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <Icon name="pi pi-tags" class="text-blue-600 w-5 h-5" />
            </div>
            <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-2">
              Бренды
            </h3>
            <p class="text-surface-600 dark:text-surface-400 text-sm mb-4">
              Управление брендами запчастей
            </p>
            <a href="/admin/brands">
              <VButton client:load severity="secondary" class="w-full">
                Управлять
              </VButton>
            </a>
          </div>
        </template>
      </VCard>

      <!-- Категории -->
      <VCard client:load>
        <template #content>
          <div class="p-6 text-center">
            <div class="w-12 h-12 mx-auto mb-4 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <Icon name="pi pi-sitemap" class="text-green-600 w-5 h-5" />
            </div>
            <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-2">
              Категории
            </h3>
            <p class="text-surface-600 dark:text-surface-400 text-sm mb-4">
              Структура категорий запчастей
            </p>
            <a href="/admin/categories">
              <VButton client:load severity="secondary" class="w-full">
                Управлять
              </VButton>
            </a>
          </div>
        </template>
      </VCard>

      <!-- Каталожные позиции -->
      <VCard client:load>
        <template #content>
          <div class="p-6 text-center">
            <div class="w-12 h-12 mx-auto mb-4 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
              <Icon name="pi pi-list" class="text-orange-600 w-5 h-5" />
            </div>
            <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-2">
              Каталог
            </h3>
            <p class="text-surface-600 dark:text-surface-400 text-sm mb-4">
              Каталожные позиции и артикулы
            </p>
            <a href="/admin/catalog-items">
              <VButton client:load severity="secondary" class="w-full">
                Просмотр
              </VButton>
            </a>
          </div>
        </template>
      </VCard>
    </div>

    <!-- Быстрые ссылки -->
    <VCard client:load>
      <template #header>
        <div class="p-6 border-b border-surface-200 dark:border-surface-700">
          <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
            Быстрые ссылки
          </h3>
        </div>
      </template>
      <template #content>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <a href="/admin/parts" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors">
              <div class="flex items-center">
                <Icon name="pi pi-wrench" class="text-primary w-4 h-4 mr-3 inline-block" />
                <span class="text-surface-900 dark:text-surface-0">Все запчасти</span>
              </div>
            </a>
            <a href="/admin/parts/create" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors">
              <div class="flex items-center">
                <Icon name="pi pi-plus" class="text-primary w-4 h-4 mr-3 inline-block" />
                <span class="text-surface-900 dark:text-surface-0">Создать запчасть</span>
              </div>
            </a>
            <a href="/admin/brands" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors">
              <div class="flex items-center">
                <Icon name="pi pi-tags" class="text-primary w-4 h-4 mr-3 inline-block" />
                <span class="text-surface-900 dark:text-surface-0">Управление брендами</span>
              </div>
            </a>
            <a href="/admin/categories" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors">
              <div class="flex items-center">
                <Icon name="pi pi-sitemap" class="text-primary w-4 h-4 mr-3 inline-block" />
                <span class="text-surface-900 dark:text-surface-0">Управление категориями</span>
              </div>
            </a>
            <a href="/admin/catalog-items" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors">
              <div class="flex items-center">
                <Icon name="pi pi-list" class="text-primary w-4 h-4 mr-3 inline-block" />
                <span class="text-surface-900 dark:text-surface-0">Каталожные позиции</span>
              </div>
            </a>
            <a href="/admin/test-trpc" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors">
              <div class="flex items-center">
                <Icon name="pi pi-code" class="text-primary w-4 h-4 mr-3 inline-block" />
                <span class="text-surface-900 dark:text-surface-0">Тест API</span>
              </div>
            </a>
          </div>
        </div>
      </template>
    </VCard>
  </div>
</AdminLayout>
