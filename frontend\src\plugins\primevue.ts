import type { App } from 'vue';
import PrimeVue from 'primevue/config';
import ToastService from 'primevue/toastservice';
import ConfirmationService from 'primevue/confirmationservice';
import Tooltip from 'primevue/tooltip';

export default (app: App) => {
  app.use(PrimeVue, {
    unstyled: true
  });

  // Добавляем сервисы для Toast и ConfirmDialog
  app.use(ToastService);
  app.use(ConfirmationService);

  // Глобальная директива tooltip
  app.directive('tooltip', Tooltip);
};
