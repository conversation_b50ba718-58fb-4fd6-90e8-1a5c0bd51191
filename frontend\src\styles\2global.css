@import "tailwindcss";
@import "tailwindcss-primeui";

/* =====================================================
СИСТЕМА ТЕМ - СВЕТЛАЯ И ТЕМНАЯ
===================================================== */

/* Светлая тема (по умолчанию) */
:root {
    /* Primary Colors - using a more professional blue */
    --p-primary-50: #eef2ff;
    --p-primary-100: #e0e7ff;
    --p-primary-200: #c7d2fe;
    --p-primary-300: #a5b4fc;
    --p-primary-400: #818cf8;
    --p-primary-500: #6366f1; /* main primary color */
    --p-primary-600: #4f46e5;
    --p-primary-700: #4338ca;
    --p-primary-800: #3730a3;
    --p-primary-900: #312e81;
    --p-primary-950: #1e1b4b;

    /* Surface Colors - using Zinc palette */
    --p-surface-0: #ffffff;   /* white */
    --p-surface-50: #fafafa;   /* zinc-50 */
    --p-surface-100: #f4f4f5;  /* zinc-100 */
    --p-surface-200: #e4e4e7;  /* zinc-200 */
    --p-surface-300: #d4d4d8;  /* zinc-300 */
    --p-surface-400: #a1a1aa;  /* zinc-400 */
    --p-surface-500: #71717a;  /* zinc-500 */
    --p-surface-600: #52525b;  /* zinc-600 */
    --p-surface-700: #3f3f46;  /* zinc-700 */
    --p-surface-800: #27272a;  /* zinc-800 */
    --p-surface-900: #18181b;  /* zinc-900 */
    --p-surface-950: #09090b;  /* zinc-950 */

    /* Generic semantic variables */
    --p-text-color: var(--p-surface-900);
    --p-text-muted-color: var(--p-surface-500);
    --p-background-color: var(--p-surface-0);
    --p-surface-ground: var(--p-surface-50);
    --p-surface-section: var(--p-surface-100);
    --p-surface-card: var(--p-surface-0);
    --p-surface-overlay: var(--p-surface-0);
    --p-surface-border: var(--p-surface-200);
    --p-surface-hover: var(--p-surface-100);

    --p-content-border-radius: 6px;
}

/* Темная тема */
[data-theme="dark"] {
    /* Primary colors are the same, just the surface/text colors change */

    /* Surface Colors - dark theme (inverted Zinc) */
    --p-surface-0: #09090b;   /* zinc-950 */
    --p-surface-50: #18181b;   /* zinc-900 */
    --p-surface-100: #27272a;  /* zinc-800 */
    --p-surface-200: #3f3f46;  /* zinc-700 */
    --p-surface-300: #52525b;  /* zinc-600 */
    --p-surface-400: #71717a;  /* zinc-500 */
    --p-surface-500: #a1a1aa;  /* zinc-400 */
    --p-surface-600: #d4d4d8;  /* zinc-300 */
    --p-surface-700: #e4e4e7;  /* zinc-200 */
    --p-surface-800: #f4f4f5;  /* zinc-100 */
    --p-surface-900: #fafafa;   /* zinc-50 */
    --p-surface-950: #ffffff;   /* white */

    /* Generic semantic variables for dark theme */
    --p-text-color: var(--p-surface-900);
    --p-text-muted-color: var(--p-surface-400);
    --p-background-color: var(--p-surface-0);
    --p-surface-ground: var(--p-surface-50);
    --p-surface-section: var(--p-surface-100);
    --p-surface-card: var(--p-surface-100);
    --p-surface-overlay: var(--p-surface-200);
    --p-surface-border: var(--p-surface-200);
    --p-surface-hover: var(--p-surface-300);
}

/* Плавные переходы при смене темы */
* {
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* Light */
:root {
    --p-primary-color: var(--p-primary-500);
    --p-primary-contrast-color: var(--p-surface-0);
    --p-primary-hover-color: var(--p-primary-600);
    --p-primary-active-color: var(--p-primary-700);
    --p-content-border-color: var(--p-surface-border);
    --p-content-hover-background: var(--p-surface-hover);
    --p-content-hover-color: var(--p-surface-800);
    --p-highlight-background: var(--p-primary-50);
    --p-highlight-color: var(--p-primary-700);
    --p-highlight-focus-background: var(--p-primary-100);
    --p-highlight-focus-color: var(--p-primary-800);
    --p-text-color: var(--p-surface-900);
    --p-text-hover-color: var(--p-surface-900);
    --p-text-muted-color: var(--p-surface-500);
    --p-text-hover-muted-color: var(--p-surface-600);
}

/* Dark Mode - применяется только когда data-theme="dark" */
[data-theme="dark"] {
    --p-primary-color: var(--p-primary-400);
    --p-primary-contrast-color: var(--p-surface-950);
    --p-primary-hover-color: var(--p-primary-300);
    --p-primary-active-color: var(--p-primary-200);
    --p-content-border-color: var(--p-surface-border);
    --p-content-hover-background: var(--p-surface-hover);
    --p-content-hover-color: var(--p-surface-50);
    --p-highlight-background: color-mix(in srgb, var(--p-primary-400), transparent 84%);
    --p-highlight-color: var(--p-surface-950);
    --p-highlight-focus-background: color-mix(in srgb, var(--p-primary-400), transparent 76%);
    --p-highlight-focus-color: var(--p-surface-950);
    --p-text-color: var(--p-surface-100);
    --p-text-hover-color: var(--p-surface-50);
    --p-text-muted-color: var(--p-surface-400);
    --p-text-hover-muted-color: var(--p-surface-300);
}