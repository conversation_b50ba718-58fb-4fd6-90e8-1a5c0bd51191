@import "tailwindcss";
@import "tailwindcss-primeui";

/* =====================================================
Tailwind v4 @theme: единые дизайн‑токены, связанные с Prime/Volt переменными
===================================================== */
@theme {
    /* Цвета бренда и статусов */
    --color-primary: var(--p-primary-600);
    --color-primary-foreground: var(--p-primary-contrast-color);
    --color-primary-hover: var(--p-primary-700);
    --color-primary-active: var(--p-primary-800);

    --color-success: var(--p-success-600);
    --color-success-foreground: var(--p-success-contrast-color);
    --color-warning: var(--p-warning-600);
    --color-warning-foreground: var(--p-warning-contrast-color);
    --color-danger: var(--p-danger-600);
    --color-danger-foreground: var(--p-danger-contrast-color);

    /* Нейтральные и поверхности */
    --color-background: var(--p-background-color);
    --color-foreground: var(--p-text-color);
    --color-muted: var(--p-text-muted-color);
    --color-secondary: var(--p-text-secondary-color);
    --color-card: var(--p-surface-card);
    --color-border: var(--p-surface-border);
    --color-overlay: var(--p-surface-overlay);
    --color-ring: var(--p-primary-color);

    /* Радиусы и тени */
    --radius-sm: var(--p-border-radius-sm);
    --radius-md: var(--p-border-radius-md);
    --radius-lg: var(--p-border-radius-lg);
    --radius-xl: var(--p-border-radius-xl);

    --shadow-sm: var(--p-shadow-sm);
    --shadow-md: var(--p-shadow-md);
    --shadow-lg: var(--p-shadow-lg);
    --shadow-xl: var(--p-shadow-xl);
}

/* =====================================================
СИСТЕМА ТЕМ - СВЕТЛАЯ И ТЕМНАЯ
===================================================== */

/* Светлая тема (по умолчанию) */
:root {
    /* Primary Colors - современный профессиональный синий для инженерной индустрии */
    --p-primary-50: #eff6ff;
    --p-primary-100: #dbeafe;
    --p-primary-200: #bfdbfe;
    --p-primary-300: #93c5fd;
    --p-primary-400: #60a5fa;
    --p-primary-500: #3b82f6;
    /* основной цвет - более насыщенный синий */
    --p-primary-600: #2563eb;
    --p-primary-700: #1d4ed8;
    --p-primary-800: #1e40af;
    --p-primary-900: #1e3a8a;
    --p-primary-950: #172554;

    /* Success Colors - для статусов "в наличии", "доступно" */
    --p-success-50: #f0fdf4;
    --p-success-100: #dcfce7;
    --p-success-200: #bbf7d0;
    --p-success-300: #86efac;
    --p-success-400: #4ade80;
    --p-success-500: #22c55e;
    --p-success-600: #16a34a;
    --p-success-700: #15803d;
    --p-success-800: #166534;
    --p-success-900: #14532d;
    --p-success-950: #052e16;

    /* Warning Colors - для статусов "ограниченное количество", "требует внимания" */
    --p-warning-50: #fffbeb;
    --p-warning-100: #fef3c7;
    --p-warning-200: #fde68a;
    --p-warning-300: #fcd34d;
    --p-warning-400: #fbbf24;
    --p-warning-500: #f59e0b;
    --p-warning-600: #d97706;
    --p-warning-700: #b45309;
    --p-warning-800: #92400e;
    --p-warning-900: #78350f;
    --p-warning-950: #451a03;

    /* Danger Colors - для статусов "нет в наличии", "ошибки", "удаление" */
    --p-danger-50: #fef2f2;
    --p-danger-100: #fee2e2;
    --p-danger-200: #fecaca;
    --p-danger-300: #fca5a5;
    --p-danger-400: #f87171;
    --p-danger-500: #ef4444;
    --p-danger-600: #dc2626;
    --p-danger-700: #b91c1c;
    --p-danger-800: #991b1b;
    --p-danger-900: #7f1d1d;
    --p-danger-950: #450a0a;

    /* Surface Colors - обновленная Zinc палитра для премиального вида */
    --p-surface-0: #ffffff;
    /* чистый белый */
    --p-surface-50: #fafafa;
    /* zinc-50 - очень светлый фон */
    --p-surface-100: #f4f4f5;
    /* zinc-100 - светлый фон секций */
    --p-surface-200: #e4e4e7;
    /* zinc-200 - границы */
    --p-surface-300: #d4d4d8;
    /* zinc-300 - неактивные элементы */
    --p-surface-400: #a1a1aa;
    /* zinc-400 - приглушенный текст */
    --p-surface-500: #71717a;
    /* zinc-500 - вторичный текст */
    --p-surface-600: #52525b;
    /* zinc-600 - активный текст */
    --p-surface-700: #3f3f46;
    /* zinc-700 - основной текст */
    --p-surface-800: #27272a;
    /* zinc-800 - темный текст */
    --p-surface-900: #18181b;
    /* zinc-900 - самый темный */
    --p-surface-950: #09090b;
    /* zinc-950 - почти черный */

    /* Семантические переменные для светлой темы */
    --p-text-color: var(--p-surface-800);
    --p-text-muted-color: var(--p-surface-500);
    --p-text-secondary-color: var(--p-surface-600);
    --p-background-color: var(--p-surface-0);
    --p-surface-ground: var(--p-surface-50);
    --p-surface-section: var(--p-surface-100);
    --p-surface-card: var(--p-surface-0);
    --p-surface-overlay: var(--p-surface-0);
    --p-surface-border: var(--p-surface-200);
    --p-surface-hover: var(--p-surface-100);
    --p-surface-pressed: var(--p-surface-200);

    /* Современные радиусы скругления */
    --p-content-border-radius: 8px;
    --p-border-radius-sm: 6px;
    --p-border-radius-md: 8px;
    --p-border-radius-lg: 12px;
    --p-border-radius-xl: 16px;

    /* Тени для глубины */
    --p-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --p-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --p-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --p-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Темная тема - премиальный темный дизайн */
[data-theme="dark"] {
    /* Primary colors остаются теми же, меняются только surface/text цвета */

    /* Surface Colors - инвертированная Zinc палитра для темной темы */
    --p-surface-0: #09090b;
    /* zinc-950 - основной фон */
    --p-surface-50: #18181b;
    /* zinc-900 - фон секций */
    --p-surface-100: #27272a;
    /* zinc-800 - карточки */
    --p-surface-200: #3f3f46;
    /* zinc-700 - границы */
    --p-surface-300: #52525b;
    /* zinc-600 - hover состояния */
    --p-surface-400: #71717a;
    /* zinc-500 - приглушенный текст */
    --p-surface-500: #a1a1aa;
    /* zinc-400 - вторичный текст */
    --p-surface-600: #d4d4d8;
    /* zinc-300 - активный текст */
    --p-surface-700: #e4e4e7;
    /* zinc-200 - основной текст */
    --p-surface-800: #f4f4f5;
    /* zinc-100 - яркий текст */
    --p-surface-900: #fafafa;
    /* zinc-50 - самый яркий */
    --p-surface-950: #ffffff;
    /* белый - контрастный */

    /* Семантические переменные для темной темы */
    --p-text-color: var(--p-surface-700);
    --p-text-muted-color: var(--p-surface-400);
    --p-text-secondary-color: var(--p-surface-500);
    --p-background-color: var(--p-surface-0);
    --p-surface-ground: var(--p-surface-50);
    --p-surface-section: var(--p-surface-100);
    --p-surface-card: var(--p-surface-100);
    --p-surface-overlay: var(--p-surface-200);
    --p-surface-border: var(--p-surface-200);
    --p-surface-hover: var(--p-surface-300);
    --p-surface-pressed: var(--p-surface-400);

    /* Тени для темной темы */
    --p-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --p-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --p-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --p-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

/* Плавные переходы при смене темы и интерактивности */
* {
    transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Улучшенная типографика для профессионального каталога */
body {
    font-feature-settings: "rlig" 1, "calt" 1, "ss01" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Светлая тема - семантические переменные */
:root {
    /* Primary цвета */
    --p-primary-color: var(--p-primary-500);
    --p-primary-contrast-color: var(--p-surface-0);
    --p-primary-hover-color: var(--p-primary-600);
    --p-primary-active-color: var(--p-primary-700);

    /* Success цвета */
    --p-success-color: var(--p-success-500);
    --p-success-contrast-color: var(--p-surface-0);
    --p-success-hover-color: var(--p-success-600);
    --p-success-active-color: var(--p-success-700);

    /* Warning цвета */
    --p-warning-color: var(--p-warning-500);
    --p-warning-contrast-color: var(--p-surface-0);
    --p-warning-hover-color: var(--p-warning-600);
    --p-warning-active-color: var(--p-warning-700);

    /* Danger цвета */
    --p-danger-color: var(--p-danger-500);
    --p-danger-contrast-color: var(--p-surface-0);
    --p-danger-hover-color: var(--p-danger-600);
    --p-danger-active-color: var(--p-danger-700);

    /* Контент и интерактивность */
    --p-content-border-color: var(--p-surface-border);
    --p-content-hover-background: var(--p-surface-hover);
    --p-content-hover-color: var(--p-surface-800);
    --p-content-pressed-background: var(--p-surface-pressed);

    /* Подсветка и фокус */
    --p-highlight-background: var(--p-primary-50);
    --p-highlight-color: var(--p-primary-700);
    --p-highlight-focus-background: var(--p-primary-100);
    --p-highlight-focus-color: var(--p-primary-800);

    /* Текст */
    --p-text-hover-color: var(--p-surface-900);
    --p-text-hover-muted-color: var(--p-surface-600);
}

/* Темная тема - семантические переменные */
[data-theme="dark"] {
    /* Primary цвета для темной темы */
    --p-primary-color: var(--p-primary-400);
    --p-primary-contrast-color: var(--p-surface-950);
    --p-primary-hover-color: var(--p-primary-300);
    --p-primary-active-color: var(--p-primary-200);

    /* Success цвета для темной темы */
    --p-success-color: var(--p-success-400);
    --p-success-contrast-color: var(--p-surface-950);
    --p-success-hover-color: var(--p-success-300);
    --p-success-active-color: var(--p-success-200);

    /* Warning цвета для темной темы */
    --p-warning-color: var(--p-warning-400);
    --p-warning-contrast-color: var(--p-surface-950);
    --p-warning-hover-color: var(--p-warning-300);
    --p-warning-active-color: var(--p-warning-200);

    /* Danger цвета для темной темы */
    --p-danger-color: var(--p-danger-400);
    --p-danger-contrast-color: var(--p-surface-950);
    --p-danger-hover-color: var(--p-danger-300);
    --p-danger-active-color: var(--p-danger-200);

    /* Контент и интерактивность для темной темы */
    --p-content-border-color: var(--p-surface-border);
    --p-content-hover-background: var(--p-surface-hover);
    --p-content-hover-color: var(--p-surface-50);
    --p-content-pressed-background: var(--p-surface-pressed);

    /* Подсветка и фокус для темной темы */
    --p-highlight-background: color-mix(in srgb, var(--p-primary-400), transparent 84%);
    --p-highlight-color: var(--p-surface-950);
    --p-highlight-focus-background: color-mix(in srgb, var(--p-primary-400), transparent 76%);
    --p-highlight-focus-color: var(--p-surface-950);

    /* Текст для темной темы */
    --p-text-hover-color: var(--p-surface-950);
    --p-text-hover-muted-color: var(--p-surface-300);
}

/* Единый стиль фокуса для управляемой доступности */
:where(a, button, input, textarea, select, [role="button"], [tabindex]:not([tabindex="-1"])):focus-visible {
    outline: 2px solid var(--color-ring);
    outline-offset: 2px;
}

/* Поддержка reduced-motion для всех переходов */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
    }
}