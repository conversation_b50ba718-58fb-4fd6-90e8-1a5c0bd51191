@import "tailwindcss";
@import "tailwindcss-primeui";

/* =====================================================
   СИСТЕМА ТЕМ - СВЕТЛАЯ И ТЕМНАЯ
   ===================================================== */

/* Светлая тема (по умолчанию) */
:root {
    /* Primary Colors - остаются неизменными */
    --p-primary-50: #ecfdf5;
    --p-primary-100: #d1fae5;
    --p-primary-200: #a7f3d0;
    --p-primary-300: #6ee7b7;
    --p-primary-400: #34d399;
    --p-primary-500: #10b981;
    --p-primary-600: #059669;
    --p-primary-700: #047857;
    --p-primary-800: #065f46;
    --p-primary-900: #064e3b;
    --p-primary-950: #022c22;

    /* Surface Colors - светлая тема */
    --p-surface-0: #ffffff;
    --p-surface-50: #fafafa;
    --p-surface-100: #f4f4f5;
    --p-surface-200: #e4e4e7;
    --p-surface-300: #d4d4d8;
    --p-surface-400: #a1a1aa;
    --p-surface-500: #71717a;
    --p-surface-600: #52525b;
    --p-surface-700: #3f3f46;
    --p-surface-800: #27272a;
    --p-surface-900: #18181b;
    --p-surface-950: #09090b;

    /* Дополнительные переменные для темизации */
    --p-text-color: #18181b;
    --p-text-muted-color: #71717a;
    --p-background-color: #ffffff;
    --p-surface-ground: #fafafa;
    --p-surface-section: #ffffff;
    --p-surface-card: #ffffff;
    --p-surface-overlay: #ffffff;
    --p-surface-border: #e4e4e7;
    --p-surface-hover: #f4f4f5;

    --p-content-border-radius: 6px;
}

/* Темная тема */
[data-theme="dark"] {
    /* Surface Colors - темная тема (инвертированные) */
    --p-surface-0: #09090b;
    --p-surface-50: #18181b;
    --p-surface-100: #27272a;
    --p-surface-200: #3f3f46;
    --p-surface-300: #52525b;
    --p-surface-400: #71717a;
    --p-surface-500: #a1a1aa;
    --p-surface-600: #d4d4d8;
    --p-surface-700: #e4e4e7;
    --p-surface-800: #f4f4f5;
    --p-surface-900: #fafafa;
    --p-surface-950: #ffffff;

    /* Дополнительные переменные для темной темы */
    --p-text-color: #fafafa;
    --p-text-muted-color: #a1a1aa;
    --p-background-color: #09090b;
    --p-surface-ground: #18181b;
    --p-surface-section: #27272a;
    --p-surface-card: #27272a;
    --p-surface-overlay: #3f3f46;
    --p-surface-border: #3f3f46;
    --p-surface-hover: #52525b;
}

/* Плавные переходы при смене темы */
* {
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* Light */
:root {
    --p-primary-color: var(--p-primary-500);
    --p-primary-contrast-color: var(--p-surface-0);
    --p-primary-hover-color: var(--p-primary-600);
    --p-primary-active-color: var(--p-primary-700);
    --p-content-border-color: var(--p-surface-200);
    --p-content-hover-background: var(--p-surface-100);
    --p-content-hover-color: var(--p-surface-800);
    --p-highlight-background: var(--p-primary-50);
    --p-highlight-color: var(--p-primary-700);
    --p-highlight-focus-background: var(--p-primary-100);
    --p-highlight-focus-color: var(--p-primary-800);
    --p-text-color: var(--p-surface-700);
    --p-text-hover-color: var(--p-surface-800);
    --p-text-muted-color: var(--p-surface-500);
    --p-text-hover-muted-color: var(--p-surface-600);
}

/*
 * Dark Mode
 * Defaults to system, change the dark variant selector to match the CSS variable configuration.
 * For example;
 * @custom-variant dark (&:where(.app-dark, .app-dark *));
 * should match to;
 * :root[class="app-dark"]
*/
@media (prefers-color-scheme: dark) {
    :root {
        --p-primary-color: var(--p-primary-400);
        --p-primary-contrast-color: var(--p-surface-900);
        --p-primary-hover-color: var(--p-primary-300);
        --p-primary-active-color: var(--p-primary-200);
        --p-content-border-color: var(--p-surface-700);
        --p-content-hover-background: var(--p-surface-800);
        --p-content-hover-color: var(--p-surface-0);
        --p-highlight-background: color-mix(in srgb, var(--p-primary-400), transparent 84%);
        --p-highlight-color: rgba(255, 255, 255, 0.87);
        --p-highlight-focus-background: color-mix(in srgb, var(--p-primary-400), transparent 76%);
        --p-highlight-focus-color: rgba(255, 255, 255, 0.87);
        --p-text-color: var(--p-surface-0);
        --p-text-hover-color: var(--p-surface-0);
        --p-text-muted-color: var(--p-surface-400);
        --p-text-hover-muted-color: var(--p-surface-300);
    }
}
