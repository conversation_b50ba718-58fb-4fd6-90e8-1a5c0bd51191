import { z } from 'zod';

// Import base Zod schemas from API
import { 
  EquipmentModelAttributeSchema,
  EquipmentModelAttributeCreateSchema,
  EquipmentModelAttributeUpdateSchema
} from '../../../api/generated/zod/models/EquipmentModelAttribute.schema';
import { 
  AttributeTemplateSchema 
} from '../../../api/generated/zod/models/AttributeTemplate.schema';
import { 
  AttributeGroupSchema 
} from '../../../api/generated/zod/models/AttributeGroup.schema';
import { 
  AttributeDataTypeSchema 
} from '../../../api/generated/zod/enums/AttributeDataType.schema';
import { 
  AttributeUnitSchema 
} from '../../../api/generated/zod/enums/AttributeUnit.schema';

// Re-export base types for convenience
export type AttributeDataType = z.infer<typeof AttributeDataTypeSchema>;
export type AttributeUnit = z.infer<typeof AttributeUnitSchema>;

// Base types from Zod schemas
export type EquipmentModelAttribute = z.infer<typeof EquipmentModelAttributeSchema>;
export type AttributeTemplate = z.infer<typeof AttributeTemplateSchema>;
export type AttributeGroup = z.infer<typeof AttributeGroupSchema>;

/**
 * Extended EquipmentModelAttribute type with populated template data
 * This includes the full template information for display purposes
 */
export interface EquipmentModelAttributeWithTemplate extends Omit<EquipmentModelAttribute, 'template'> {
  template: AttributeTemplate & {
    group?: AttributeGroup | null;
  };
}

/**
 * Grouped attributes by AttributeGroup for organized display
 * Key is the group name, value is array of attributes in that group
 */
export interface GroupedAttributes {
  [groupName: string]: EquipmentModelAttributeWithTemplate[];
}

/**
 * Form data for adding a new attribute to equipment model
 */
export interface AttributeFormData {
  templateId: number;
  value: string | number | boolean | Date;
  template?: AttributeTemplate;
}

/**
 * Form data for editing an existing attribute
 */
export interface EditAttributeFormData {
  id: number;
  value: string | number | boolean | Date;
  template: AttributeTemplate;
}

/**
 * Validation schema for attribute values based on data type
 */
export const createAttributeValueSchema = (template: AttributeTemplate) => {
  switch (template.dataType) {
    case 'STRING':
      let stringSchema = z.string().min(1, 'Значение не может быть пустым');
      if (template.allowedValues && template.allowedValues.length > 0) {
        return z.enum(template.allowedValues as [string, ...string[]]);
      }
      return stringSchema;
    
    case 'NUMBER':
      let numberSchema = z.number({ 
        required_error: 'Значение обязательно',
        invalid_type_error: 'Значение должно быть числом'
      });
      if (template.minValue !== null && template.minValue !== undefined) {
        numberSchema = numberSchema.min(template.minValue, `Минимальное значение: ${template.minValue}`);
      }
      if (template.maxValue !== null && template.maxValue !== undefined) {
        numberSchema = numberSchema.max(template.maxValue, `Максимальное значение: ${template.maxValue}`);
      }
      return numberSchema;
    
    case 'BOOLEAN':
      return z.boolean();
    
    case 'DATE':
      return z.date({
        required_error: 'Дата обязательна',
        invalid_type_error: 'Неверный формат даты'
      });
    
    case 'JSON':
      return z.record(z.unknown()).or(z.array(z.unknown()));
    
    default:
      return z.string();
  }
};

/**
 * Schema for creating new equipment attribute
 */
export const EquipmentAttributeCreateFormSchema = z.object({
  templateId: z.number().min(1, 'Выберите шаблон атрибута'),
  value: z.union([z.string(), z.number(), z.boolean(), z.date()]),
  equipmentModelId: z.string().min(1, 'ID модели техники обязателен')
});

/**
 * Schema for updating equipment attribute
 */
export const EquipmentAttributeUpdateFormSchema = z.object({
  id: z.number().min(1, 'ID атрибута обязателен'),
  value: z.union([z.string(), z.number(), z.boolean(), z.date()])
});

/**
 * Type for attribute form validation errors
 */
export interface AttributeValidationError {
  field: string;
  message: string;
}

/**
 * Utility type for attribute display formatting
 */
export interface FormattedAttributeValue {
  displayValue: string;
  rawValue: string | number | boolean | Date;
  unit?: AttributeUnit | null;
  dataType: AttributeDataType;
}

/**
 * Props interface for attribute-related components
 */
export interface AttributeComponentProps {
  equipmentId: string;
  attributes?: EquipmentModelAttributeWithTemplate[];
  readonly?: boolean;
}

/**
 * Events interface for attribute management components
 */
export interface AttributeEvents {
  'add-attribute': (data: AttributeFormData) => void;
  'edit-attribute': (attributeId: number) => void;
  'delete-attribute': (attributeId: number) => void;
  'update-attribute': (data: EditAttributeFormData) => void;
}

/**
 * State interface for attribute management
 */
export interface AttributeManagementState {
  attributes: EquipmentModelAttributeWithTemplate[];
  loading: boolean;
  error: string | null;
  selectedAttribute: EquipmentModelAttributeWithTemplate | null;
  showAddDialog: boolean;
  showEditDialog: boolean;
}

/**
 * Filter options for attribute templates
 */
export interface AttributeTemplateFilter {
  groupId?: number;
  dataType?: AttributeDataType;
  searchQuery?: string;
  excludeTemplateIds?: number[];
}

/**
 * Utility functions type definitions
 */
export interface AttributeUtils {
  formatValue: (attribute: EquipmentModelAttributeWithTemplate) => FormattedAttributeValue;
  groupAttributes: (attributes: EquipmentModelAttributeWithTemplate[]) => GroupedAttributes;
  validateAttributeValue: (value: any, template: AttributeTemplate) => AttributeValidationError[];
  getAvailableTemplates: (existingAttributes: EquipmentModelAttributeWithTemplate[], filter?: AttributeTemplateFilter) => AttributeTemplate[];
}

// Export Zod schemas for validation
export {
  EquipmentModelAttributeSchema,
  EquipmentModelAttributeCreateSchema,
  EquipmentModelAttributeUpdateSchema,
  AttributeTemplateSchema,
  AttributeGroupSchema,
  AttributeDataTypeSchema,
  AttributeUnitSchema
};

// Additional utility types for form handling
export type AttributeFormValue = string | number | boolean | Date | null;

export interface AttributeFormState {
  templateId: number | null;
  value: AttributeFormValue;
  isValid: boolean;
  errors: AttributeValidationError[];
}

export interface AttributeDialogProps {
  visible: boolean;
  equipmentId: string;
  attribute?: EquipmentModelAttributeWithTemplate;
  availableTemplates?: AttributeTemplate[];
}

export interface AttributeDialogEvents {
  'update:visible': (visible: boolean) => void;
  'save': (data: AttributeFormData | EditAttributeFormData) => void;
  'cancel': () => void;
}