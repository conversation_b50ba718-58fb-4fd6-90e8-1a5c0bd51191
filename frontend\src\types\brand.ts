import type { trpc } from '@/lib/trpc';

/**
 * Тип данных бренда, возвращаемый из API tRPC (включая связанные данные, такие как _count).
 * Получен напрямую из вывода роутера tRPC для обеспечения максимальной безопасности типов.
 */
export type BrandWithCount = Awaited<
  ReturnType<typeof trpc.crud.brand.findMany.query>
>[number];

/**
 * Основной тип бренда, без служебных полей вроде `_count`.
 * Идеально подходит для использования в формах и при передаче данных, не требующих агрегации.
 */
export type Brand = Omit<BrandWithCount, '_count'>;

/**
 * Тип для данных формы редактирования или создания бренда.
 * Все поля являются необязательными, так как форма может быть не полностью заполнена.
 * `id` также необязателен, так как при создании нового бренда его еще нет.
 */
export type BrandFormData = Partial<Brand>;

/**
 * Тип для данных, необходимых для создания нового бренда через tRPC.
 * Получен из параметров мутации `create` для соответствия ожиданиям API.
 */
export type BrandCreateInput = Parameters<
  typeof trpc.crud.brand.create.mutate
>[0];

/**
 * Тип для данных, необходимых для обновления существующего бренда через tRPC.
 * Получен из параметров мутации `update` для соответствия ожиданиям API.
 */
export type BrandUpdateInputData = Parameters<
  typeof trpc.crud.brand.update.mutate
>[0]['data']; 