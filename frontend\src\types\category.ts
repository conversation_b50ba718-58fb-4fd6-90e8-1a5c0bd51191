import type { trpc } from '@/lib/trpc';

/**
 * Тип данных категории, возвращаемый из API tRPC (включая связанные данные, такие как _count).
 * Получен напрямую из вывода роутера tRPC для обеспечения максимальной безопасности типов.
 */
export type CategoryWithCount = Awaited<
  ReturnType<typeof trpc.crud.partCategory.findMany.query>
>[number];

/**
 * Основной тип категории, без служебных полей вроде `_count`.
 * Идеально подходит для использования в формах и при передаче данных, не требующих агрегации.
 */
export type Category = Omit<CategoryWithCount, '_count'>;

/**
 * Тип для данных формы редактирования или создания категории.
 * Все поля являются необязательными, так как форма может быть не полностью заполнена.
 * `id` также необязателен, так как при создании новой категории его еще нет.
 */
export type CategoryFormData = Partial<Category>;

/**
 * Тип для данных, необходимых для создания новой категории через tRPC.
 * Получен из параметров мутации `create` для соответствия ожиданиям API.
 */
export type CategoryCreateInput = Parameters<
  typeof trpc.crud.partCategory.create.mutate
>[0];

/**
 * Тип для данных, необходимых для обновления существующей категории через tRPC.
 * Получен из параметров мутации `update` для соответствия ожиданиям API.
 */
export type CategoryUpdateInputData = Parameters<
  typeof trpc.crud.partCategory.update.mutate
>[0]['data'];