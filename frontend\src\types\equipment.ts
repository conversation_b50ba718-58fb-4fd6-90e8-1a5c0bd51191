import type { trpc } from '@/lib/trpc';

/**
 * Тип данных модели техники, возвращаемый из API tRPC (включая связанные данные, такие как _count).
 * Получен напрямую из вывода роутера tRPC для обеспечения максимальной безопасности типов.
 */
export type EquipmentModelWithCount = Awaited<
  ReturnType<typeof trpc.crud.equipmentModel.findMany.query>
>[number];

/**
 * Основной тип модели техники, без служебных полей вроде `_count`.
 * Идеально подходит для использования в формах и при передаче данных, не требующих агрегации.
 */
export type EquipmentModel = Omit<EquipmentModelWithCount, '_count'>;

/**
 * Тип для данных формы редактирования или создания модели техники.
 * Все поля являются необязательными, так как форма может быть не полностью заполнена.
 * `id` также необязателен, так как при создании новой модели его еще нет.
 */
export type EquipmentModelFormData = Partial<EquipmentModel>;

/**
 * Тип для данных, необходимых для создания новой модели техники через tRPC.
 * Получен из параметров мутации `create` для соответствия ожиданиям API.
 */
export type EquipmentModelCreateInput = Parameters<
  typeof trpc.crud.equipmentModel.create.mutate
>[0];

/**
 * Тип для данных, необходимых для обновления существующей модели техники через tRPC.
 * Получен из параметров мутации `update` для соответствия ожиданиям API.
 */
export type EquipmentModelUpdateInputData = Parameters<
  typeof trpc.crud.equipmentModel.update.mutate
>[0]['data'];

/**
 * Расширенный тип модели техники с информацией об атрибутах
 */
export interface EquipmentModelWithAttributes extends EquipmentModel {
  attributes?: import('./attributes').EquipmentModelAttributeWithTemplate[];
  _count?: {
    attributes?: number;
  };
}