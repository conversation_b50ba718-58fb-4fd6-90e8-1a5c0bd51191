import type {
  EquipmentModelAttributeWithTemplate,
  GroupedAttributes,
  FormattedAttributeValue,
  AttributeTemplate,
  AttributeValidationError,
  AttributeTemplateFilter,
  AttributeDataType,
  AttributeUnit
} from '@/types/attributes';
import { createAttributeValueSchema } from '@/types/attributes';

/**
 * Format attribute value for display based on data type and unit
 */
export function formatAttributeValue(attribute: EquipmentModelAttributeWithTemplate): FormattedAttributeValue {
  const { value, template } = attribute;
  const { dataType, unit } = template;
  
  let displayValue: string;
  let rawValue: string | number | boolean | Date;

  try {
    switch (dataType) {
      case 'STRING':
        rawValue = value;
        displayValue = value;
        break;
      
      case 'NUMBER':
        rawValue = parseFloat(value);
        displayValue = isNaN(rawValue) ? value : rawValue.toLocaleString('ru-RU');
        if (unit) {
          displayValue += ` ${getUnitDisplayName(unit)}`;
        }
        break;
      
      case 'BOOLEAN':
        rawValue = value.toLowerCase() === 'true';
        displayValue = rawValue ? 'Да' : 'Нет';
        break;
      
      case 'DATE':
        rawValue = new Date(value);
        displayValue = isNaN(rawValue.getTime()) ? value : rawValue.toLocaleDateString('ru-RU');
        break;
      
      case 'JSON':
        try {
          rawValue = JSON.parse(value);
          displayValue = JSON.stringify(rawValue, null, 2);
        } catch {
          rawValue = value;
          displayValue = value;
        }
        break;
      
      default:
        rawValue = value;
        displayValue = value;
    }
  } catch (error) {
    rawValue = value;
    displayValue = value;
  }

  return {
    displayValue,
    rawValue,
    unit,
    dataType
  };
}

/**
 * Group attributes by their AttributeGroup
 */
export function groupAttributes(attributes: EquipmentModelAttributeWithTemplate[]): GroupedAttributes {
  const grouped: GroupedAttributes = {};
  
  attributes.forEach(attribute => {
    const groupName = attribute.template.group?.name || 'Общие';
    
    if (!grouped[groupName]) {
      grouped[groupName] = [];
    }
    
    grouped[groupName].push(attribute);
  });
  
  // Sort attributes within each group by template name
  Object.keys(grouped).forEach(groupName => {
    grouped[groupName].sort((a, b) => a.template.title.localeCompare(b.template.title, 'ru'));
  });
  
  return grouped;
}

/**
 * Validate attribute value against template constraints
 */
export function validateAttributeValue(value: any, template: AttributeTemplate): AttributeValidationError[] {
  const errors: AttributeValidationError[] = [];
  
  try {
    const schema = createAttributeValueSchema(template);
    schema.parse(value);
  } catch (error: any) {
    if (error.errors) {
      error.errors.forEach((err: any) => {
        errors.push({
          field: 'value',
          message: err.message
        });
      });
    } else {
      errors.push({
        field: 'value',
        message: 'Неверное значение атрибута'
      });
    }
  }
  
  return errors;
}

/**
 * Get available templates excluding already used ones
 */
export function getAvailableTemplates(
  allTemplates: AttributeTemplate[],
  existingAttributes: EquipmentModelAttributeWithTemplate[],
  filter?: AttributeTemplateFilter
): AttributeTemplate[] {
  const usedTemplateIds = new Set(existingAttributes.map(attr => attr.templateId));
  
  let availableTemplates = allTemplates.filter(template => 
    !usedTemplateIds.has(template.id)
  );
  
  if (filter) {
    if (filter.excludeTemplateIds) {
      const excludeIds = new Set(filter.excludeTemplateIds);
      availableTemplates = availableTemplates.filter(template => 
        !excludeIds.has(template.id)
      );
    }
    
    if (filter.groupId) {
      availableTemplates = availableTemplates.filter(template => 
        template.groupId === filter.groupId
      );
    }
    
    if (filter.dataType) {
      availableTemplates = availableTemplates.filter(template => 
        template.dataType === filter.dataType
      );
    }
    
    if (filter.searchQuery) {
      const query = filter.searchQuery.toLowerCase();
      availableTemplates = availableTemplates.filter(template => 
        template.title.toLowerCase().includes(query) ||
        template.name.toLowerCase().includes(query) ||
        (template.description && template.description.toLowerCase().includes(query))
      );
    }
  }
  
  return availableTemplates.sort((a, b) => a.title.localeCompare(b.title, 'ru'));
}

/**
 * Get display name for attribute unit
 */
export function getUnitDisplayName(unit: AttributeUnit | null | undefined): string {
  if (!unit) return '';
  
  const unitNames: Record<AttributeUnit, string> = {
    MM: 'мм',
    INCH: 'дюйм',
    FT: 'фт',
    G: 'г',
    KG: 'кг',
    T: 'т',
    LB: 'фунт',
    ML: 'мл',
    L: 'л',
    GAL: 'гал',
    SEC: 'сек',
    MIN: 'мин',
    H: 'ч',
    PCS: 'шт',
    SET: 'комплект',
    PAIR: 'пара',
    BAR: 'бар',
    PSI: 'psi',
    KW: 'кВт',
    HP: 'л.с.',
    NM: 'Н⋅м',
    RPM: 'об/мин',
    C: '°C',
    F: '°F',
    PERCENT: '%'
  };
  
  return unitNames[unit] || unit;
}

/**
 * Get display name for attribute data type
 */
export function getDataTypeDisplayName(dataType: AttributeDataType): string {
  const typeNames: Record<AttributeDataType, string> = {
    STRING: 'Текст',
    NUMBER: 'Число',
    BOOLEAN: 'Да/Нет',
    DATE: 'Дата',
    JSON: 'JSON'
  };
  
  return typeNames[dataType] || dataType;
}

/**
 * Convert string value to appropriate type based on data type
 */
export function convertValueToType(value: string, dataType: AttributeDataType): string | number | boolean | Date {
  switch (dataType) {
    case 'NUMBER':
      const num = parseFloat(value);
      return isNaN(num) ? 0 : num;
    
    case 'BOOLEAN':
      return value.toLowerCase() === 'true' || value === '1' || value === 'да';
    
    case 'DATE':
      const date = new Date(value);
      return isNaN(date.getTime()) ? new Date() : date;
    
    case 'JSON':
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    
    default:
      return value;
  }
}

/**
 * Convert typed value to string for storage
 */
export function convertValueToString(value: string | number | boolean | Date): string {
  if (value instanceof Date) {
    return value.toISOString();
  }
  
  if (typeof value === 'boolean') {
    return value.toString();
  }
  
  if (typeof value === 'number') {
    return value.toString();
  }
  
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  
  return String(value);
}

/**
 * Check if attribute is required based on template
 */
export function isAttributeRequired(template: AttributeTemplate): boolean {
  return template.isRequired;
}

/**
 * Get attribute count summary for equipment model
 */
export function getAttributeCountSummary(attributes: EquipmentModelAttributeWithTemplate[]): {
  total: number;
  required: number;
  optional: number;
  byGroup: Record<string, number>;
} {
  const summary = {
    total: attributes.length,
    required: 0,
    optional: 0,
    byGroup: {} as Record<string, number>
  };
  
  attributes.forEach(attribute => {
    if (attribute.template.isRequired) {
      summary.required++;
    } else {
      summary.optional++;
    }
    
    const groupName = attribute.template.group?.name || 'Общие';
    summary.byGroup[groupName] = (summary.byGroup[groupName] || 0) + 1;
  });
  
  return summary;
}