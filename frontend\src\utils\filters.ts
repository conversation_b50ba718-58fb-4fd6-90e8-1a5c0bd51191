/**
 * Утилиты для работы с фильтрами каталога
 */

export interface CatalogFilters {
  search?: string;
  categoryIds?: number[];
  brandIds?: number[];
  attributes?: AttributeFilter[];
  equipmentModelIds?: string[];
  priceRange?: [number, number];
}

export interface AttributeFilter {
  templateId: number;
  value?: string;
  minValue?: number;
  maxValue?: number;
  operator?: 'equals' | 'contains' | 'range' | 'in';
}

export interface ViewPreferences {
  mode: 'grid' | 'list' | 'table';
  itemsPerPage: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Преобразование фильтров в параметры запроса
export const filtersToQuery = (filters: CatalogFilters) => {
  const query: Record<string, any> = {};
  
  if (filters.search) {
    query.search = filters.search;
  }
  
  if (filters.categoryIds?.length) {
    query.categoryIds = filters.categoryIds;
  }
  
  if (filters.brandIds?.length) {
    query.brandIds = filters.brandIds;
  }
  
  if (filters.equipmentModelIds?.length) {
    query.equipmentModelIds = filters.equipmentModelIds;
  }
  
  if (filters.priceRange) {
    query.minPrice = filters.priceRange[0];
    query.maxPrice = filters.priceRange[1];
  }
  
  if (filters.attributes?.length) {
    query.attributes = filters.attributes;
  }
  
  return query;
};

// Преобразование параметров запроса в фильтры
export const queryToFilters = (query: Record<string, any>): CatalogFilters => {
  const filters: CatalogFilters = {};
  
  if (query.search) {
    filters.search = query.search;
  }
  
  if (query.categoryIds) {
    filters.categoryIds = Array.isArray(query.categoryIds) 
      ? query.categoryIds.map(Number) 
      : [Number(query.categoryIds)];
  }
  
  if (query.brandIds) {
    filters.brandIds = Array.isArray(query.brandIds) 
      ? query.brandIds.map(Number) 
      : [Number(query.brandIds)];
  }
  
  if (query.equipmentModelIds) {
    filters.equipmentModelIds = Array.isArray(query.equipmentModelIds) 
      ? query.equipmentModelIds 
      : [query.equipmentModelIds];
  }
  
  if (query.minPrice !== undefined || query.maxPrice !== undefined) {
    filters.priceRange = [
      query.minPrice ? Number(query.minPrice) : 0,
      query.maxPrice ? Number(query.maxPrice) : Infinity
    ];
  }
  
  if (query.attributes) {
    filters.attributes = Array.isArray(query.attributes) 
      ? query.attributes 
      : [query.attributes];
  }
  
  return filters;
};

// Проверка, есть ли активные фильтры
export const hasActiveFilters = (filters: CatalogFilters): boolean => {
  return !!(
    filters.search ||
    filters.categoryIds?.length ||
    filters.brandIds?.length ||
    filters.equipmentModelIds?.length ||
    filters.priceRange ||
    filters.attributes?.length
  );
};

// Очистка фильтров
export const clearFilters = (): CatalogFilters => ({});

// Объединение фильтров
export const mergeFilters = (base: CatalogFilters, updates: Partial<CatalogFilters>): CatalogFilters => {
  return {
    ...base,
    ...updates,
    categoryIds: updates.categoryIds ?? base.categoryIds,
    brandIds: updates.brandIds ?? base.brandIds,
    equipmentModelIds: updates.equipmentModelIds ?? base.equipmentModelIds,
    attributes: updates.attributes ?? base.attributes,
  };
};