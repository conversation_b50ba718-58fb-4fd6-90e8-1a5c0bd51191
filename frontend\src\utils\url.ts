/**
 * Утилиты для работы с URL параметрами
 */

// Получить параметр из URL
export const getUrlParam = (key: string): string | null => {
  if (typeof window === 'undefined') return null
  const params = new URLSearchParams(window.location.search)
  return params.get(key)
}

// Получить все значения параметра из URL (для массивов)
export const getUrlParams = (key: string): string[] => {
  if (typeof window === 'undefined') return []
  const params = new URLSearchParams(window.location.search)
  return params.getAll(key)
}

// Установить параметр в URL
export const setUrlParam = (key: string, value: string | string[] | null) => {
  if (typeof window === 'undefined') return
  
  const params = new URLSearchParams(window.location.search)
  
  // Удаляем существующие значения
  params.delete(key)
  
  // Добавляем новые значения
  if (value !== null) {
    if (Array.isArray(value)) {
      value.forEach(v => params.append(key, v))
    } else {
      params.set(key, value)
    }
  }
  
  // Обновляем URL
  const newSearch = params.toString()
  const newUrl = `${window.location.pathname}${newSearch ? '?' + newSearch : ''}`
  window.history.replaceState({}, '', newUrl)
}

// Удалить параметр из URL
export const removeUrlParam = (key: string) => {
  setUrlParam(key, null)
}

// Получить все параметры как объект
export const getAllUrlParams = (): Record<string, string | string[]> => {
  if (typeof window === 'undefined') return {}
  
  const params = new URLSearchParams(window.location.search)
  const result: Record<string, string | string[]> = {}
  
  params.forEach((value, key) => {
    if (result[key]) {
      if (Array.isArray(result[key])) {
        (result[key] as string[]).push(value)
      } else {
        result[key] = [result[key] as string, value]
      }
    } else {
      result[key] = value
    }
  })
  
  return result
}

// Очистить все параметры
export const clearUrlParams = () => {
  if (typeof window === 'undefined') return
  window.history.replaceState({}, '', window.location.pathname)
}